<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info</title>
  <link rel="stylesheet" type="text/css" href="gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue">top level</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">16.9&nbsp;%</td>
            <td class="headerCovTableEntry">1347</td>
            <td class="headerCovTableEntry">227</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-07-03 12:38:23</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <center>
          <table width="80%" cellpadding=1 cellspacing=1 border=0>

            <tr>
              <td width="40%"><br></td>
            <td width="15%"></td>
            <td width="15%"></td>
            <td width="15%"></td>
            <td width="15%"></td>
            </tr>

            <tr>
              <td class="tableHead" rowspan=2>Directory <span  title="Click to sort table by file name" class="tableHeadSort"><img src="glass.png" width=10 height=14 alt="Sort by file name" title="Click to sort table by file name" border=0></span></td>
        <td class="tableHead" colspan=4>Line Coverage <span  title="Click to sort table by line coverage" class="tableHeadSort"><a href="index-sort-l.html"><img src="updown.png" width=10 height=14 alt="Sort by line coverage" title="Click to sort table by line coverage" border=0></a></span></td>
            </tr>
            <tr>
                    <td class="tableHead" colspan=2> Rate</td>
                    <td class="tableHead"> Total</td>
                    <td class="tableHead"> Hit</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/index.html" title="Click to go to directory /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Metering/lib">lib/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">1</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/bloc/auth/index.html" title="Click to go to directory /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Metering/lib/bloc/auth">lib/bloc/auth/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="ruby.png" width=1 height=10 alt="0.9%"><img src="snow.png" width=99 height=10 alt="0.9%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.9&nbsp;%</td>
              <td class="coverNumDflt">107</td>
              <td class="coverNumDflt">1</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/core/error/index.html" title="Click to go to directory /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Metering/lib/core/error">lib/core/error/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="ruby.png" width=5 height=10 alt="5.0%"><img src="snow.png" width=95 height=10 alt="5.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">5.0&nbsp;%</td>
              <td class="coverNumDflt">240</td>
              <td class="coverNumDflt">12</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/data/datasources/index.html" title="Click to go to directory /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Metering/lib/data/datasources">lib/data/datasources/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">180</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/data/models/index.html" title="Click to go to directory /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Metering/lib/data/models">lib/data/models/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">147</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/data/repositories/index.html" title="Click to go to directory /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Metering/lib/data/repositories">lib/data/repositories/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">97</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/domain/entities/index.html" title="Click to go to directory /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Metering/lib/domain/entities">lib/domain/entities/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">174</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/domain/services/index.html" title="Click to go to directory /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Metering/lib/domain/services">lib/domain/services/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="ruby.png" width=17 height=10 alt="16.7%"><img src="snow.png" width=83 height=10 alt="16.7%"></td></tr></table>
              </td>
              <td class="coverPerLo">16.7&nbsp;%</td>
              <td class="coverNumDflt">12</td>
              <td class="coverNumDflt">2</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/domain/usecases/index.html" title="Click to go to directory /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Metering/lib/domain/usecases">lib/domain/usecases/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">72</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/presentation/widgets/auth/index.html" title="Click to go to directory /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Metering/lib/presentation/widgets/auth">lib/presentation/widgets/auth/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="emerald.png" width=90 height=10 alt="90.0%"><img src="snow.png" width=10 height=10 alt="90.0%"></td></tr></table>
              </td>
              <td class="coverPerHi">90.0&nbsp;%</td>
              <td class="coverNumDflt">90</td>
              <td class="coverNumDflt">81</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/theme/index.html" title="Click to go to directory /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Metering/lib/theme">lib/theme/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="ruby.png" width=4 height=10 alt="3.7%"><img src="snow.png" width=96 height=10 alt="3.7%"></td></tr></table>
              </td>
              <td class="coverPerLo">3.7&nbsp;%</td>
              <td class="coverNumDflt">27</td>
              <td class="coverNumDflt">1</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/utils/index.html" title="Click to go to directory /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Metering/lib/utils">lib/utils/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="ruby.png" width=39 height=10 alt="38.8%"><img src="snow.png" width=61 height=10 alt="38.8%"></td></tr></table>
              </td>
              <td class="coverPerLo">38.8&nbsp;%</td>
              <td class="coverNumDflt">80</td>
              <td class="coverNumDflt">31</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/view_model/index.html" title="Click to go to directory /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Metering/lib/view_model">lib/view_model/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">8</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/views/widgets/containers/index.html" title="Click to go to directory /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Metering/lib/views/widgets/containers">lib/views/widgets/containers/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="amber.png" width=88 height=10 alt="88.4%"><img src="snow.png" width=12 height=10 alt="88.4%"></td></tr></table>
              </td>
              <td class="coverPerMed">88.4&nbsp;%</td>
              <td class="coverNumDflt">112</td>
              <td class="coverNumDflt">99</td>
            </tr>
        <tr>
          <td class="footnote" colspan=5>Note:  'Function Coverage' columns elided as function owner is not identified.</td>
         </tr>
          </table>
          </center>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
