<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/core/error/error_mapper.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/core/error">lib/core/error</a> - error_mapper.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">124</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-07-03 12:38:23</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'auth_exceptions.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'auth_failures.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import '../../view_model/custom_exception.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : /// Utility class for mapping data layer exceptions to domain layer failures</span>
<span id="L6"><span class="lineNum">       6</span>              : /// </span>
<span id="L7"><span class="lineNum">       7</span>              : /// This class provides a centralized way to convert technical exceptions</span>
<span id="L8"><span class="lineNum">       8</span>              : /// from the data layer into user-friendly failures for the domain layer.</span>
<span id="L9"><span class="lineNum">       9</span>              : /// It ensures consistent error handling across the authentication module.</span>
<span id="L10"><span class="lineNum">      10</span>              : class ErrorMapper {</span>
<span id="L11"><span class="lineNum">      11</span>              :   /// Maps an exception to an appropriate authentication failure</span>
<span id="L12"><span class="lineNum">      12</span>              :   /// </span>
<span id="L13"><span class="lineNum">      13</span>              :   /// This method handles the conversion of various exception types to</span>
<span id="L14"><span class="lineNum">      14</span>              :   /// corresponding failure types, providing user-friendly error messages</span>
<span id="L15"><span class="lineNum">      15</span>              :   /// while preserving technical details for debugging.</span>
<span id="L16"><span class="lineNum">      16</span>              :   /// </span>
<span id="L17"><span class="lineNum">      17</span>              :   /// Parameters:</span>
<span id="L18"><span class="lineNum">      18</span>              :   /// - [exception]: The exception to map to a failure</span>
<span id="L19"><span class="lineNum">      19</span>              :   /// </span>
<span id="L20"><span class="lineNum">      20</span>              :   /// Returns: An [AuthFailure] appropriate for the given exception</span>
<span id="L21"><span class="lineNum">      21</span> <span class="tlaUNC">           0 :   static AuthFailure mapExceptionToFailure(dynamic exception) {</span></span>
<span id="L22"><span class="lineNum">      22</span>              :     // Handle authentication-specific exceptions</span>
<span id="L23"><span class="lineNum">      23</span> <span class="tlaUNC">           0 :     if (exception is AuthException) {</span></span>
<span id="L24"><span class="lineNum">      24</span> <span class="tlaUNC">           0 :       return _mapAuthException(exception);</span></span>
<span id="L25"><span class="lineNum">      25</span>              :     }</span>
<span id="L26"><span class="lineNum">      26</span>              :     </span>
<span id="L27"><span class="lineNum">      27</span>              :     // Handle legacy CustomException</span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaUNC">           0 :     if (exception is CustomException) {</span></span>
<span id="L29"><span class="lineNum">      29</span> <span class="tlaUNC">           0 :       return _mapCustomException(exception);</span></span>
<span id="L30"><span class="lineNum">      30</span>              :     }</span>
<span id="L31"><span class="lineNum">      31</span>              :     </span>
<span id="L32"><span class="lineNum">      32</span>              :     // Handle common Dart exceptions</span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaUNC">           0 :     if (exception is FormatException) {</span></span>
<span id="L34"><span class="lineNum">      34</span>              :       return const ValidationFailure(</span>
<span id="L35"><span class="lineNum">      35</span>              :         field: 'input',</span>
<span id="L36"><span class="lineNum">      36</span>              :         message: 'Invalid input format. Please check your data and try again',</span>
<span id="L37"><span class="lineNum">      37</span>              :       );</span>
<span id="L38"><span class="lineNum">      38</span>              :     }</span>
<span id="L39"><span class="lineNum">      39</span>              :     </span>
<span id="L40"><span class="lineNum">      40</span>              :     // Handle timeout exceptions</span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaUNC">           0 :     if (exception.toString().toLowerCase().contains('timeout')) {</span></span>
<span id="L42"><span class="lineNum">      42</span>              :       return const TimeoutFailure();</span>
<span id="L43"><span class="lineNum">      43</span>              :     }</span>
<span id="L44"><span class="lineNum">      44</span>              :     </span>
<span id="L45"><span class="lineNum">      45</span>              :     // Handle network-related exceptions</span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :     if (_isNetworkException(exception)) {</span></span>
<span id="L47"><span class="lineNum">      47</span>              :       return const NetworkFailure();</span>
<span id="L48"><span class="lineNum">      48</span>              :     }</span>
<span id="L49"><span class="lineNum">      49</span>              :     </span>
<span id="L50"><span class="lineNum">      50</span>              :     // Default to unknown failure for unhandled exceptions</span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaUNC">           0 :     return UnknownFailure(</span></span>
<span id="L52"><span class="lineNum">      52</span>              :       message: 'An unexpected error occurred. Please try again',</span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaUNC">           0 :       context: {'originalException': exception.toString()},</span></span>
<span id="L54"><span class="lineNum">      54</span>              :     );</span>
<span id="L55"><span class="lineNum">      55</span>              :   }</span>
<span id="L56"><span class="lineNum">      56</span>              : </span>
<span id="L57"><span class="lineNum">      57</span>              :   /// Maps authentication-specific exceptions to failures</span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaUNC">           0 :   static AuthFailure _mapAuthException(AuthException exception) {</span></span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaUNC">           0 :     switch (exception.runtimeType) {</span></span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaUNC">           0 :       case NetworkException:</span></span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaUNC">           0 :         return NetworkFailure(</span></span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaUNC">           0 :           message: exception.message,</span></span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaUNC">           0 :           context: exception.context,</span></span>
<span id="L64"><span class="lineNum">      64</span>              :         );</span>
<span id="L65"><span class="lineNum">      65</span>              :         </span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaUNC">           0 :       case ServerException:</span></span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :         return ServerFailure(</span></span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaUNC">           0 :           message: exception.message,</span></span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :           context: exception.context,</span></span>
<span id="L70"><span class="lineNum">      70</span>              :         );</span>
<span id="L71"><span class="lineNum">      71</span>              :         </span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaUNC">           0 :       case InvalidCredentialsException:</span></span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :         return InvalidCredentialsFailure(</span></span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaUNC">           0 :           message: exception.message,</span></span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaUNC">           0 :           context: exception.context,</span></span>
<span id="L76"><span class="lineNum">      76</span>              :         );</span>
<span id="L77"><span class="lineNum">      77</span>              :         </span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :       case AccountNotVerifiedException:</span></span>
<span id="L79"><span class="lineNum">      79</span>              :         final accountException = exception as AccountNotVerifiedException;</span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaUNC">           0 :         return AccountNotVerifiedFailure(</span></span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaUNC">           0 :           verificationType: accountException.verificationType,</span></span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaUNC">           0 :           message: exception.message,</span></span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaUNC">           0 :           context: exception.context,</span></span>
<span id="L84"><span class="lineNum">      84</span>              :         );</span>
<span id="L85"><span class="lineNum">      85</span>              :         </span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaUNC">           0 :       case InvalidTwoFactorCodeException:</span></span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaUNC">           0 :         return InvalidTwoFactorCodeFailure(</span></span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaUNC">           0 :           message: exception.message,</span></span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaUNC">           0 :           context: exception.context,</span></span>
<span id="L90"><span class="lineNum">      90</span>              :         );</span>
<span id="L91"><span class="lineNum">      91</span>              :         </span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaUNC">           0 :       case TwoFactorCodeExpiredException:</span></span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaUNC">           0 :         return TwoFactorCodeExpiredFailure(</span></span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaUNC">           0 :           message: exception.message,</span></span>
<span id="L95"><span class="lineNum">      95</span> <span class="tlaUNC">           0 :           context: exception.context,</span></span>
<span id="L96"><span class="lineNum">      96</span>              :         );</span>
<span id="L97"><span class="lineNum">      97</span>              :         </span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaUNC">           0 :       case BiometricException:</span></span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaUNC">           0 :         return _mapBiometricException(exception as BiometricException);</span></span>
<span id="L100"><span class="lineNum">     100</span>              :         </span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaUNC">           0 :       case InvalidTokenException:</span></span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaUNC">           0 :       case RefreshTokenExpiredException:</span></span>
<span id="L103"><span class="lineNum">     103</span> <span class="tlaUNC">           0 :         return SessionExpiredFailure(</span></span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaUNC">           0 :           message: exception.message,</span></span>
<span id="L105"><span class="lineNum">     105</span> <span class="tlaUNC">           0 :           context: exception.context,</span></span>
<span id="L106"><span class="lineNum">     106</span>              :         );</span>
<span id="L107"><span class="lineNum">     107</span>              :         </span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaUNC">           0 :       case TokenException:</span></span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaUNC">           0 :         return TokenFailure(</span></span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaUNC">           0 :           message: exception.message,</span></span>
<span id="L111"><span class="lineNum">     111</span> <span class="tlaUNC">           0 :           context: exception.context,</span></span>
<span id="L112"><span class="lineNum">     112</span>              :         );</span>
<span id="L113"><span class="lineNum">     113</span>              :         </span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaUNC">           0 :       case InvalidEmailFormatException:</span></span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaUNC">           0 :         return InvalidEmailFailure(</span></span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaUNC">           0 :           message: exception.message,</span></span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaUNC">           0 :           context: exception.context,</span></span>
<span id="L118"><span class="lineNum">     118</span>              :         );</span>
<span id="L119"><span class="lineNum">     119</span>              :         </span>
<span id="L120"><span class="lineNum">     120</span> <span class="tlaUNC">           0 :       case InvalidPasswordFormatException:</span></span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaUNC">           0 :         return InvalidPasswordFailure(</span></span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaUNC">           0 :           message: exception.message,</span></span>
<span id="L123"><span class="lineNum">     123</span> <span class="tlaUNC">           0 :           context: exception.context,</span></span>
<span id="L124"><span class="lineNum">     124</span>              :         );</span>
<span id="L125"><span class="lineNum">     125</span>              :         </span>
<span id="L126"><span class="lineNum">     126</span> <span class="tlaUNC">           0 :       case ValidationException:</span></span>
<span id="L127"><span class="lineNum">     127</span>              :         final validationException = exception as ValidationException;</span>
<span id="L128"><span class="lineNum">     128</span> <span class="tlaUNC">           0 :         return ValidationFailure(</span></span>
<span id="L129"><span class="lineNum">     129</span> <span class="tlaUNC">           0 :           field: validationException.field,</span></span>
<span id="L130"><span class="lineNum">     130</span> <span class="tlaUNC">           0 :           message: exception.message,</span></span>
<span id="L131"><span class="lineNum">     131</span> <span class="tlaUNC">           0 :           context: exception.context,</span></span>
<span id="L132"><span class="lineNum">     132</span>              :         );</span>
<span id="L133"><span class="lineNum">     133</span>              :         </span>
<span id="L134"><span class="lineNum">     134</span> <span class="tlaUNC">           0 :       case TimeoutException:</span></span>
<span id="L135"><span class="lineNum">     135</span> <span class="tlaUNC">           0 :         return TimeoutFailure(</span></span>
<span id="L136"><span class="lineNum">     136</span> <span class="tlaUNC">           0 :           message: exception.message,</span></span>
<span id="L137"><span class="lineNum">     137</span> <span class="tlaUNC">           0 :           context: exception.context,</span></span>
<span id="L138"><span class="lineNum">     138</span>              :         );</span>
<span id="L139"><span class="lineNum">     139</span>              :         </span>
<span id="L140"><span class="lineNum">     140</span>              :       default:</span>
<span id="L141"><span class="lineNum">     141</span> <span class="tlaUNC">           0 :         return UnknownFailure(</span></span>
<span id="L142"><span class="lineNum">     142</span> <span class="tlaUNC">           0 :           message: exception.message,</span></span>
<span id="L143"><span class="lineNum">     143</span> <span class="tlaUNC">           0 :           context: exception.context,</span></span>
<span id="L144"><span class="lineNum">     144</span>              :         );</span>
<span id="L145"><span class="lineNum">     145</span>              :     }</span>
<span id="L146"><span class="lineNum">     146</span>              :   }</span>
<span id="L147"><span class="lineNum">     147</span>              : </span>
<span id="L148"><span class="lineNum">     148</span>              :   /// Maps biometric exceptions to appropriate failures</span>
<span id="L149"><span class="lineNum">     149</span> <span class="tlaUNC">           0 :   static AuthFailure _mapBiometricException(BiometricException exception) {</span></span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaUNC">           0 :     switch (exception.failureType) {</span></span>
<span id="L151"><span class="lineNum">     151</span> <span class="tlaUNC">           0 :       case BiometricFailureType.notAvailable:</span></span>
<span id="L152"><span class="lineNum">     152</span> <span class="tlaUNC">           0 :         return BiometricNotAvailableFailure(</span></span>
<span id="L153"><span class="lineNum">     153</span> <span class="tlaUNC">           0 :           message: exception.message,</span></span>
<span id="L154"><span class="lineNum">     154</span> <span class="tlaUNC">           0 :           context: exception.context,</span></span>
<span id="L155"><span class="lineNum">     155</span>              :         );</span>
<span id="L156"><span class="lineNum">     156</span>              :         </span>
<span id="L157"><span class="lineNum">     157</span> <span class="tlaUNC">           0 :       case BiometricFailureType.notEnrolled:</span></span>
<span id="L158"><span class="lineNum">     158</span> <span class="tlaUNC">           0 :         return BiometricNotEnrolledFailure(</span></span>
<span id="L159"><span class="lineNum">     159</span> <span class="tlaUNC">           0 :           message: exception.message,</span></span>
<span id="L160"><span class="lineNum">     160</span> <span class="tlaUNC">           0 :           context: exception.context,</span></span>
<span id="L161"><span class="lineNum">     161</span>              :         );</span>
<span id="L162"><span class="lineNum">     162</span>              :         </span>
<span id="L163"><span class="lineNum">     163</span> <span class="tlaUNC">           0 :       case BiometricFailureType.authenticationFailed:</span></span>
<span id="L164"><span class="lineNum">     164</span> <span class="tlaUNC">           0 :         return BiometricAuthenticationFailure(</span></span>
<span id="L165"><span class="lineNum">     165</span> <span class="tlaUNC">           0 :           message: exception.message,</span></span>
<span id="L166"><span class="lineNum">     166</span> <span class="tlaUNC">           0 :           context: exception.context,</span></span>
<span id="L167"><span class="lineNum">     167</span>              :         );</span>
<span id="L168"><span class="lineNum">     168</span>              :         </span>
<span id="L169"><span class="lineNum">     169</span> <span class="tlaUNC">           0 :       case BiometricFailureType.userCancelled:</span></span>
<span id="L170"><span class="lineNum">     170</span> <span class="tlaUNC">           0 :         return BiometricCancelledFailure(</span></span>
<span id="L171"><span class="lineNum">     171</span> <span class="tlaUNC">           0 :           message: exception.message,</span></span>
<span id="L172"><span class="lineNum">     172</span> <span class="tlaUNC">           0 :           context: exception.context,</span></span>
<span id="L173"><span class="lineNum">     173</span>              :         );</span>
<span id="L174"><span class="lineNum">     174</span>              :         </span>
<span id="L175"><span class="lineNum">     175</span> <span class="tlaUNC">           0 :       case BiometricFailureType.lockedOut:</span></span>
<span id="L176"><span class="lineNum">     176</span> <span class="tlaUNC">           0 :         return BiometricLockedFailure(</span></span>
<span id="L177"><span class="lineNum">     177</span>              :           isPermanent: false,</span>
<span id="L178"><span class="lineNum">     178</span> <span class="tlaUNC">           0 :           message: exception.message,</span></span>
<span id="L179"><span class="lineNum">     179</span> <span class="tlaUNC">           0 :           context: exception.context,</span></span>
<span id="L180"><span class="lineNum">     180</span>              :         );</span>
<span id="L181"><span class="lineNum">     181</span>              :         </span>
<span id="L182"><span class="lineNum">     182</span> <span class="tlaUNC">           0 :       case BiometricFailureType.permanentlyLockedOut:</span></span>
<span id="L183"><span class="lineNum">     183</span> <span class="tlaUNC">           0 :         return BiometricLockedFailure(</span></span>
<span id="L184"><span class="lineNum">     184</span>              :           isPermanent: true,</span>
<span id="L185"><span class="lineNum">     185</span> <span class="tlaUNC">           0 :           message: exception.message,</span></span>
<span id="L186"><span class="lineNum">     186</span> <span class="tlaUNC">           0 :           context: exception.context,</span></span>
<span id="L187"><span class="lineNum">     187</span>              :         );</span>
<span id="L188"><span class="lineNum">     188</span>              :         </span>
<span id="L189"><span class="lineNum">     189</span> <span class="tlaUNC">           0 :       case BiometricFailureType.noStoredCredentials:</span></span>
<span id="L190"><span class="lineNum">     190</span> <span class="tlaUNC">           0 :         return NoStoredCredentialsFailure(</span></span>
<span id="L191"><span class="lineNum">     191</span> <span class="tlaUNC">           0 :           message: exception.message,</span></span>
<span id="L192"><span class="lineNum">     192</span> <span class="tlaUNC">           0 :           context: exception.context,</span></span>
<span id="L193"><span class="lineNum">     193</span>              :         );</span>
<span id="L194"><span class="lineNum">     194</span>              :     }</span>
<span id="L195"><span class="lineNum">     195</span>              :   }</span>
<span id="L196"><span class="lineNum">     196</span>              : </span>
<span id="L197"><span class="lineNum">     197</span>              :   /// Maps legacy CustomException to appropriate failures</span>
<span id="L198"><span class="lineNum">     198</span> <span class="tlaUNC">           0 :   static AuthFailure _mapCustomException(CustomException exception) {</span></span>
<span id="L199"><span class="lineNum">     199</span> <span class="tlaUNC">           0 :     final message = exception.toString();</span></span>
<span id="L200"><span class="lineNum">     200</span> <span class="tlaUNC">           0 :     final lowerMessage = message.toLowerCase();</span></span>
<span id="L201"><span class="lineNum">     201</span>              :     </span>
<span id="L202"><span class="lineNum">     202</span>              :     // Map based on common error messages</span>
<span id="L203"><span class="lineNum">     203</span> <span class="tlaUNC">           0 :     if (lowerMessage.contains('incorrect email or password') ||</span></span>
<span id="L204"><span class="lineNum">     204</span> <span class="tlaUNC">           0 :         lowerMessage.contains('invalid credentials')) {</span></span>
<span id="L205"><span class="lineNum">     205</span>              :       return const InvalidCredentialsFailure();</span>
<span id="L206"><span class="lineNum">     206</span>              :     }</span>
<span id="L207"><span class="lineNum">     207</span>              :     </span>
<span id="L208"><span class="lineNum">     208</span> <span class="tlaUNC">           0 :     if (lowerMessage.contains('email') &amp;&amp; lowerMessage.contains('unverified')) {</span></span>
<span id="L209"><span class="lineNum">     209</span>              :       return const AccountNotVerifiedFailure(verificationType: 'email');</span>
<span id="L210"><span class="lineNum">     210</span>              :     }</span>
<span id="L211"><span class="lineNum">     211</span>              :     </span>
<span id="L212"><span class="lineNum">     212</span> <span class="tlaUNC">           0 :     if (lowerMessage.contains('phone') &amp;&amp; lowerMessage.contains('unverified')) {</span></span>
<span id="L213"><span class="lineNum">     213</span>              :       return const AccountNotVerifiedFailure(verificationType: 'phone');</span>
<span id="L214"><span class="lineNum">     214</span>              :     }</span>
<span id="L215"><span class="lineNum">     215</span>              :     </span>
<span id="L216"><span class="lineNum">     216</span> <span class="tlaUNC">           0 :     if (lowerMessage.contains('no internet') || </span></span>
<span id="L217"><span class="lineNum">     217</span> <span class="tlaUNC">           0 :         lowerMessage.contains('network') ||</span></span>
<span id="L218"><span class="lineNum">     218</span> <span class="tlaUNC">           0 :         lowerMessage.contains('connection')) {</span></span>
<span id="L219"><span class="lineNum">     219</span>              :       return const NetworkFailure();</span>
<span id="L220"><span class="lineNum">     220</span>              :     }</span>
<span id="L221"><span class="lineNum">     221</span>              :     </span>
<span id="L222"><span class="lineNum">     222</span> <span class="tlaUNC">           0 :     if (lowerMessage.contains('server') || </span></span>
<span id="L223"><span class="lineNum">     223</span> <span class="tlaUNC">           0 :         lowerMessage.contains('http')) {</span></span>
<span id="L224"><span class="lineNum">     224</span>              :       return const ServerFailure();</span>
<span id="L225"><span class="lineNum">     225</span>              :     }</span>
<span id="L226"><span class="lineNum">     226</span>              :     </span>
<span id="L227"><span class="lineNum">     227</span> <span class="tlaUNC">           0 :     if (lowerMessage.contains('timeout')) {</span></span>
<span id="L228"><span class="lineNum">     228</span>              :       return const TimeoutFailure();</span>
<span id="L229"><span class="lineNum">     229</span>              :     }</span>
<span id="L230"><span class="lineNum">     230</span>              :     </span>
<span id="L231"><span class="lineNum">     231</span> <span class="tlaUNC">           0 :     if (lowerMessage.contains('biometric')) {</span></span>
<span id="L232"><span class="lineNum">     232</span>              :       return const BiometricAuthenticationFailure();</span>
<span id="L233"><span class="lineNum">     233</span>              :     }</span>
<span id="L234"><span class="lineNum">     234</span>              :     </span>
<span id="L235"><span class="lineNum">     235</span> <span class="tlaUNC">           0 :     if (lowerMessage.contains('two-factor') || </span></span>
<span id="L236"><span class="lineNum">     236</span> <span class="tlaUNC">           0 :         lowerMessage.contains('2fa') ||</span></span>
<span id="L237"><span class="lineNum">     237</span> <span class="tlaUNC">           0 :         lowerMessage.contains('verification code')) {</span></span>
<span id="L238"><span class="lineNum">     238</span>              :       return const InvalidTwoFactorCodeFailure();</span>
<span id="L239"><span class="lineNum">     239</span>              :     }</span>
<span id="L240"><span class="lineNum">     240</span>              :     </span>
<span id="L241"><span class="lineNum">     241</span>              :     // Default mapping for CustomException</span>
<span id="L242"><span class="lineNum">     242</span> <span class="tlaUNC">           0 :     return UnknownFailure(</span></span>
<span id="L243"><span class="lineNum">     243</span>              :       message: 'An error occurred. Please try again',</span>
<span id="L244"><span class="lineNum">     244</span> <span class="tlaUNC">           0 :       context: {'originalMessage': message},</span></span>
<span id="L245"><span class="lineNum">     245</span>              :     );</span>
<span id="L246"><span class="lineNum">     246</span>              :   }</span>
<span id="L247"><span class="lineNum">     247</span>              : </span>
<span id="L248"><span class="lineNum">     248</span>              :   /// Checks if an exception is network-related</span>
<span id="L249"><span class="lineNum">     249</span> <span class="tlaUNC">           0 :   static bool _isNetworkException(dynamic exception) {</span></span>
<span id="L250"><span class="lineNum">     250</span> <span class="tlaUNC">           0 :     final exceptionString = exception.toString().toLowerCase();</span></span>
<span id="L251"><span class="lineNum">     251</span> <span class="tlaUNC">           0 :     return exceptionString.contains('socketexception') ||</span></span>
<span id="L252"><span class="lineNum">     252</span> <span class="tlaUNC">           0 :            exceptionString.contains('handshakeexception') ||</span></span>
<span id="L253"><span class="lineNum">     253</span> <span class="tlaUNC">           0 :            exceptionString.contains('connection') ||</span></span>
<span id="L254"><span class="lineNum">     254</span> <span class="tlaUNC">           0 :            exceptionString.contains('network') ||</span></span>
<span id="L255"><span class="lineNum">     255</span> <span class="tlaUNC">           0 :            exceptionString.contains('no internet');</span></span>
<span id="L256"><span class="lineNum">     256</span>              :   }</span>
<span id="L257"><span class="lineNum">     257</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
