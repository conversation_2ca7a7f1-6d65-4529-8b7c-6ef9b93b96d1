<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/core/error/auth_failures.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/core/error">lib/core/error</a> - auth_failures.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">18.2&nbsp;%</td>
            <td class="headerCovTableEntry">66</td>
            <td class="headerCovTableEntry">12</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-07-03 12:38:23</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:equatable/equatable.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : /// Authentication-specific failures for the domain layer</span>
<span id="L4"><span class="lineNum">       4</span>              : ///</span>
<span id="L5"><span class="lineNum">       5</span>              : /// These failures represent business logic errors that occur during</span>
<span id="L6"><span class="lineNum">       6</span>              : /// authentication operations. They are returned by repository methods</span>
<span id="L7"><span class="lineNum">       7</span>              : /// and handled by use cases and BLoCs.</span>
<span id="L8"><span class="lineNum">       8</span>              : ///</span>
<span id="L9"><span class="lineNum">       9</span>              : /// All authentication failures extend [AuthFailure] for consistent handling.</span>
<span id="L10"><span class="lineNum">      10</span>              : </span>
<span id="L11"><span class="lineNum">      11</span>              : /// Base class for all authentication-related failures</span>
<span id="L12"><span class="lineNum">      12</span>              : ///</span>
<span id="L13"><span class="lineNum">      13</span>              : /// This abstract class provides a common interface for all authentication</span>
<span id="L14"><span class="lineNum">      14</span>              : /// failures and includes error codes for programmatic handling.</span>
<span id="L15"><span class="lineNum">      15</span>              : abstract class AuthFailure extends Equatable {</span>
<span id="L16"><span class="lineNum">      16</span>              :   /// Human-readable error message for users</span>
<span id="L17"><span class="lineNum">      17</span>              :   final String message;</span>
<span id="L18"><span class="lineNum">      18</span>              : </span>
<span id="L19"><span class="lineNum">      19</span>              :   /// Error code for programmatic handling</span>
<span id="L20"><span class="lineNum">      20</span>              :   final String code;</span>
<span id="L21"><span class="lineNum">      21</span>              : </span>
<span id="L22"><span class="lineNum">      22</span>              :   /// Additional context information</span>
<span id="L23"><span class="lineNum">      23</span>              :   final Map&lt;String, dynamic&gt;? context;</span>
<span id="L24"><span class="lineNum">      24</span>              : </span>
<span id="L25"><span class="lineNum">      25</span> <span class="tlaGNC">           3 :   const AuthFailure({</span></span>
<span id="L26"><span class="lineNum">      26</span>              :     required this.message,</span>
<span id="L27"><span class="lineNum">      27</span>              :     required this.code,</span>
<span id="L28"><span class="lineNum">      28</span>              :     this.context,</span>
<span id="L29"><span class="lineNum">      29</span>              :   });</span>
<span id="L30"><span class="lineNum">      30</span>              : </span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaUNC">           0 :   List&lt;Object?&gt; get props =&gt; [message, code, context];</span></span>
<span id="L33"><span class="lineNum">      33</span>              : </span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaUNC">           0 :   String toString() =&gt; 'AuthFailure($code): $message';</span></span>
<span id="L36"><span class="lineNum">      36</span>              : }</span>
<span id="L37"><span class="lineNum">      37</span>              : </span>
<span id="L38"><span class="lineNum">      38</span>              : /// Failure when network connectivity issues occur</span>
<span id="L39"><span class="lineNum">      39</span>              : class NetworkFailure extends AuthFailure {</span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaGNC">           1 :   const NetworkFailure({</span></span>
<span id="L41"><span class="lineNum">      41</span>              :     String message = 'Please check your internet connection and try again',</span>
<span id="L42"><span class="lineNum">      42</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L44"><span class="lineNum">      44</span>              :           message: message,</span>
<span id="L45"><span class="lineNum">      45</span>              :           code: 'NETWORK_FAILURE',</span>
<span id="L46"><span class="lineNum">      46</span>              :           context: context,</span>
<span id="L47"><span class="lineNum">      47</span>              :         );</span>
<span id="L48"><span class="lineNum">      48</span>              : }</span>
<span id="L49"><span class="lineNum">      49</span>              : </span>
<span id="L50"><span class="lineNum">      50</span>              : /// Failure when server is unavailable or returns an error</span>
<span id="L51"><span class="lineNum">      51</span>              : class ServerFailure extends AuthFailure {</span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaGNC">           1 :   const ServerFailure({</span></span>
<span id="L53"><span class="lineNum">      53</span>              :     String message =</span>
<span id="L54"><span class="lineNum">      54</span>              :         'Server is temporarily unavailable. Please try again later',</span>
<span id="L55"><span class="lineNum">      55</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L57"><span class="lineNum">      57</span>              :           message: message,</span>
<span id="L58"><span class="lineNum">      58</span>              :           code: 'SERVER_FAILURE',</span>
<span id="L59"><span class="lineNum">      59</span>              :           context: context,</span>
<span id="L60"><span class="lineNum">      60</span>              :         );</span>
<span id="L61"><span class="lineNum">      61</span>              : }</span>
<span id="L62"><span class="lineNum">      62</span>              : </span>
<span id="L63"><span class="lineNum">      63</span>              : /// Failure when authentication credentials are invalid</span>
<span id="L64"><span class="lineNum">      64</span>              : class InvalidCredentialsFailure extends AuthFailure {</span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaGNC">           1 :   const InvalidCredentialsFailure({</span></span>
<span id="L66"><span class="lineNum">      66</span>              :     String message =</span>
<span id="L67"><span class="lineNum">      67</span>              :         'Invalid email or password. Please check your credentials and try again',</span>
<span id="L68"><span class="lineNum">      68</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L70"><span class="lineNum">      70</span>              :           message: message,</span>
<span id="L71"><span class="lineNum">      71</span>              :           code: 'INVALID_CREDENTIALS',</span>
<span id="L72"><span class="lineNum">      72</span>              :           context: context,</span>
<span id="L73"><span class="lineNum">      73</span>              :         );</span>
<span id="L74"><span class="lineNum">      74</span>              : }</span>
<span id="L75"><span class="lineNum">      75</span>              : </span>
<span id="L76"><span class="lineNum">      76</span>              : /// Failure when user account requires verification</span>
<span id="L77"><span class="lineNum">      77</span>              : class AccountNotVerifiedFailure extends AuthFailure {</span>
<span id="L78"><span class="lineNum">      78</span>              :   /// Type of verification required</span>
<span id="L79"><span class="lineNum">      79</span>              :   final String verificationType;</span>
<span id="L80"><span class="lineNum">      80</span>              : </span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaGNC">           1 :   const AccountNotVerifiedFailure({</span></span>
<span id="L82"><span class="lineNum">      82</span>              :     required this.verificationType,</span>
<span id="L83"><span class="lineNum">      83</span>              :     String? message,</span>
<span id="L84"><span class="lineNum">      84</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L86"><span class="lineNum">      86</span>              :           message: message ??</span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaUNC">           0 :               'Please verify your $verificationType before signing in',</span></span>
<span id="L88"><span class="lineNum">      88</span>              :           code: 'ACCOUNT_NOT_VERIFIED',</span>
<span id="L89"><span class="lineNum">      89</span>              :           context: context,</span>
<span id="L90"><span class="lineNum">      90</span>              :         );</span>
<span id="L91"><span class="lineNum">      91</span>              : </span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaUNC">           0 :   List&lt;Object?&gt; get props =&gt; [message, code, context, verificationType];</span></span>
<span id="L94"><span class="lineNum">      94</span>              : }</span>
<span id="L95"><span class="lineNum">      95</span>              : </span>
<span id="L96"><span class="lineNum">      96</span>              : /// Failure when two-factor authentication is required but not provided</span>
<span id="L97"><span class="lineNum">      97</span>              : class TwoFactorRequiredFailure extends AuthFailure {</span>
<span id="L98"><span class="lineNum">      98</span>              :   /// Reference code for two-factor authentication</span>
<span id="L99"><span class="lineNum">      99</span>              :   final String refCode;</span>
<span id="L100"><span class="lineNum">     100</span>              : </span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaUNC">           0 :   const TwoFactorRequiredFailure({</span></span>
<span id="L102"><span class="lineNum">     102</span>              :     required this.refCode,</span>
<span id="L103"><span class="lineNum">     103</span>              :     String message = 'Two-factor authentication is required',</span>
<span id="L104"><span class="lineNum">     104</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L105"><span class="lineNum">     105</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L106"><span class="lineNum">     106</span>              :           message: message,</span>
<span id="L107"><span class="lineNum">     107</span>              :           code: 'TWO_FACTOR_REQUIRED',</span>
<span id="L108"><span class="lineNum">     108</span>              :           context: context,</span>
<span id="L109"><span class="lineNum">     109</span>              :         );</span>
<span id="L110"><span class="lineNum">     110</span>              : </span>
<span id="L111"><span class="lineNum">     111</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L112"><span class="lineNum">     112</span> <span class="tlaUNC">           0 :   List&lt;Object?&gt; get props =&gt; [message, code, context, refCode];</span></span>
<span id="L113"><span class="lineNum">     113</span>              : }</span>
<span id="L114"><span class="lineNum">     114</span>              : </span>
<span id="L115"><span class="lineNum">     115</span>              : /// Failure when two-factor authentication code is invalid</span>
<span id="L116"><span class="lineNum">     116</span>              : class InvalidTwoFactorCodeFailure extends AuthFailure {</span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaGNC">           1 :   const InvalidTwoFactorCodeFailure({</span></span>
<span id="L118"><span class="lineNum">     118</span>              :     String message =</span>
<span id="L119"><span class="lineNum">     119</span>              :         'Invalid verification code. Please check the code and try again',</span>
<span id="L120"><span class="lineNum">     120</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L122"><span class="lineNum">     122</span>              :           message: message,</span>
<span id="L123"><span class="lineNum">     123</span>              :           code: 'INVALID_TWO_FACTOR_CODE',</span>
<span id="L124"><span class="lineNum">     124</span>              :           context: context,</span>
<span id="L125"><span class="lineNum">     125</span>              :         );</span>
<span id="L126"><span class="lineNum">     126</span>              : }</span>
<span id="L127"><span class="lineNum">     127</span>              : </span>
<span id="L128"><span class="lineNum">     128</span>              : /// Failure when two-factor authentication code has expired</span>
<span id="L129"><span class="lineNum">     129</span>              : class TwoFactorCodeExpiredFailure extends AuthFailure {</span>
<span id="L130"><span class="lineNum">     130</span> <span class="tlaUNC">           0 :   const TwoFactorCodeExpiredFailure({</span></span>
<span id="L131"><span class="lineNum">     131</span>              :     String message = 'Verification code has expired. Please request a new code',</span>
<span id="L132"><span class="lineNum">     132</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L133"><span class="lineNum">     133</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L134"><span class="lineNum">     134</span>              :           message: message,</span>
<span id="L135"><span class="lineNum">     135</span>              :           code: 'TWO_FACTOR_CODE_EXPIRED',</span>
<span id="L136"><span class="lineNum">     136</span>              :           context: context,</span>
<span id="L137"><span class="lineNum">     137</span>              :         );</span>
<span id="L138"><span class="lineNum">     138</span>              : }</span>
<span id="L139"><span class="lineNum">     139</span>              : </span>
<span id="L140"><span class="lineNum">     140</span>              : /// Failure when biometric authentication is not available</span>
<span id="L141"><span class="lineNum">     141</span>              : class BiometricNotAvailableFailure extends AuthFailure {</span>
<span id="L142"><span class="lineNum">     142</span> <span class="tlaGNC">           1 :   const BiometricNotAvailableFailure({</span></span>
<span id="L143"><span class="lineNum">     143</span>              :     String message = 'Biometric authentication is not available on this device',</span>
<span id="L144"><span class="lineNum">     144</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L145"><span class="lineNum">     145</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L146"><span class="lineNum">     146</span>              :           message: message,</span>
<span id="L147"><span class="lineNum">     147</span>              :           code: 'BIOMETRIC_NOT_AVAILABLE',</span>
<span id="L148"><span class="lineNum">     148</span>              :           context: context,</span>
<span id="L149"><span class="lineNum">     149</span>              :         );</span>
<span id="L150"><span class="lineNum">     150</span>              : }</span>
<span id="L151"><span class="lineNum">     151</span>              : </span>
<span id="L152"><span class="lineNum">     152</span>              : /// Failure when biometric authentication is not set up</span>
<span id="L153"><span class="lineNum">     153</span>              : class BiometricNotEnrolledFailure extends AuthFailure {</span>
<span id="L154"><span class="lineNum">     154</span> <span class="tlaGNC">           1 :   const BiometricNotEnrolledFailure({</span></span>
<span id="L155"><span class="lineNum">     155</span>              :     String message =</span>
<span id="L156"><span class="lineNum">     156</span>              :         'Please set up biometric authentication in your device settings',</span>
<span id="L157"><span class="lineNum">     157</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L158"><span class="lineNum">     158</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L159"><span class="lineNum">     159</span>              :           message: message,</span>
<span id="L160"><span class="lineNum">     160</span>              :           code: 'BIOMETRIC_NOT_ENROLLED',</span>
<span id="L161"><span class="lineNum">     161</span>              :           context: context,</span>
<span id="L162"><span class="lineNum">     162</span>              :         );</span>
<span id="L163"><span class="lineNum">     163</span>              : }</span>
<span id="L164"><span class="lineNum">     164</span>              : </span>
<span id="L165"><span class="lineNum">     165</span>              : /// Failure when biometric authentication fails</span>
<span id="L166"><span class="lineNum">     166</span>              : class BiometricAuthenticationFailure extends AuthFailure {</span>
<span id="L167"><span class="lineNum">     167</span> <span class="tlaGNC">           2 :   const BiometricAuthenticationFailure({</span></span>
<span id="L168"><span class="lineNum">     168</span>              :     String message = 'Biometric authentication failed. Please try again',</span>
<span id="L169"><span class="lineNum">     169</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L170"><span class="lineNum">     170</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L171"><span class="lineNum">     171</span>              :           message: message,</span>
<span id="L172"><span class="lineNum">     172</span>              :           code: 'BIOMETRIC_AUTHENTICATION_FAILED',</span>
<span id="L173"><span class="lineNum">     173</span>              :           context: context,</span>
<span id="L174"><span class="lineNum">     174</span>              :         );</span>
<span id="L175"><span class="lineNum">     175</span>              : }</span>
<span id="L176"><span class="lineNum">     176</span>              : </span>
<span id="L177"><span class="lineNum">     177</span>              : /// Failure when user cancels biometric authentication</span>
<span id="L178"><span class="lineNum">     178</span>              : class BiometricCancelledFailure extends AuthFailure {</span>
<span id="L179"><span class="lineNum">     179</span> <span class="tlaUNC">           0 :   const BiometricCancelledFailure({</span></span>
<span id="L180"><span class="lineNum">     180</span>              :     String message = 'Biometric authentication was cancelled',</span>
<span id="L181"><span class="lineNum">     181</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L182"><span class="lineNum">     182</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L183"><span class="lineNum">     183</span>              :           message: message,</span>
<span id="L184"><span class="lineNum">     184</span>              :           code: 'BIOMETRIC_CANCELLED',</span>
<span id="L185"><span class="lineNum">     185</span>              :           context: context,</span>
<span id="L186"><span class="lineNum">     186</span>              :         );</span>
<span id="L187"><span class="lineNum">     187</span>              : }</span>
<span id="L188"><span class="lineNum">     188</span>              : </span>
<span id="L189"><span class="lineNum">     189</span>              : /// Failure when biometric authentication is locked</span>
<span id="L190"><span class="lineNum">     190</span>              : class BiometricLockedFailure extends AuthFailure {</span>
<span id="L191"><span class="lineNum">     191</span>              :   /// Whether the lockout is permanent</span>
<span id="L192"><span class="lineNum">     192</span>              :   final bool isPermanent;</span>
<span id="L193"><span class="lineNum">     193</span>              : </span>
<span id="L194"><span class="lineNum">     194</span> <span class="tlaUNC">           0 :   const BiometricLockedFailure({</span></span>
<span id="L195"><span class="lineNum">     195</span>              :     this.isPermanent = false,</span>
<span id="L196"><span class="lineNum">     196</span>              :     String? message,</span>
<span id="L197"><span class="lineNum">     197</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L198"><span class="lineNum">     198</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L199"><span class="lineNum">     199</span>              :           message: message ??</span>
<span id="L200"><span class="lineNum">     200</span>              :               (isPermanent</span>
<span id="L201"><span class="lineNum">     201</span>              :                   ? 'Biometric authentication is permanently locked. Please use password login'</span>
<span id="L202"><span class="lineNum">     202</span>              :                   : 'Biometric authentication is temporarily locked. Please try again later'),</span>
<span id="L203"><span class="lineNum">     203</span>              :           code: isPermanent</span>
<span id="L204"><span class="lineNum">     204</span>              :               ? 'BIOMETRIC_PERMANENTLY_LOCKED'</span>
<span id="L205"><span class="lineNum">     205</span>              :               : 'BIOMETRIC_TEMPORARILY_LOCKED',</span>
<span id="L206"><span class="lineNum">     206</span>              :           context: context,</span>
<span id="L207"><span class="lineNum">     207</span>              :         );</span>
<span id="L208"><span class="lineNum">     208</span>              : </span>
<span id="L209"><span class="lineNum">     209</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L210"><span class="lineNum">     210</span> <span class="tlaUNC">           0 :   List&lt;Object?&gt; get props =&gt; [message, code, context, isPermanent];</span></span>
<span id="L211"><span class="lineNum">     211</span>              : }</span>
<span id="L212"><span class="lineNum">     212</span>              : </span>
<span id="L213"><span class="lineNum">     213</span>              : /// Failure when biometric system encounters an error</span>
<span id="L214"><span class="lineNum">     214</span>              : class BiometricSystemFailure extends AuthFailure {</span>
<span id="L215"><span class="lineNum">     215</span> <span class="tlaUNC">           0 :   const BiometricSystemFailure({</span></span>
<span id="L216"><span class="lineNum">     216</span>              :     String message =</span>
<span id="L217"><span class="lineNum">     217</span>              :         'Biometric system error. Please try again or use password login',</span>
<span id="L218"><span class="lineNum">     218</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L219"><span class="lineNum">     219</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L220"><span class="lineNum">     220</span>              :           message: message,</span>
<span id="L221"><span class="lineNum">     221</span>              :           code: 'BIOMETRIC_SYSTEM_ERROR',</span>
<span id="L222"><span class="lineNum">     222</span>              :           context: context,</span>
<span id="L223"><span class="lineNum">     223</span>              :         );</span>
<span id="L224"><span class="lineNum">     224</span>              : }</span>
<span id="L225"><span class="lineNum">     225</span>              : </span>
<span id="L226"><span class="lineNum">     226</span>              : /// Failure when no stored credentials are found for biometric authentication</span>
<span id="L227"><span class="lineNum">     227</span>              : class NoStoredCredentialsFailure extends AuthFailure {</span>
<span id="L228"><span class="lineNum">     228</span> <span class="tlaGNC">           1 :   const NoStoredCredentialsFailure({</span></span>
<span id="L229"><span class="lineNum">     229</span>              :     String message =</span>
<span id="L230"><span class="lineNum">     230</span>              :         'No stored credentials found. Please sign in with your password first',</span>
<span id="L231"><span class="lineNum">     231</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L232"><span class="lineNum">     232</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L233"><span class="lineNum">     233</span>              :           message: message,</span>
<span id="L234"><span class="lineNum">     234</span>              :           code: 'NO_STORED_CREDENTIALS',</span>
<span id="L235"><span class="lineNum">     235</span>              :           context: context,</span>
<span id="L236"><span class="lineNum">     236</span>              :         );</span>
<span id="L237"><span class="lineNum">     237</span>              : }</span>
<span id="L238"><span class="lineNum">     238</span>              : </span>
<span id="L239"><span class="lineNum">     239</span>              : /// Failure when token operations fail</span>
<span id="L240"><span class="lineNum">     240</span>              : class TokenFailure extends AuthFailure {</span>
<span id="L241"><span class="lineNum">     241</span> <span class="tlaUNC">           0 :   const TokenFailure({</span></span>
<span id="L242"><span class="lineNum">     242</span>              :     String message = 'Authentication token error. Please sign in again',</span>
<span id="L243"><span class="lineNum">     243</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L244"><span class="lineNum">     244</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L245"><span class="lineNum">     245</span>              :           message: message,</span>
<span id="L246"><span class="lineNum">     246</span>              :           code: 'TOKEN_FAILURE',</span>
<span id="L247"><span class="lineNum">     247</span>              :           context: context,</span>
<span id="L248"><span class="lineNum">     248</span>              :         );</span>
<span id="L249"><span class="lineNum">     249</span>              : }</span>
<span id="L250"><span class="lineNum">     250</span>              : </span>
<span id="L251"><span class="lineNum">     251</span>              : /// Failure when session has expired</span>
<span id="L252"><span class="lineNum">     252</span>              : class SessionExpiredFailure extends AuthFailure {</span>
<span id="L253"><span class="lineNum">     253</span> <span class="tlaUNC">           0 :   const SessionExpiredFailure({</span></span>
<span id="L254"><span class="lineNum">     254</span>              :     String message = 'Your session has expired. Please sign in again',</span>
<span id="L255"><span class="lineNum">     255</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L256"><span class="lineNum">     256</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L257"><span class="lineNum">     257</span>              :           message: message,</span>
<span id="L258"><span class="lineNum">     258</span>              :           code: 'SESSION_EXPIRED',</span>
<span id="L259"><span class="lineNum">     259</span>              :           context: context,</span>
<span id="L260"><span class="lineNum">     260</span>              :         );</span>
<span id="L261"><span class="lineNum">     261</span>              : }</span>
<span id="L262"><span class="lineNum">     262</span>              : </span>
<span id="L263"><span class="lineNum">     263</span>              : /// Failure when input validation fails</span>
<span id="L264"><span class="lineNum">     264</span>              : class ValidationFailure extends AuthFailure {</span>
<span id="L265"><span class="lineNum">     265</span>              :   /// Field that failed validation</span>
<span id="L266"><span class="lineNum">     266</span>              :   final String field;</span>
<span id="L267"><span class="lineNum">     267</span>              : </span>
<span id="L268"><span class="lineNum">     268</span> <span class="tlaGNC">           2 :   const ValidationFailure({</span></span>
<span id="L269"><span class="lineNum">     269</span>              :     required this.field,</span>
<span id="L270"><span class="lineNum">     270</span>              :     required String message,</span>
<span id="L271"><span class="lineNum">     271</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L272"><span class="lineNum">     272</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L273"><span class="lineNum">     273</span>              :           message: message,</span>
<span id="L274"><span class="lineNum">     274</span>              :           code: 'VALIDATION_FAILURE',</span>
<span id="L275"><span class="lineNum">     275</span>              :           context: context,</span>
<span id="L276"><span class="lineNum">     276</span>              :         );</span>
<span id="L277"><span class="lineNum">     277</span>              : </span>
<span id="L278"><span class="lineNum">     278</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L279"><span class="lineNum">     279</span> <span class="tlaUNC">           0 :   List&lt;Object?&gt; get props =&gt; [message, code, context, field];</span></span>
<span id="L280"><span class="lineNum">     280</span>              : }</span>
<span id="L281"><span class="lineNum">     281</span>              : </span>
<span id="L282"><span class="lineNum">     282</span>              : /// Failure when email format is invalid</span>
<span id="L283"><span class="lineNum">     283</span>              : class InvalidEmailFailure extends ValidationFailure {</span>
<span id="L284"><span class="lineNum">     284</span> <span class="tlaUNC">           0 :   const InvalidEmailFailure({</span></span>
<span id="L285"><span class="lineNum">     285</span>              :     String message = 'Please enter a valid email address',</span>
<span id="L286"><span class="lineNum">     286</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L287"><span class="lineNum">     287</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L288"><span class="lineNum">     288</span>              :           field: 'email',</span>
<span id="L289"><span class="lineNum">     289</span>              :           message: message,</span>
<span id="L290"><span class="lineNum">     290</span>              :           context: context,</span>
<span id="L291"><span class="lineNum">     291</span>              :         );</span>
<span id="L292"><span class="lineNum">     292</span>              : }</span>
<span id="L293"><span class="lineNum">     293</span>              : </span>
<span id="L294"><span class="lineNum">     294</span>              : /// Failure when password format is invalid</span>
<span id="L295"><span class="lineNum">     295</span>              : class InvalidPasswordFailure extends ValidationFailure {</span>
<span id="L296"><span class="lineNum">     296</span> <span class="tlaUNC">           0 :   const InvalidPasswordFailure({</span></span>
<span id="L297"><span class="lineNum">     297</span>              :     String message = 'Password does not meet requirements',</span>
<span id="L298"><span class="lineNum">     298</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L299"><span class="lineNum">     299</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L300"><span class="lineNum">     300</span>              :           field: 'password',</span>
<span id="L301"><span class="lineNum">     301</span>              :           message: message,</span>
<span id="L302"><span class="lineNum">     302</span>              :           context: context,</span>
<span id="L303"><span class="lineNum">     303</span>              :         );</span>
<span id="L304"><span class="lineNum">     304</span>              : }</span>
<span id="L305"><span class="lineNum">     305</span>              : </span>
<span id="L306"><span class="lineNum">     306</span>              : /// Failure when request times out</span>
<span id="L307"><span class="lineNum">     307</span>              : class TimeoutFailure extends AuthFailure {</span>
<span id="L308"><span class="lineNum">     308</span> <span class="tlaGNC">           1 :   const TimeoutFailure({</span></span>
<span id="L309"><span class="lineNum">     309</span>              :     String message =</span>
<span id="L310"><span class="lineNum">     310</span>              :         'Request timed out. Please check your connection and try again',</span>
<span id="L311"><span class="lineNum">     311</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L312"><span class="lineNum">     312</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L313"><span class="lineNum">     313</span>              :           message: message,</span>
<span id="L314"><span class="lineNum">     314</span>              :           code: 'TIMEOUT_FAILURE',</span>
<span id="L315"><span class="lineNum">     315</span>              :           context: context,</span>
<span id="L316"><span class="lineNum">     316</span>              :         );</span>
<span id="L317"><span class="lineNum">     317</span>              : }</span>
<span id="L318"><span class="lineNum">     318</span>              : </span>
<span id="L319"><span class="lineNum">     319</span>              : /// Failure when two-factor authentication is not enabled</span>
<span id="L320"><span class="lineNum">     320</span>              : class TwoFactorNotEnabledFailure extends AuthFailure {</span>
<span id="L321"><span class="lineNum">     321</span> <span class="tlaUNC">           0 :   const TwoFactorNotEnabledFailure({</span></span>
<span id="L322"><span class="lineNum">     322</span>              :     String message =</span>
<span id="L323"><span class="lineNum">     323</span>              :         'Two-factor authentication is not enabled for this account',</span>
<span id="L324"><span class="lineNum">     324</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L325"><span class="lineNum">     325</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L326"><span class="lineNum">     326</span>              :           message: message,</span>
<span id="L327"><span class="lineNum">     327</span>              :           code: 'TWO_FACTOR_NOT_ENABLED',</span>
<span id="L328"><span class="lineNum">     328</span>              :           context: context,</span>
<span id="L329"><span class="lineNum">     329</span>              :         );</span>
<span id="L330"><span class="lineNum">     330</span>              : }</span>
<span id="L331"><span class="lineNum">     331</span>              : </span>
<span id="L332"><span class="lineNum">     332</span>              : /// Failure when two-factor authentication method is not supported</span>
<span id="L333"><span class="lineNum">     333</span>              : class TwoFactorMethodNotSupportedFailure extends AuthFailure {</span>
<span id="L334"><span class="lineNum">     334</span> <span class="tlaUNC">           0 :   const TwoFactorMethodNotSupportedFailure({</span></span>
<span id="L335"><span class="lineNum">     335</span>              :     String message =</span>
<span id="L336"><span class="lineNum">     336</span>              :         'The requested two-factor authentication method is not supported',</span>
<span id="L337"><span class="lineNum">     337</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L338"><span class="lineNum">     338</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L339"><span class="lineNum">     339</span>              :           message: message,</span>
<span id="L340"><span class="lineNum">     340</span>              :           code: 'TWO_FACTOR_METHOD_NOT_SUPPORTED',</span>
<span id="L341"><span class="lineNum">     341</span>              :           context: context,</span>
<span id="L342"><span class="lineNum">     342</span>              :         );</span>
<span id="L343"><span class="lineNum">     343</span>              : }</span>
<span id="L344"><span class="lineNum">     344</span>              : </span>
<span id="L345"><span class="lineNum">     345</span>              : /// Failure when too many two-factor authentication attempts have been made</span>
<span id="L346"><span class="lineNum">     346</span>              : class TwoFactorAttemptsExceededFailure extends AuthFailure {</span>
<span id="L347"><span class="lineNum">     347</span> <span class="tlaUNC">           0 :   const TwoFactorAttemptsExceededFailure({</span></span>
<span id="L348"><span class="lineNum">     348</span>              :     String message = 'Too many failed attempts. Please try again later',</span>
<span id="L349"><span class="lineNum">     349</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L350"><span class="lineNum">     350</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L351"><span class="lineNum">     351</span>              :           message: message,</span>
<span id="L352"><span class="lineNum">     352</span>              :           code: 'TWO_FACTOR_ATTEMPTS_EXCEEDED',</span>
<span id="L353"><span class="lineNum">     353</span>              :           context: context,</span>
<span id="L354"><span class="lineNum">     354</span>              :         );</span>
<span id="L355"><span class="lineNum">     355</span>              : }</span>
<span id="L356"><span class="lineNum">     356</span>              : </span>
<span id="L357"><span class="lineNum">     357</span>              : /// Failure when two-factor authentication system encounters an error</span>
<span id="L358"><span class="lineNum">     358</span>              : class TwoFactorSystemFailure extends AuthFailure {</span>
<span id="L359"><span class="lineNum">     359</span> <span class="tlaUNC">           0 :   const TwoFactorSystemFailure({</span></span>
<span id="L360"><span class="lineNum">     360</span>              :     String message = 'Two-factor authentication system error. Please try again',</span>
<span id="L361"><span class="lineNum">     361</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L362"><span class="lineNum">     362</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L363"><span class="lineNum">     363</span>              :           message: message,</span>
<span id="L364"><span class="lineNum">     364</span>              :           code: 'TWO_FACTOR_SYSTEM_ERROR',</span>
<span id="L365"><span class="lineNum">     365</span>              :           context: context,</span>
<span id="L366"><span class="lineNum">     366</span>              :         );</span>
<span id="L367"><span class="lineNum">     367</span>              : }</span>
<span id="L368"><span class="lineNum">     368</span>              : </span>
<span id="L369"><span class="lineNum">     369</span>              : /// Failure when two-factor authentication setup fails</span>
<span id="L370"><span class="lineNum">     370</span>              : class TwoFactorSetupFailure extends AuthFailure {</span>
<span id="L371"><span class="lineNum">     371</span> <span class="tlaUNC">           0 :   const TwoFactorSetupFailure({</span></span>
<span id="L372"><span class="lineNum">     372</span>              :     String message =</span>
<span id="L373"><span class="lineNum">     373</span>              :         'Failed to set up two-factor authentication. Please try again',</span>
<span id="L374"><span class="lineNum">     374</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L375"><span class="lineNum">     375</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L376"><span class="lineNum">     376</span>              :           message: message,</span>
<span id="L377"><span class="lineNum">     377</span>              :           code: 'TWO_FACTOR_SETUP_FAILURE',</span>
<span id="L378"><span class="lineNum">     378</span>              :           context: context,</span>
<span id="L379"><span class="lineNum">     379</span>              :         );</span>
<span id="L380"><span class="lineNum">     380</span>              : }</span>
<span id="L381"><span class="lineNum">     381</span>              : </span>
<span id="L382"><span class="lineNum">     382</span>              : /// Failure for unexpected or unknown errors</span>
<span id="L383"><span class="lineNum">     383</span>              : class UnknownFailure extends AuthFailure {</span>
<span id="L384"><span class="lineNum">     384</span> <span class="tlaUNC">           0 :   const UnknownFailure({</span></span>
<span id="L385"><span class="lineNum">     385</span>              :     String message = 'An unexpected error occurred. Please try again',</span>
<span id="L386"><span class="lineNum">     386</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L387"><span class="lineNum">     387</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L388"><span class="lineNum">     388</span>              :           message: message,</span>
<span id="L389"><span class="lineNum">     389</span>              :           code: 'UNKNOWN_FAILURE',</span>
<span id="L390"><span class="lineNum">     390</span>              :           context: context,</span>
<span id="L391"><span class="lineNum">     391</span>              :         );</span>
<span id="L392"><span class="lineNum">     392</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
