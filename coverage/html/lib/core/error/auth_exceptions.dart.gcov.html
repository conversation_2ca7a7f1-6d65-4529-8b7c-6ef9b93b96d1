<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/core/error/auth_exceptions.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/core/error">lib/core/error</a> - auth_exceptions.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">50</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-07-03 12:38:23</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : /// Authentication-specific exceptions for the data layer</span>
<span id="L2"><span class="lineNum">       2</span>              : ///</span>
<span id="L3"><span class="lineNum">       3</span>              : /// These exceptions represent technical errors that occur in the data layer</span>
<span id="L4"><span class="lineNum">       4</span>              : /// during authentication operations. They are caught by the repository layer</span>
<span id="L5"><span class="lineNum">       5</span>              : /// and mapped to appropriate domain failures.</span>
<span id="L6"><span class="lineNum">       6</span>              : ///</span>
<span id="L7"><span class="lineNum">       7</span>              : /// All authentication exceptions extend [AuthException] for consistent handling.</span>
<span id="L8"><span class="lineNum">       8</span>              : </span>
<span id="L9"><span class="lineNum">       9</span>              : /// Base class for all authentication-related exceptions</span>
<span id="L10"><span class="lineNum">      10</span>              : ///</span>
<span id="L11"><span class="lineNum">      11</span>              : /// This abstract class provides a common interface for all authentication</span>
<span id="L12"><span class="lineNum">      12</span>              : /// exceptions and includes error codes for programmatic handling.</span>
<span id="L13"><span class="lineNum">      13</span>              : abstract class AuthException implements Exception {</span>
<span id="L14"><span class="lineNum">      14</span>              :   /// Human-readable error message</span>
<span id="L15"><span class="lineNum">      15</span>              :   final String message;</span>
<span id="L16"><span class="lineNum">      16</span>              : </span>
<span id="L17"><span class="lineNum">      17</span>              :   /// Error code for programmatic handling</span>
<span id="L18"><span class="lineNum">      18</span>              :   final String code;</span>
<span id="L19"><span class="lineNum">      19</span>              : </span>
<span id="L20"><span class="lineNum">      20</span>              :   /// Additional context information</span>
<span id="L21"><span class="lineNum">      21</span>              :   final Map&lt;String, dynamic&gt;? context;</span>
<span id="L22"><span class="lineNum">      22</span>              : </span>
<span id="L23"><span class="lineNum">      23</span> <span class="tlaUNC">           0 :   const AuthException({</span></span>
<span id="L24"><span class="lineNum">      24</span>              :     required this.message,</span>
<span id="L25"><span class="lineNum">      25</span>              :     required this.code,</span>
<span id="L26"><span class="lineNum">      26</span>              :     this.context,</span>
<span id="L27"><span class="lineNum">      27</span>              :   });</span>
<span id="L28"><span class="lineNum">      28</span>              : </span>
<span id="L29"><span class="lineNum">      29</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaUNC">           0 :   String toString() =&gt; 'AuthException($code): $message';</span></span>
<span id="L31"><span class="lineNum">      31</span>              : }</span>
<span id="L32"><span class="lineNum">      32</span>              : </span>
<span id="L33"><span class="lineNum">      33</span>              : /// Exception thrown when network connectivity issues occur</span>
<span id="L34"><span class="lineNum">      34</span>              : class NetworkException extends AuthException {</span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaUNC">           0 :   const NetworkException({</span></span>
<span id="L36"><span class="lineNum">      36</span>              :     String message = 'Network connection failed',</span>
<span id="L37"><span class="lineNum">      37</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L39"><span class="lineNum">      39</span>              :           message: message,</span>
<span id="L40"><span class="lineNum">      40</span>              :           code: 'NETWORK_ERROR',</span>
<span id="L41"><span class="lineNum">      41</span>              :           context: context,</span>
<span id="L42"><span class="lineNum">      42</span>              :         );</span>
<span id="L43"><span class="lineNum">      43</span>              : }</span>
<span id="L44"><span class="lineNum">      44</span>              : </span>
<span id="L45"><span class="lineNum">      45</span>              : /// Exception thrown when server returns an error response</span>
<span id="L46"><span class="lineNum">      46</span>              : class ServerException extends AuthException {</span>
<span id="L47"><span class="lineNum">      47</span>              :   /// HTTP status code if applicable</span>
<span id="L48"><span class="lineNum">      48</span>              :   final int? statusCode;</span>
<span id="L49"><span class="lineNum">      49</span>              : </span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaUNC">           0 :   const ServerException({</span></span>
<span id="L51"><span class="lineNum">      51</span>              :     required String message,</span>
<span id="L52"><span class="lineNum">      52</span>              :     this.statusCode,</span>
<span id="L53"><span class="lineNum">      53</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L55"><span class="lineNum">      55</span>              :           message: message,</span>
<span id="L56"><span class="lineNum">      56</span>              :           code: 'SERVER_ERROR',</span>
<span id="L57"><span class="lineNum">      57</span>              :           context: context,</span>
<span id="L58"><span class="lineNum">      58</span>              :         );</span>
<span id="L59"><span class="lineNum">      59</span>              : </span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L61"><span class="lineNum">      61</span>              :   String toString() =&gt;</span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaUNC">           0 :       'ServerException($code): $message${statusCode != null ? ' (HTTP $statusCode)' : ''}';</span></span>
<span id="L63"><span class="lineNum">      63</span>              : }</span>
<span id="L64"><span class="lineNum">      64</span>              : </span>
<span id="L65"><span class="lineNum">      65</span>              : /// Exception thrown when authentication credentials are invalid</span>
<span id="L66"><span class="lineNum">      66</span>              : class InvalidCredentialsException extends AuthException {</span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :   const InvalidCredentialsException({</span></span>
<span id="L68"><span class="lineNum">      68</span>              :     String message = 'Invalid email or password',</span>
<span id="L69"><span class="lineNum">      69</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L71"><span class="lineNum">      71</span>              :           message: message,</span>
<span id="L72"><span class="lineNum">      72</span>              :           code: 'INVALID_CREDENTIALS',</span>
<span id="L73"><span class="lineNum">      73</span>              :           context: context,</span>
<span id="L74"><span class="lineNum">      74</span>              :         );</span>
<span id="L75"><span class="lineNum">      75</span>              : }</span>
<span id="L76"><span class="lineNum">      76</span>              : </span>
<span id="L77"><span class="lineNum">      77</span>              : /// Exception thrown when user account is not verified</span>
<span id="L78"><span class="lineNum">      78</span>              : class AccountNotVerifiedException extends AuthException {</span>
<span id="L79"><span class="lineNum">      79</span>              :   /// Type of verification required (email, phone, etc.)</span>
<span id="L80"><span class="lineNum">      80</span>              :   final String verificationType;</span>
<span id="L81"><span class="lineNum">      81</span>              : </span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaUNC">           0 :   const AccountNotVerifiedException({</span></span>
<span id="L83"><span class="lineNum">      83</span>              :     required this.verificationType,</span>
<span id="L84"><span class="lineNum">      84</span>              :     String? message,</span>
<span id="L85"><span class="lineNum">      85</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaUNC">           0 :           message: message ?? 'Account $verificationType verification required',</span></span>
<span id="L88"><span class="lineNum">      88</span>              :           code: 'ACCOUNT_NOT_VERIFIED',</span>
<span id="L89"><span class="lineNum">      89</span>              :           context: context,</span>
<span id="L90"><span class="lineNum">      90</span>              :         );</span>
<span id="L91"><span class="lineNum">      91</span>              : }</span>
<span id="L92"><span class="lineNum">      92</span>              : </span>
<span id="L93"><span class="lineNum">      93</span>              : /// Exception thrown when two-factor authentication fails</span>
<span id="L94"><span class="lineNum">      94</span>              : class TwoFactorException extends AuthException {</span>
<span id="L95"><span class="lineNum">      95</span> <span class="tlaUNC">           0 :   const TwoFactorException({</span></span>
<span id="L96"><span class="lineNum">      96</span>              :     required String message,</span>
<span id="L97"><span class="lineNum">      97</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L99"><span class="lineNum">      99</span>              :           message: message,</span>
<span id="L100"><span class="lineNum">     100</span>              :           code: 'TWO_FACTOR_ERROR',</span>
<span id="L101"><span class="lineNum">     101</span>              :           context: context,</span>
<span id="L102"><span class="lineNum">     102</span>              :         );</span>
<span id="L103"><span class="lineNum">     103</span>              : }</span>
<span id="L104"><span class="lineNum">     104</span>              : </span>
<span id="L105"><span class="lineNum">     105</span>              : /// Exception thrown when two-factor authentication code is invalid</span>
<span id="L106"><span class="lineNum">     106</span>              : class InvalidTwoFactorCodeException extends TwoFactorException {</span>
<span id="L107"><span class="lineNum">     107</span> <span class="tlaUNC">           0 :   const InvalidTwoFactorCodeException({</span></span>
<span id="L108"><span class="lineNum">     108</span>              :     String message = 'Invalid two-factor authentication code',</span>
<span id="L109"><span class="lineNum">     109</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L111"><span class="lineNum">     111</span>              :           message: message,</span>
<span id="L112"><span class="lineNum">     112</span>              :           context: context,</span>
<span id="L113"><span class="lineNum">     113</span>              :         );</span>
<span id="L114"><span class="lineNum">     114</span>              : }</span>
<span id="L115"><span class="lineNum">     115</span>              : </span>
<span id="L116"><span class="lineNum">     116</span>              : /// Exception thrown when two-factor authentication code has expired</span>
<span id="L117"><span class="lineNum">     117</span>              : class TwoFactorCodeExpiredException extends TwoFactorException {</span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaUNC">           0 :   const TwoFactorCodeExpiredException({</span></span>
<span id="L119"><span class="lineNum">     119</span>              :     String message = 'Two-factor authentication code has expired',</span>
<span id="L120"><span class="lineNum">     120</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L122"><span class="lineNum">     122</span>              :           message: message,</span>
<span id="L123"><span class="lineNum">     123</span>              :           context: context,</span>
<span id="L124"><span class="lineNum">     124</span>              :         );</span>
<span id="L125"><span class="lineNum">     125</span>              : }</span>
<span id="L126"><span class="lineNum">     126</span>              : </span>
<span id="L127"><span class="lineNum">     127</span>              : /// Exception thrown when biometric authentication fails</span>
<span id="L128"><span class="lineNum">     128</span>              : class BiometricException extends AuthException {</span>
<span id="L129"><span class="lineNum">     129</span>              :   /// Type of biometric failure</span>
<span id="L130"><span class="lineNum">     130</span>              :   final BiometricFailureType failureType;</span>
<span id="L131"><span class="lineNum">     131</span>              : </span>
<span id="L132"><span class="lineNum">     132</span> <span class="tlaUNC">           0 :   BiometricException({</span></span>
<span id="L133"><span class="lineNum">     133</span>              :     required this.failureType,</span>
<span id="L134"><span class="lineNum">     134</span>              :     String? message,</span>
<span id="L135"><span class="lineNum">     135</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L136"><span class="lineNum">     136</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L137"><span class="lineNum">     137</span> <span class="tlaUNC">           0 :           message: message ?? _getDefaultMessage(failureType),</span></span>
<span id="L138"><span class="lineNum">     138</span>              :           code: 'BIOMETRIC_ERROR',</span>
<span id="L139"><span class="lineNum">     139</span>              :           context: context,</span>
<span id="L140"><span class="lineNum">     140</span>              :         );</span>
<span id="L141"><span class="lineNum">     141</span>              : </span>
<span id="L142"><span class="lineNum">     142</span> <span class="tlaUNC">           0 :   static String _getDefaultMessage(BiometricFailureType type) {</span></span>
<span id="L143"><span class="lineNum">     143</span>              :     switch (type) {</span>
<span id="L144"><span class="lineNum">     144</span> <span class="tlaUNC">           0 :       case BiometricFailureType.notAvailable:</span></span>
<span id="L145"><span class="lineNum">     145</span>              :         return 'Biometric authentication is not available on this device';</span>
<span id="L146"><span class="lineNum">     146</span> <span class="tlaUNC">           0 :       case BiometricFailureType.notEnrolled:</span></span>
<span id="L147"><span class="lineNum">     147</span>              :         return 'No biometric credentials are enrolled on this device';</span>
<span id="L148"><span class="lineNum">     148</span> <span class="tlaUNC">           0 :       case BiometricFailureType.authenticationFailed:</span></span>
<span id="L149"><span class="lineNum">     149</span>              :         return 'Biometric authentication failed';</span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaUNC">           0 :       case BiometricFailureType.userCancelled:</span></span>
<span id="L151"><span class="lineNum">     151</span>              :         return 'Biometric authentication was cancelled by user';</span>
<span id="L152"><span class="lineNum">     152</span> <span class="tlaUNC">           0 :       case BiometricFailureType.lockedOut:</span></span>
<span id="L153"><span class="lineNum">     153</span>              :         return 'Biometric authentication is temporarily locked';</span>
<span id="L154"><span class="lineNum">     154</span> <span class="tlaUNC">           0 :       case BiometricFailureType.permanentlyLockedOut:</span></span>
<span id="L155"><span class="lineNum">     155</span>              :         return 'Biometric authentication is permanently locked';</span>
<span id="L156"><span class="lineNum">     156</span> <span class="tlaUNC">           0 :       case BiometricFailureType.noStoredCredentials:</span></span>
<span id="L157"><span class="lineNum">     157</span>              :         return 'No stored credentials found for biometric authentication';</span>
<span id="L158"><span class="lineNum">     158</span>              :     }</span>
<span id="L159"><span class="lineNum">     159</span>              :   }</span>
<span id="L160"><span class="lineNum">     160</span>              : }</span>
<span id="L161"><span class="lineNum">     161</span>              : </span>
<span id="L162"><span class="lineNum">     162</span>              : /// Types of biometric authentication failures</span>
<span id="L163"><span class="lineNum">     163</span>              : enum BiometricFailureType {</span>
<span id="L164"><span class="lineNum">     164</span>              :   /// Biometric authentication is not available on the device</span>
<span id="L165"><span class="lineNum">     165</span>              :   notAvailable,</span>
<span id="L166"><span class="lineNum">     166</span>              : </span>
<span id="L167"><span class="lineNum">     167</span>              :   /// No biometric credentials are enrolled</span>
<span id="L168"><span class="lineNum">     168</span>              :   notEnrolled,</span>
<span id="L169"><span class="lineNum">     169</span>              : </span>
<span id="L170"><span class="lineNum">     170</span>              :   /// Biometric authentication failed (e.g., fingerprint not recognized)</span>
<span id="L171"><span class="lineNum">     171</span>              :   authenticationFailed,</span>
<span id="L172"><span class="lineNum">     172</span>              : </span>
<span id="L173"><span class="lineNum">     173</span>              :   /// User cancelled the biometric authentication</span>
<span id="L174"><span class="lineNum">     174</span>              :   userCancelled,</span>
<span id="L175"><span class="lineNum">     175</span>              : </span>
<span id="L176"><span class="lineNum">     176</span>              :   /// Biometric authentication is temporarily locked due to too many failed attempts</span>
<span id="L177"><span class="lineNum">     177</span>              :   lockedOut,</span>
<span id="L178"><span class="lineNum">     178</span>              : </span>
<span id="L179"><span class="lineNum">     179</span>              :   /// Biometric authentication is permanently locked</span>
<span id="L180"><span class="lineNum">     180</span>              :   permanentlyLockedOut,</span>
<span id="L181"><span class="lineNum">     181</span>              : </span>
<span id="L182"><span class="lineNum">     182</span>              :   /// No stored credentials found for biometric authentication</span>
<span id="L183"><span class="lineNum">     183</span>              :   noStoredCredentials,</span>
<span id="L184"><span class="lineNum">     184</span>              : }</span>
<span id="L185"><span class="lineNum">     185</span>              : </span>
<span id="L186"><span class="lineNum">     186</span>              : /// Exception thrown when token operations fail</span>
<span id="L187"><span class="lineNum">     187</span>              : class TokenException extends AuthException {</span>
<span id="L188"><span class="lineNum">     188</span> <span class="tlaUNC">           0 :   const TokenException({</span></span>
<span id="L189"><span class="lineNum">     189</span>              :     required String message,</span>
<span id="L190"><span class="lineNum">     190</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L191"><span class="lineNum">     191</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L192"><span class="lineNum">     192</span>              :           message: message,</span>
<span id="L193"><span class="lineNum">     193</span>              :           code: 'TOKEN_ERROR',</span>
<span id="L194"><span class="lineNum">     194</span>              :           context: context,</span>
<span id="L195"><span class="lineNum">     195</span>              :         );</span>
<span id="L196"><span class="lineNum">     196</span>              : }</span>
<span id="L197"><span class="lineNum">     197</span>              : </span>
<span id="L198"><span class="lineNum">     198</span>              : /// Exception thrown when access token is invalid or expired</span>
<span id="L199"><span class="lineNum">     199</span>              : class InvalidTokenException extends TokenException {</span>
<span id="L200"><span class="lineNum">     200</span> <span class="tlaUNC">           0 :   const InvalidTokenException({</span></span>
<span id="L201"><span class="lineNum">     201</span>              :     String message = 'Access token is invalid or expired',</span>
<span id="L202"><span class="lineNum">     202</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L203"><span class="lineNum">     203</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L204"><span class="lineNum">     204</span>              :           message: message,</span>
<span id="L205"><span class="lineNum">     205</span>              :           context: context,</span>
<span id="L206"><span class="lineNum">     206</span>              :         );</span>
<span id="L207"><span class="lineNum">     207</span>              : }</span>
<span id="L208"><span class="lineNum">     208</span>              : </span>
<span id="L209"><span class="lineNum">     209</span>              : /// Exception thrown when refresh token is invalid or expired</span>
<span id="L210"><span class="lineNum">     210</span>              : class RefreshTokenExpiredException extends TokenException {</span>
<span id="L211"><span class="lineNum">     211</span> <span class="tlaUNC">           0 :   const RefreshTokenExpiredException({</span></span>
<span id="L212"><span class="lineNum">     212</span>              :     String message = 'Refresh token has expired',</span>
<span id="L213"><span class="lineNum">     213</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L214"><span class="lineNum">     214</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L215"><span class="lineNum">     215</span>              :           message: message,</span>
<span id="L216"><span class="lineNum">     216</span>              :           context: context,</span>
<span id="L217"><span class="lineNum">     217</span>              :         );</span>
<span id="L218"><span class="lineNum">     218</span>              : }</span>
<span id="L219"><span class="lineNum">     219</span>              : </span>
<span id="L220"><span class="lineNum">     220</span>              : /// Exception thrown when input validation fails</span>
<span id="L221"><span class="lineNum">     221</span>              : class ValidationException extends AuthException {</span>
<span id="L222"><span class="lineNum">     222</span>              :   /// Field that failed validation</span>
<span id="L223"><span class="lineNum">     223</span>              :   final String field;</span>
<span id="L224"><span class="lineNum">     224</span>              : </span>
<span id="L225"><span class="lineNum">     225</span> <span class="tlaUNC">           0 :   const ValidationException({</span></span>
<span id="L226"><span class="lineNum">     226</span>              :     required this.field,</span>
<span id="L227"><span class="lineNum">     227</span>              :     required String message,</span>
<span id="L228"><span class="lineNum">     228</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L229"><span class="lineNum">     229</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L230"><span class="lineNum">     230</span>              :           message: message,</span>
<span id="L231"><span class="lineNum">     231</span>              :           code: 'VALIDATION_ERROR',</span>
<span id="L232"><span class="lineNum">     232</span>              :           context: context,</span>
<span id="L233"><span class="lineNum">     233</span>              :         );</span>
<span id="L234"><span class="lineNum">     234</span>              : }</span>
<span id="L235"><span class="lineNum">     235</span>              : </span>
<span id="L236"><span class="lineNum">     236</span>              : /// Exception thrown when email format is invalid</span>
<span id="L237"><span class="lineNum">     237</span>              : class InvalidEmailFormatException extends ValidationException {</span>
<span id="L238"><span class="lineNum">     238</span> <span class="tlaUNC">           0 :   const InvalidEmailFormatException({</span></span>
<span id="L239"><span class="lineNum">     239</span>              :     String message = 'Please enter a valid email address',</span>
<span id="L240"><span class="lineNum">     240</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L241"><span class="lineNum">     241</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L242"><span class="lineNum">     242</span>              :           field: 'email',</span>
<span id="L243"><span class="lineNum">     243</span>              :           message: message,</span>
<span id="L244"><span class="lineNum">     244</span>              :           context: context,</span>
<span id="L245"><span class="lineNum">     245</span>              :         );</span>
<span id="L246"><span class="lineNum">     246</span>              : }</span>
<span id="L247"><span class="lineNum">     247</span>              : </span>
<span id="L248"><span class="lineNum">     248</span>              : /// Exception thrown when password format is invalid</span>
<span id="L249"><span class="lineNum">     249</span>              : class InvalidPasswordFormatException extends ValidationException {</span>
<span id="L250"><span class="lineNum">     250</span> <span class="tlaUNC">           0 :   const InvalidPasswordFormatException({</span></span>
<span id="L251"><span class="lineNum">     251</span>              :     String message = 'Password does not meet requirements',</span>
<span id="L252"><span class="lineNum">     252</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L253"><span class="lineNum">     253</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L254"><span class="lineNum">     254</span>              :           field: 'password',</span>
<span id="L255"><span class="lineNum">     255</span>              :           message: message,</span>
<span id="L256"><span class="lineNum">     256</span>              :           context: context,</span>
<span id="L257"><span class="lineNum">     257</span>              :         );</span>
<span id="L258"><span class="lineNum">     258</span>              : }</span>
<span id="L259"><span class="lineNum">     259</span>              : </span>
<span id="L260"><span class="lineNum">     260</span>              : /// Exception thrown when request timeout occurs</span>
<span id="L261"><span class="lineNum">     261</span>              : class TimeoutException extends AuthException {</span>
<span id="L262"><span class="lineNum">     262</span>              :   /// Timeout duration in seconds</span>
<span id="L263"><span class="lineNum">     263</span>              :   final int timeoutSeconds;</span>
<span id="L264"><span class="lineNum">     264</span>              : </span>
<span id="L265"><span class="lineNum">     265</span> <span class="tlaUNC">           0 :   const TimeoutException({</span></span>
<span id="L266"><span class="lineNum">     266</span>              :     required this.timeoutSeconds,</span>
<span id="L267"><span class="lineNum">     267</span>              :     String? message,</span>
<span id="L268"><span class="lineNum">     268</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L269"><span class="lineNum">     269</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L270"><span class="lineNum">     270</span> <span class="tlaUNC">           0 :           message: message ?? 'Request timed out after $timeoutSeconds seconds',</span></span>
<span id="L271"><span class="lineNum">     271</span>              :           code: 'TIMEOUT_ERROR',</span>
<span id="L272"><span class="lineNum">     272</span>              :           context: context,</span>
<span id="L273"><span class="lineNum">     273</span>              :         );</span>
<span id="L274"><span class="lineNum">     274</span>              : }</span>
<span id="L275"><span class="lineNum">     275</span>              : </span>
<span id="L276"><span class="lineNum">     276</span>              : /// Exception thrown for unexpected or unknown errors</span>
<span id="L277"><span class="lineNum">     277</span>              : class UnknownAuthException extends AuthException {</span>
<span id="L278"><span class="lineNum">     278</span>              :   /// Original exception that caused this error</span>
<span id="L279"><span class="lineNum">     279</span>              :   final dynamic originalException;</span>
<span id="L280"><span class="lineNum">     280</span>              : </span>
<span id="L281"><span class="lineNum">     281</span> <span class="tlaUNC">           0 :   const UnknownAuthException({</span></span>
<span id="L282"><span class="lineNum">     282</span>              :     required this.originalException,</span>
<span id="L283"><span class="lineNum">     283</span>              :     String message = 'An unexpected error occurred',</span>
<span id="L284"><span class="lineNum">     284</span>              :     Map&lt;String, dynamic&gt;? context,</span>
<span id="L285"><span class="lineNum">     285</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L286"><span class="lineNum">     286</span>              :           message: message,</span>
<span id="L287"><span class="lineNum">     287</span>              :           code: 'UNKNOWN_ERROR',</span>
<span id="L288"><span class="lineNum">     288</span>              :           context: context,</span>
<span id="L289"><span class="lineNum">     289</span>              :         );</span>
<span id="L290"><span class="lineNum">     290</span>              : </span>
<span id="L291"><span class="lineNum">     291</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L292"><span class="lineNum">     292</span>              :   String toString() =&gt;</span>
<span id="L293"><span class="lineNum">     293</span> <span class="tlaUNC">           0 :       'UnknownAuthException($code): $message (Original: $originalException)';</span></span>
<span id="L294"><span class="lineNum">     294</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
