<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/models/auth_user_model.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/models">lib/data/models</a> - auth_user_model.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">52</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-07-03 12:38:23</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:json_annotation/json_annotation.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import '../../domain/entities/auth_user.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : </span>
<span id="L4"><span class="lineNum">       4</span>              : part 'auth_user_model.g.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : </span>
<span id="L6"><span class="lineNum">       6</span>              : /// Data model for user information with JSON serialization</span>
<span id="L7"><span class="lineNum">       7</span>              : ///</span>
<span id="L8"><span class="lineNum">       8</span>              : /// This model handles the conversion between API responses and domain entities.</span>
<span id="L9"><span class="lineNum">       9</span>              : /// It matches the structure returned by the tokenCheck() API endpoint.</span>
<span id="L10"><span class="lineNum">      10</span>              : @JsonSerializable()</span>
<span id="L11"><span class="lineNum">      11</span>              : class AuthUserModel {</span>
<span id="L12"><span class="lineNum">      12</span>              :   /// User's unique identifier</span>
<span id="L13"><span class="lineNum">      13</span>              :   @JsonKey(name: 'userID')</span>
<span id="L14"><span class="lineNum">      14</span>              :   final String id;</span>
<span id="L15"><span class="lineNum">      15</span>              : </span>
<span id="L16"><span class="lineNum">      16</span>              :   /// User's full name</span>
<span id="L17"><span class="lineNum">      17</span>              :   final String name;</span>
<span id="L18"><span class="lineNum">      18</span>              : </span>
<span id="L19"><span class="lineNum">      19</span>              :   /// User's email address</span>
<span id="L20"><span class="lineNum">      20</span>              :   final String email;</span>
<span id="L21"><span class="lineNum">      21</span>              : </span>
<span id="L22"><span class="lineNum">      22</span>              :   /// Whether the user's email has been verified</span>
<span id="L23"><span class="lineNum">      23</span>              :   final bool emailVerified;</span>
<span id="L24"><span class="lineNum">      24</span>              : </span>
<span id="L25"><span class="lineNum">      25</span>              :   /// User's phone number</span>
<span id="L26"><span class="lineNum">      26</span>              :   final String phone;</span>
<span id="L27"><span class="lineNum">      27</span>              : </span>
<span id="L28"><span class="lineNum">      28</span>              :   /// Whether the user's phone number has been verified</span>
<span id="L29"><span class="lineNum">      29</span>              :   final bool phoneVerified;</span>
<span id="L30"><span class="lineNum">      30</span>              : </span>
<span id="L31"><span class="lineNum">      31</span>              :   /// Timestamp of the last password change (in milliseconds since epoch as string)</span>
<span id="L32"><span class="lineNum">      32</span>              :   final String? lastPassChange;</span>
<span id="L33"><span class="lineNum">      33</span>              : </span>
<span id="L34"><span class="lineNum">      34</span>              :   /// Timestamp of the last update (in milliseconds since epoch)</span>
<span id="L35"><span class="lineNum">      35</span>              :   final int? lastUpdate;</span>
<span id="L36"><span class="lineNum">      36</span>              : </span>
<span id="L37"><span class="lineNum">      37</span>              :   /// Multi-factor authentication setting</span>
<span id="L38"><span class="lineNum">      38</span>              :   /// - &quot;0&quot; = disabled</span>
<span id="L39"><span class="lineNum">      39</span>              :   /// - &quot;app&quot; = app-based 2FA</span>
<span id="L40"><span class="lineNum">      40</span>              :   /// - &quot;sms&quot; = SMS-based 2FA</span>
<span id="L41"><span class="lineNum">      41</span>              :   final String? multiFactor;</span>
<span id="L42"><span class="lineNum">      42</span>              : </span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaUNC">           0 :   const AuthUserModel({</span></span>
<span id="L44"><span class="lineNum">      44</span>              :     required this.id,</span>
<span id="L45"><span class="lineNum">      45</span>              :     required this.name,</span>
<span id="L46"><span class="lineNum">      46</span>              :     required this.email,</span>
<span id="L47"><span class="lineNum">      47</span>              :     required this.emailVerified,</span>
<span id="L48"><span class="lineNum">      48</span>              :     required this.phone,</span>
<span id="L49"><span class="lineNum">      49</span>              :     required this.phoneVerified,</span>
<span id="L50"><span class="lineNum">      50</span>              :     this.lastPassChange,</span>
<span id="L51"><span class="lineNum">      51</span>              :     this.lastUpdate,</span>
<span id="L52"><span class="lineNum">      52</span>              :     this.multiFactor,</span>
<span id="L53"><span class="lineNum">      53</span>              :   });</span>
<span id="L54"><span class="lineNum">      54</span>              : </span>
<span id="L55"><span class="lineNum">      55</span>              :   /// Creates an AuthUserModel from JSON</span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaUNC">           0 :   factory AuthUserModel.fromJson(Map&lt;String, dynamic&gt; json) =&gt;</span></span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaUNC">           0 :       _$AuthUserModelFromJson(json);</span></span>
<span id="L58"><span class="lineNum">      58</span>              : </span>
<span id="L59"><span class="lineNum">      59</span>              :   /// Converts AuthUserModel to JSON</span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaUNC">           0 :   Map&lt;String, dynamic&gt; toJson() =&gt; _$AuthUserModelToJson(this);</span></span>
<span id="L61"><span class="lineNum">      61</span>              : </span>
<span id="L62"><span class="lineNum">      62</span>              :   /// Converts this data model to a domain entity</span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaUNC">           0 :   AuthUser toDomain() {</span></span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :     return AuthUser(</span></span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaUNC">           0 :       id: id,</span></span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaUNC">           0 :       name: name,</span></span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :       email: email,</span></span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaUNC">           0 :       emailVerified: emailVerified,</span></span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :       phone: phone,</span></span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :       phoneVerified: phoneVerified,</span></span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :       lastPassChange: lastPassChange,</span></span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaUNC">           0 :       lastUpdate: lastUpdate,</span></span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :       multiFactor: multiFactor,</span></span>
<span id="L74"><span class="lineNum">      74</span>              :     );</span>
<span id="L75"><span class="lineNum">      75</span>              :   }</span>
<span id="L76"><span class="lineNum">      76</span>              : </span>
<span id="L77"><span class="lineNum">      77</span>              :   /// Creates an AuthUserModel from a domain entity</span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :   factory AuthUserModel.fromDomain(AuthUser user) {</span></span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaUNC">           0 :     return AuthUserModel(</span></span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaUNC">           0 :       id: user.id,</span></span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaUNC">           0 :       name: user.name,</span></span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaUNC">           0 :       email: user.email,</span></span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaUNC">           0 :       emailVerified: user.emailVerified,</span></span>
<span id="L84"><span class="lineNum">      84</span> <span class="tlaUNC">           0 :       phone: user.phone,</span></span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaUNC">           0 :       phoneVerified: user.phoneVerified,</span></span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaUNC">           0 :       lastPassChange: user.lastPassChange,</span></span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaUNC">           0 :       lastUpdate: user.lastUpdate,</span></span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaUNC">           0 :       multiFactor: user.multiFactor,</span></span>
<span id="L89"><span class="lineNum">      89</span>              :     );</span>
<span id="L90"><span class="lineNum">      90</span>              :   }</span>
<span id="L91"><span class="lineNum">      91</span>              : </span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L93"><span class="lineNum">      93</span>              :   String toString() {</span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaUNC">           0 :     return 'AuthUserModel(id: $id, name: $name, email: $email, '</span></span>
<span id="L95"><span class="lineNum">      95</span> <span class="tlaUNC">           0 :         'emailVerified: $emailVerified, phone: $phone, '</span></span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaUNC">           0 :         'phoneVerified: $phoneVerified, multiFactor: $multiFactor)';</span></span>
<span id="L97"><span class="lineNum">      97</span>              :   }</span>
<span id="L98"><span class="lineNum">      98</span>              : </span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L100"><span class="lineNum">     100</span>              :   bool operator ==(Object other) {</span>
<span id="L101"><span class="lineNum">     101</span>              :     if (identical(this, other)) return true;</span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaUNC">           0 :     return other is AuthUserModel &amp;&amp;</span></span>
<span id="L103"><span class="lineNum">     103</span> <span class="tlaUNC">           0 :         other.id == id &amp;&amp;</span></span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaUNC">           0 :         other.name == name &amp;&amp;</span></span>
<span id="L105"><span class="lineNum">     105</span> <span class="tlaUNC">           0 :         other.email == email &amp;&amp;</span></span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaUNC">           0 :         other.emailVerified == emailVerified &amp;&amp;</span></span>
<span id="L107"><span class="lineNum">     107</span> <span class="tlaUNC">           0 :         other.phone == phone &amp;&amp;</span></span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaUNC">           0 :         other.phoneVerified == phoneVerified &amp;&amp;</span></span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaUNC">           0 :         other.lastPassChange == lastPassChange &amp;&amp;</span></span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaUNC">           0 :         other.lastUpdate == lastUpdate &amp;&amp;</span></span>
<span id="L111"><span class="lineNum">     111</span> <span class="tlaUNC">           0 :         other.multiFactor == multiFactor;</span></span>
<span id="L112"><span class="lineNum">     112</span>              :   }</span>
<span id="L113"><span class="lineNum">     113</span>              : </span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L115"><span class="lineNum">     115</span>              :   int get hashCode {</span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaUNC">           0 :     return Object.hash(</span></span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaUNC">           0 :       id,</span></span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaUNC">           0 :       name,</span></span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaUNC">           0 :       email,</span></span>
<span id="L120"><span class="lineNum">     120</span> <span class="tlaUNC">           0 :       emailVerified,</span></span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaUNC">           0 :       phone,</span></span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaUNC">           0 :       phoneVerified,</span></span>
<span id="L123"><span class="lineNum">     123</span> <span class="tlaUNC">           0 :       lastPassChange,</span></span>
<span id="L124"><span class="lineNum">     124</span> <span class="tlaUNC">           0 :       lastUpdate,</span></span>
<span id="L125"><span class="lineNum">     125</span> <span class="tlaUNC">           0 :       multiFactor,</span></span>
<span id="L126"><span class="lineNum">     126</span>              :     );</span>
<span id="L127"><span class="lineNum">     127</span>              :   }</span>
<span id="L128"><span class="lineNum">     128</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
