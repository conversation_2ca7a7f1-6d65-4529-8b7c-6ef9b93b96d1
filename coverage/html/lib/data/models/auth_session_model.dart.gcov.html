<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/models/auth_session_model.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/models">lib/data/models</a> - auth_session_model.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">55</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-07-03 12:38:23</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:json_annotation/json_annotation.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : part 'auth_session_model.g.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : /// Data model for authentication session with JSON serialization</span>
<span id="L6"><span class="lineNum">       6</span>              : /// </span>
<span id="L7"><span class="lineNum">       7</span>              : /// This model handles token storage, expiration tracking, and session management.</span>
<span id="L8"><span class="lineNum">       8</span>              : /// It provides methods to check token validity and manage session state.</span>
<span id="L9"><span class="lineNum">       9</span>              : @JsonSerializable()</span>
<span id="L10"><span class="lineNum">      10</span>              : class AuthSessionModel {</span>
<span id="L11"><span class="lineNum">      11</span>              :   /// JWT access token for API authentication</span>
<span id="L12"><span class="lineNum">      12</span>              :   final String accessToken;</span>
<span id="L13"><span class="lineNum">      13</span>              :   </span>
<span id="L14"><span class="lineNum">      14</span>              :   /// Refresh token for obtaining new access tokens</span>
<span id="L15"><span class="lineNum">      15</span>              :   final String refreshToken;</span>
<span id="L16"><span class="lineNum">      16</span>              :   </span>
<span id="L17"><span class="lineNum">      17</span>              :   /// Timestamp when the session was created (milliseconds since epoch)</span>
<span id="L18"><span class="lineNum">      18</span>              :   final int createdAt;</span>
<span id="L19"><span class="lineNum">      19</span>              :   </span>
<span id="L20"><span class="lineNum">      20</span>              :   /// Timestamp when the session was last updated (milliseconds since epoch)</span>
<span id="L21"><span class="lineNum">      21</span>              :   final int lastUpdated;</span>
<span id="L22"><span class="lineNum">      22</span>              :   </span>
<span id="L23"><span class="lineNum">      23</span>              :   /// Whether two-factor authentication is enabled for this session</span>
<span id="L24"><span class="lineNum">      24</span>              :   final bool twoFactorEnabled;</span>
<span id="L25"><span class="lineNum">      25</span>              :   </span>
<span id="L26"><span class="lineNum">      26</span>              :   /// User's email associated with this session</span>
<span id="L27"><span class="lineNum">      27</span>              :   final String? email;</span>
<span id="L28"><span class="lineNum">      28</span>              :   </span>
<span id="L29"><span class="lineNum">      29</span>              :   /// Whether biometric authentication is enabled for this session</span>
<span id="L30"><span class="lineNum">      30</span>              :   final bool biometricEnabled;</span>
<span id="L31"><span class="lineNum">      31</span>              : </span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaUNC">           0 :   const AuthSessionModel({</span></span>
<span id="L33"><span class="lineNum">      33</span>              :     required this.accessToken,</span>
<span id="L34"><span class="lineNum">      34</span>              :     required this.refreshToken,</span>
<span id="L35"><span class="lineNum">      35</span>              :     required this.createdAt,</span>
<span id="L36"><span class="lineNum">      36</span>              :     required this.lastUpdated,</span>
<span id="L37"><span class="lineNum">      37</span>              :     this.twoFactorEnabled = false,</span>
<span id="L38"><span class="lineNum">      38</span>              :     this.email,</span>
<span id="L39"><span class="lineNum">      39</span>              :     this.biometricEnabled = false,</span>
<span id="L40"><span class="lineNum">      40</span>              :   });</span>
<span id="L41"><span class="lineNum">      41</span>              : </span>
<span id="L42"><span class="lineNum">      42</span>              :   /// Creates an AuthSessionModel from JSON</span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaUNC">           0 :   factory AuthSessionModel.fromJson(Map&lt;String, dynamic&gt; json) =&gt; </span></span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaUNC">           0 :       _$AuthSessionModelFromJson(json);</span></span>
<span id="L45"><span class="lineNum">      45</span>              : </span>
<span id="L46"><span class="lineNum">      46</span>              :   /// Converts AuthSessionModel to JSON</span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaUNC">           0 :   Map&lt;String, dynamic&gt; toJson() =&gt; _$AuthSessionModelToJson(this);</span></span>
<span id="L48"><span class="lineNum">      48</span>              : </span>
<span id="L49"><span class="lineNum">      49</span>              :   /// Creates a new session model with updated tokens</span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaUNC">           0 :   AuthSessionModel copyWithTokens({</span></span>
<span id="L51"><span class="lineNum">      51</span>              :     required String accessToken,</span>
<span id="L52"><span class="lineNum">      52</span>              :     required String refreshToken,</span>
<span id="L53"><span class="lineNum">      53</span>              :   }) {</span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :     return AuthSessionModel(</span></span>
<span id="L55"><span class="lineNum">      55</span>              :       accessToken: accessToken,</span>
<span id="L56"><span class="lineNum">      56</span>              :       refreshToken: refreshToken,</span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaUNC">           0 :       createdAt: createdAt,</span></span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaUNC">           0 :       lastUpdated: DateTime.now().millisecondsSinceEpoch,</span></span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaUNC">           0 :       twoFactorEnabled: twoFactorEnabled,</span></span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaUNC">           0 :       email: email,</span></span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaUNC">           0 :       biometricEnabled: biometricEnabled,</span></span>
<span id="L62"><span class="lineNum">      62</span>              :     );</span>
<span id="L63"><span class="lineNum">      63</span>              :   }</span>
<span id="L64"><span class="lineNum">      64</span>              : </span>
<span id="L65"><span class="lineNum">      65</span>              :   /// Creates a new session model with updated settings</span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaUNC">           0 :   AuthSessionModel copyWith({</span></span>
<span id="L67"><span class="lineNum">      67</span>              :     String? accessToken,</span>
<span id="L68"><span class="lineNum">      68</span>              :     String? refreshToken,</span>
<span id="L69"><span class="lineNum">      69</span>              :     int? createdAt,</span>
<span id="L70"><span class="lineNum">      70</span>              :     int? lastUpdated,</span>
<span id="L71"><span class="lineNum">      71</span>              :     bool? twoFactorEnabled,</span>
<span id="L72"><span class="lineNum">      72</span>              :     String? email,</span>
<span id="L73"><span class="lineNum">      73</span>              :     bool? biometricEnabled,</span>
<span id="L74"><span class="lineNum">      74</span>              :   }) {</span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaUNC">           0 :     return AuthSessionModel(</span></span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaUNC">           0 :       accessToken: accessToken ?? this.accessToken,</span></span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :       refreshToken: refreshToken ?? this.refreshToken,</span></span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :       createdAt: createdAt ?? this.createdAt,</span></span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaUNC">           0 :       lastUpdated: lastUpdated ?? this.lastUpdated,</span></span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaUNC">           0 :       twoFactorEnabled: twoFactorEnabled ?? this.twoFactorEnabled,</span></span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaUNC">           0 :       email: email ?? this.email,</span></span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaUNC">           0 :       biometricEnabled: biometricEnabled ?? this.biometricEnabled,</span></span>
<span id="L83"><span class="lineNum">      83</span>              :     );</span>
<span id="L84"><span class="lineNum">      84</span>              :   }</span>
<span id="L85"><span class="lineNum">      85</span>              : </span>
<span id="L86"><span class="lineNum">      86</span>              :   /// Checks if the access token is expiring soon (within 5 minutes)</span>
<span id="L87"><span class="lineNum">      87</span>              :   /// </span>
<span id="L88"><span class="lineNum">      88</span>              :   /// This is a simplified check based on the existing LoginPostRequests logic.</span>
<span id="L89"><span class="lineNum">      89</span>              :   /// In a real implementation, you would decode the JWT and check the exp claim.</span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaUNC">           0 :   bool get isTokenExpiring {</span></span>
<span id="L91"><span class="lineNum">      91</span>              :     try {</span>
<span id="L92"><span class="lineNum">      92</span>              :       // For now, we'll use a simple time-based check</span>
<span id="L93"><span class="lineNum">      93</span>              :       // In production, you should decode the JWT and check the actual expiration</span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaUNC">           0 :       final now = DateTime.now().millisecondsSinceEpoch;</span></span>
<span id="L95"><span class="lineNum">      95</span> <span class="tlaUNC">           0 :       final tokenAge = now - lastUpdated;</span></span>
<span id="L96"><span class="lineNum">      96</span>              :       </span>
<span id="L97"><span class="lineNum">      97</span>              :       // Consider token expiring if it's older than 55 minutes (assuming 1-hour tokens)</span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaUNC">           0 :       return tokenAge &gt; (55 * 60 * 1000);</span></span>
<span id="L99"><span class="lineNum">      99</span>              :     } catch (e) {</span>
<span id="L100"><span class="lineNum">     100</span>              :       return true; // If we can't determine, assume it's expiring</span>
<span id="L101"><span class="lineNum">     101</span>              :     }</span>
<span id="L102"><span class="lineNum">     102</span>              :   }</span>
<span id="L103"><span class="lineNum">     103</span>              : </span>
<span id="L104"><span class="lineNum">     104</span>              :   /// Checks if the session is valid (has tokens and not expired)</span>
<span id="L105"><span class="lineNum">     105</span> <span class="tlaUNC">           0 :   bool get isValid {</span></span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaUNC">           0 :     return accessToken.isNotEmpty &amp;&amp; </span></span>
<span id="L107"><span class="lineNum">     107</span> <span class="tlaUNC">           0 :            refreshToken.isNotEmpty &amp;&amp; </span></span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaUNC">           0 :            !isTokenExpiring;</span></span>
<span id="L109"><span class="lineNum">     109</span>              :   }</span>
<span id="L110"><span class="lineNum">     110</span>              : </span>
<span id="L111"><span class="lineNum">     111</span>              :   /// Gets the age of the session in milliseconds</span>
<span id="L112"><span class="lineNum">     112</span> <span class="tlaUNC">           0 :   int get sessionAge {</span></span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaUNC">           0 :     return DateTime.now().millisecondsSinceEpoch - createdAt;</span></span>
<span id="L114"><span class="lineNum">     114</span>              :   }</span>
<span id="L115"><span class="lineNum">     115</span>              : </span>
<span id="L116"><span class="lineNum">     116</span>              :   /// Creates a new session from login response tokens</span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaUNC">           0 :   factory AuthSessionModel.fromTokens({</span></span>
<span id="L118"><span class="lineNum">     118</span>              :     required String accessToken,</span>
<span id="L119"><span class="lineNum">     119</span>              :     required String refreshToken,</span>
<span id="L120"><span class="lineNum">     120</span>              :     String? email,</span>
<span id="L121"><span class="lineNum">     121</span>              :     bool twoFactorEnabled = false,</span>
<span id="L122"><span class="lineNum">     122</span>              :     bool biometricEnabled = false,</span>
<span id="L123"><span class="lineNum">     123</span>              :   }) {</span>
<span id="L124"><span class="lineNum">     124</span> <span class="tlaUNC">           0 :     final now = DateTime.now().millisecondsSinceEpoch;</span></span>
<span id="L125"><span class="lineNum">     125</span> <span class="tlaUNC">           0 :     return AuthSessionModel(</span></span>
<span id="L126"><span class="lineNum">     126</span>              :       accessToken: accessToken,</span>
<span id="L127"><span class="lineNum">     127</span>              :       refreshToken: refreshToken,</span>
<span id="L128"><span class="lineNum">     128</span>              :       createdAt: now,</span>
<span id="L129"><span class="lineNum">     129</span>              :       lastUpdated: now,</span>
<span id="L130"><span class="lineNum">     130</span>              :       twoFactorEnabled: twoFactorEnabled,</span>
<span id="L131"><span class="lineNum">     131</span>              :       email: email,</span>
<span id="L132"><span class="lineNum">     132</span>              :       biometricEnabled: biometricEnabled,</span>
<span id="L133"><span class="lineNum">     133</span>              :     );</span>
<span id="L134"><span class="lineNum">     134</span>              :   }</span>
<span id="L135"><span class="lineNum">     135</span>              : </span>
<span id="L136"><span class="lineNum">     136</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L137"><span class="lineNum">     137</span>              :   String toString() {</span>
<span id="L138"><span class="lineNum">     138</span> <span class="tlaUNC">           0 :     return 'AuthSessionModel(email: $email, twoFactorEnabled: $twoFactorEnabled, '</span></span>
<span id="L139"><span class="lineNum">     139</span> <span class="tlaUNC">           0 :         'biometricEnabled: $biometricEnabled, isValid: $isValid, '</span></span>
<span id="L140"><span class="lineNum">     140</span> <span class="tlaUNC">           0 :         'isTokenExpiring: $isTokenExpiring)';</span></span>
<span id="L141"><span class="lineNum">     141</span>              :   }</span>
<span id="L142"><span class="lineNum">     142</span>              : </span>
<span id="L143"><span class="lineNum">     143</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L144"><span class="lineNum">     144</span>              :   bool operator ==(Object other) {</span>
<span id="L145"><span class="lineNum">     145</span>              :     if (identical(this, other)) return true;</span>
<span id="L146"><span class="lineNum">     146</span> <span class="tlaUNC">           0 :     return other is AuthSessionModel &amp;&amp;</span></span>
<span id="L147"><span class="lineNum">     147</span> <span class="tlaUNC">           0 :         other.accessToken == accessToken &amp;&amp;</span></span>
<span id="L148"><span class="lineNum">     148</span> <span class="tlaUNC">           0 :         other.refreshToken == refreshToken &amp;&amp;</span></span>
<span id="L149"><span class="lineNum">     149</span> <span class="tlaUNC">           0 :         other.createdAt == createdAt &amp;&amp;</span></span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaUNC">           0 :         other.lastUpdated == lastUpdated &amp;&amp;</span></span>
<span id="L151"><span class="lineNum">     151</span> <span class="tlaUNC">           0 :         other.twoFactorEnabled == twoFactorEnabled &amp;&amp;</span></span>
<span id="L152"><span class="lineNum">     152</span> <span class="tlaUNC">           0 :         other.email == email &amp;&amp;</span></span>
<span id="L153"><span class="lineNum">     153</span> <span class="tlaUNC">           0 :         other.biometricEnabled == biometricEnabled;</span></span>
<span id="L154"><span class="lineNum">     154</span>              :   }</span>
<span id="L155"><span class="lineNum">     155</span>              : </span>
<span id="L156"><span class="lineNum">     156</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L157"><span class="lineNum">     157</span>              :   int get hashCode {</span>
<span id="L158"><span class="lineNum">     158</span> <span class="tlaUNC">           0 :     return Object.hash(</span></span>
<span id="L159"><span class="lineNum">     159</span> <span class="tlaUNC">           0 :       accessToken,</span></span>
<span id="L160"><span class="lineNum">     160</span> <span class="tlaUNC">           0 :       refreshToken,</span></span>
<span id="L161"><span class="lineNum">     161</span> <span class="tlaUNC">           0 :       createdAt,</span></span>
<span id="L162"><span class="lineNum">     162</span> <span class="tlaUNC">           0 :       lastUpdated,</span></span>
<span id="L163"><span class="lineNum">     163</span> <span class="tlaUNC">           0 :       twoFactorEnabled,</span></span>
<span id="L164"><span class="lineNum">     164</span> <span class="tlaUNC">           0 :       email,</span></span>
<span id="L165"><span class="lineNum">     165</span> <span class="tlaUNC">           0 :       biometricEnabled,</span></span>
<span id="L166"><span class="lineNum">     166</span>              :     );</span>
<span id="L167"><span class="lineNum">     167</span>              :   }</span>
<span id="L168"><span class="lineNum">     168</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
