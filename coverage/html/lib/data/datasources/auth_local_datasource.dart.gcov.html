<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/datasources/auth_local_datasource.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/datasources">lib/data/datasources</a> - auth_local_datasource.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">72</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-07-03 12:38:23</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'dart:convert';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_secure_storage/flutter_secure_storage.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:local_auth/local_auth.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import '../models/auth_session_model.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import '../models/auth_user_model.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import '../../domain/services/biometric_service.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : </span>
<span id="L8"><span class="lineNum">       8</span>              : /// Abstract interface for local authentication data operations</span>
<span id="L9"><span class="lineNum">       9</span>              : abstract class AuthLocalDataSource {</span>
<span id="L10"><span class="lineNum">      10</span>              :   /// Session management</span>
<span id="L11"><span class="lineNum">      11</span>              :   Future&lt;void&gt; saveSession(AuthSessionModel session);</span>
<span id="L12"><span class="lineNum">      12</span>              :   Future&lt;AuthSessionModel?&gt; getSession();</span>
<span id="L13"><span class="lineNum">      13</span>              :   Future&lt;void&gt; clearSession();</span>
<span id="L14"><span class="lineNum">      14</span>              : </span>
<span id="L15"><span class="lineNum">      15</span>              :   /// Token management</span>
<span id="L16"><span class="lineNum">      16</span>              :   Future&lt;void&gt; saveTokens(String accessToken, String refreshToken);</span>
<span id="L17"><span class="lineNum">      17</span>              :   Future&lt;String?&gt; getAccessToken();</span>
<span id="L18"><span class="lineNum">      18</span>              :   Future&lt;String?&gt; getRefreshToken();</span>
<span id="L19"><span class="lineNum">      19</span>              :   Future&lt;void&gt; clearTokens();</span>
<span id="L20"><span class="lineNum">      20</span>              : </span>
<span id="L21"><span class="lineNum">      21</span>              :   /// User data management</span>
<span id="L22"><span class="lineNum">      22</span>              :   Future&lt;void&gt; saveUser(AuthUserModel user);</span>
<span id="L23"><span class="lineNum">      23</span>              :   Future&lt;AuthUserModel?&gt; getUser();</span>
<span id="L24"><span class="lineNum">      24</span>              :   Future&lt;void&gt; clearUser();</span>
<span id="L25"><span class="lineNum">      25</span>              : </span>
<span id="L26"><span class="lineNum">      26</span>              :   /// Credentials management for biometric login</span>
<span id="L27"><span class="lineNum">      27</span>              :   Future&lt;void&gt; saveCredentials(String email, String password);</span>
<span id="L28"><span class="lineNum">      28</span>              :   Future&lt;Map&lt;String, String&gt;?&gt; getCredentials();</span>
<span id="L29"><span class="lineNum">      29</span>              :   Future&lt;void&gt; clearCredentials();</span>
<span id="L30"><span class="lineNum">      30</span>              : </span>
<span id="L31"><span class="lineNum">      31</span>              :   /// Biometric authentication</span>
<span id="L32"><span class="lineNum">      32</span>              :   Future&lt;bool&gt; isBiometricAvailable();</span>
<span id="L33"><span class="lineNum">      33</span>              :   Future&lt;bool&gt; isBiometricEnabled();</span>
<span id="L34"><span class="lineNum">      34</span>              :   Future&lt;void&gt; setBiometricEnabled(bool enabled);</span>
<span id="L35"><span class="lineNum">      35</span>              :   Future&lt;bool&gt; authenticateWithBiometric({String? reason});</span>
<span id="L36"><span class="lineNum">      36</span>              : </span>
<span id="L37"><span class="lineNum">      37</span>              :   /// Two-factor authentication settings</span>
<span id="L38"><span class="lineNum">      38</span>              :   Future&lt;void&gt; setTwoFactorEnabled(bool enabled);</span>
<span id="L39"><span class="lineNum">      39</span>              :   Future&lt;bool&gt; isTwoFactorEnabled();</span>
<span id="L40"><span class="lineNum">      40</span>              : </span>
<span id="L41"><span class="lineNum">      41</span>              :   /// Theme and app settings</span>
<span id="L42"><span class="lineNum">      42</span>              :   Future&lt;void&gt; saveThemeMode(String themeMode);</span>
<span id="L43"><span class="lineNum">      43</span>              :   Future&lt;String?&gt; getThemeMode();</span>
<span id="L44"><span class="lineNum">      44</span>              : </span>
<span id="L45"><span class="lineNum">      45</span>              :   /// Clear all stored data</span>
<span id="L46"><span class="lineNum">      46</span>              :   Future&lt;void&gt; clearAllData();</span>
<span id="L47"><span class="lineNum">      47</span>              : }</span>
<span id="L48"><span class="lineNum">      48</span>              : </span>
<span id="L49"><span class="lineNum">      49</span>              : /// Implementation of AuthLocalDataSource using FlutterSecureStorage and LocalAuthentication</span>
<span id="L50"><span class="lineNum">      50</span>              : class AuthLocalDataSourceImpl implements AuthLocalDataSource {</span>
<span id="L51"><span class="lineNum">      51</span>              :   final FlutterSecureStorage secureStorage;</span>
<span id="L52"><span class="lineNum">      52</span>              :   final LocalAuthentication localAuth;</span>
<span id="L53"><span class="lineNum">      53</span>              :   final BiometricService biometricService;</span>
<span id="L54"><span class="lineNum">      54</span>              : </span>
<span id="L55"><span class="lineNum">      55</span>              :   // Storage keys</span>
<span id="L56"><span class="lineNum">      56</span>              :   static const String _sessionKey = 'auth_session';</span>
<span id="L57"><span class="lineNum">      57</span>              :   static const String _userKey = 'auth_user';</span>
<span id="L58"><span class="lineNum">      58</span>              :   static const String _accessTokenKey = 'access_token';</span>
<span id="L59"><span class="lineNum">      59</span>              :   static const String _refreshTokenKey = 'refresh_token';</span>
<span id="L60"><span class="lineNum">      60</span>              :   static const String _emailKey = 'email';</span>
<span id="L61"><span class="lineNum">      61</span>              :   static const String _passwordKey = 'password';</span>
<span id="L62"><span class="lineNum">      62</span>              : </span>
<span id="L63"><span class="lineNum">      63</span>              :   static const String _twoFactorKey = 'two_factor';</span>
<span id="L64"><span class="lineNum">      64</span>              :   static const String _themeModeKey = 'themeMode';</span>
<span id="L65"><span class="lineNum">      65</span>              : </span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaUNC">           0 :   const AuthLocalDataSourceImpl({</span></span>
<span id="L67"><span class="lineNum">      67</span>              :     required this.secureStorage,</span>
<span id="L68"><span class="lineNum">      68</span>              :     required this.localAuth,</span>
<span id="L69"><span class="lineNum">      69</span>              :     required this.biometricService,</span>
<span id="L70"><span class="lineNum">      70</span>              :   });</span>
<span id="L71"><span class="lineNum">      71</span>              : </span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L73"><span class="lineNum">      73</span>              :   Future&lt;void&gt; saveSession(AuthSessionModel session) async {</span>
<span id="L74"><span class="lineNum">      74</span>              :     try {</span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaUNC">           0 :       final sessionJson = json.encode(session.toJson());</span></span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaUNC">           0 :       await secureStorage.write(key: _sessionKey, value: sessionJson);</span></span>
<span id="L77"><span class="lineNum">      77</span>              :     } catch (e) {</span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :       throw Exception('Failed to save session: $e');</span></span>
<span id="L79"><span class="lineNum">      79</span>              :     }</span>
<span id="L80"><span class="lineNum">      80</span>              :   }</span>
<span id="L81"><span class="lineNum">      81</span>              : </span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L83"><span class="lineNum">      83</span>              :   Future&lt;AuthSessionModel?&gt; getSession() async {</span>
<span id="L84"><span class="lineNum">      84</span>              :     try {</span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaUNC">           0 :       final sessionJson = await secureStorage.read(key: _sessionKey);</span></span>
<span id="L86"><span class="lineNum">      86</span>              :       if (sessionJson == null) return null;</span>
<span id="L87"><span class="lineNum">      87</span>              : </span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaUNC">           0 :       final sessionMap = json.decode(sessionJson) as Map&lt;String, dynamic&gt;;</span></span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaUNC">           0 :       return AuthSessionModel.fromJson(sessionMap);</span></span>
<span id="L90"><span class="lineNum">      90</span>              :     } catch (e) {</span>
<span id="L91"><span class="lineNum">      91</span>              :       // If we can't parse the session, return null</span>
<span id="L92"><span class="lineNum">      92</span>              :       return null;</span>
<span id="L93"><span class="lineNum">      93</span>              :     }</span>
<span id="L94"><span class="lineNum">      94</span>              :   }</span>
<span id="L95"><span class="lineNum">      95</span>              : </span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L97"><span class="lineNum">      97</span>              :   Future&lt;void&gt; clearSession() async {</span>
<span id="L98"><span class="lineNum">      98</span>              :     try {</span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaUNC">           0 :       await secureStorage.delete(key: _sessionKey);</span></span>
<span id="L100"><span class="lineNum">     100</span>              :     } catch (e) {</span>
<span id="L101"><span class="lineNum">     101</span>              :       // Ignore errors when clearing</span>
<span id="L102"><span class="lineNum">     102</span>              :     }</span>
<span id="L103"><span class="lineNum">     103</span>              :   }</span>
<span id="L104"><span class="lineNum">     104</span>              : </span>
<span id="L105"><span class="lineNum">     105</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L106"><span class="lineNum">     106</span>              :   Future&lt;void&gt; saveTokens(String accessToken, String refreshToken) async {</span>
<span id="L107"><span class="lineNum">     107</span>              :     try {</span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaUNC">           0 :       await Future.wait([</span></span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaUNC">           0 :         secureStorage.write(key: _accessTokenKey, value: accessToken),</span></span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaUNC">           0 :         secureStorage.write(key: _refreshTokenKey, value: refreshToken),</span></span>
<span id="L111"><span class="lineNum">     111</span>              :       ]);</span>
<span id="L112"><span class="lineNum">     112</span>              :     } catch (e) {</span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaUNC">           0 :       throw Exception('Failed to save tokens: $e');</span></span>
<span id="L114"><span class="lineNum">     114</span>              :     }</span>
<span id="L115"><span class="lineNum">     115</span>              :   }</span>
<span id="L116"><span class="lineNum">     116</span>              : </span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L118"><span class="lineNum">     118</span>              :   Future&lt;String?&gt; getAccessToken() async {</span>
<span id="L119"><span class="lineNum">     119</span>              :     try {</span>
<span id="L120"><span class="lineNum">     120</span> <span class="tlaUNC">           0 :       return await secureStorage.read(key: _accessTokenKey);</span></span>
<span id="L121"><span class="lineNum">     121</span>              :     } catch (e) {</span>
<span id="L122"><span class="lineNum">     122</span>              :       return null;</span>
<span id="L123"><span class="lineNum">     123</span>              :     }</span>
<span id="L124"><span class="lineNum">     124</span>              :   }</span>
<span id="L125"><span class="lineNum">     125</span>              : </span>
<span id="L126"><span class="lineNum">     126</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L127"><span class="lineNum">     127</span>              :   Future&lt;String?&gt; getRefreshToken() async {</span>
<span id="L128"><span class="lineNum">     128</span>              :     try {</span>
<span id="L129"><span class="lineNum">     129</span> <span class="tlaUNC">           0 :       return await secureStorage.read(key: _refreshTokenKey);</span></span>
<span id="L130"><span class="lineNum">     130</span>              :     } catch (e) {</span>
<span id="L131"><span class="lineNum">     131</span>              :       return null;</span>
<span id="L132"><span class="lineNum">     132</span>              :     }</span>
<span id="L133"><span class="lineNum">     133</span>              :   }</span>
<span id="L134"><span class="lineNum">     134</span>              : </span>
<span id="L135"><span class="lineNum">     135</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L136"><span class="lineNum">     136</span>              :   Future&lt;void&gt; clearTokens() async {</span>
<span id="L137"><span class="lineNum">     137</span>              :     try {</span>
<span id="L138"><span class="lineNum">     138</span> <span class="tlaUNC">           0 :       await Future.wait([</span></span>
<span id="L139"><span class="lineNum">     139</span> <span class="tlaUNC">           0 :         secureStorage.delete(key: _accessTokenKey),</span></span>
<span id="L140"><span class="lineNum">     140</span> <span class="tlaUNC">           0 :         secureStorage.delete(key: _refreshTokenKey),</span></span>
<span id="L141"><span class="lineNum">     141</span>              :       ]);</span>
<span id="L142"><span class="lineNum">     142</span>              :     } catch (e) {</span>
<span id="L143"><span class="lineNum">     143</span>              :       // Ignore errors when clearing</span>
<span id="L144"><span class="lineNum">     144</span>              :     }</span>
<span id="L145"><span class="lineNum">     145</span>              :   }</span>
<span id="L146"><span class="lineNum">     146</span>              : </span>
<span id="L147"><span class="lineNum">     147</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L148"><span class="lineNum">     148</span>              :   Future&lt;void&gt; saveUser(AuthUserModel user) async {</span>
<span id="L149"><span class="lineNum">     149</span>              :     try {</span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaUNC">           0 :       final userJson = json.encode(user.toJson());</span></span>
<span id="L151"><span class="lineNum">     151</span> <span class="tlaUNC">           0 :       await secureStorage.write(key: _userKey, value: userJson);</span></span>
<span id="L152"><span class="lineNum">     152</span>              :     } catch (e) {</span>
<span id="L153"><span class="lineNum">     153</span> <span class="tlaUNC">           0 :       throw Exception('Failed to save user: $e');</span></span>
<span id="L154"><span class="lineNum">     154</span>              :     }</span>
<span id="L155"><span class="lineNum">     155</span>              :   }</span>
<span id="L156"><span class="lineNum">     156</span>              : </span>
<span id="L157"><span class="lineNum">     157</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L158"><span class="lineNum">     158</span>              :   Future&lt;AuthUserModel?&gt; getUser() async {</span>
<span id="L159"><span class="lineNum">     159</span>              :     try {</span>
<span id="L160"><span class="lineNum">     160</span> <span class="tlaUNC">           0 :       final userJson = await secureStorage.read(key: _userKey);</span></span>
<span id="L161"><span class="lineNum">     161</span>              :       if (userJson == null) return null;</span>
<span id="L162"><span class="lineNum">     162</span>              : </span>
<span id="L163"><span class="lineNum">     163</span> <span class="tlaUNC">           0 :       final userMap = json.decode(userJson) as Map&lt;String, dynamic&gt;;</span></span>
<span id="L164"><span class="lineNum">     164</span> <span class="tlaUNC">           0 :       return AuthUserModel.fromJson(userMap);</span></span>
<span id="L165"><span class="lineNum">     165</span>              :     } catch (e) {</span>
<span id="L166"><span class="lineNum">     166</span>              :       return null;</span>
<span id="L167"><span class="lineNum">     167</span>              :     }</span>
<span id="L168"><span class="lineNum">     168</span>              :   }</span>
<span id="L169"><span class="lineNum">     169</span>              : </span>
<span id="L170"><span class="lineNum">     170</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L171"><span class="lineNum">     171</span>              :   Future&lt;void&gt; clearUser() async {</span>
<span id="L172"><span class="lineNum">     172</span>              :     try {</span>
<span id="L173"><span class="lineNum">     173</span> <span class="tlaUNC">           0 :       await secureStorage.delete(key: _userKey);</span></span>
<span id="L174"><span class="lineNum">     174</span>              :     } catch (e) {</span>
<span id="L175"><span class="lineNum">     175</span>              :       // Ignore errors when clearing</span>
<span id="L176"><span class="lineNum">     176</span>              :     }</span>
<span id="L177"><span class="lineNum">     177</span>              :   }</span>
<span id="L178"><span class="lineNum">     178</span>              : </span>
<span id="L179"><span class="lineNum">     179</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L180"><span class="lineNum">     180</span>              :   Future&lt;void&gt; saveCredentials(String email, String password) async {</span>
<span id="L181"><span class="lineNum">     181</span>              :     try {</span>
<span id="L182"><span class="lineNum">     182</span> <span class="tlaUNC">           0 :       await Future.wait([</span></span>
<span id="L183"><span class="lineNum">     183</span> <span class="tlaUNC">           0 :         secureStorage.write(key: _emailKey, value: email),</span></span>
<span id="L184"><span class="lineNum">     184</span> <span class="tlaUNC">           0 :         secureStorage.write(key: _passwordKey, value: password),</span></span>
<span id="L185"><span class="lineNum">     185</span>              :       ]);</span>
<span id="L186"><span class="lineNum">     186</span>              :     } catch (e) {</span>
<span id="L187"><span class="lineNum">     187</span> <span class="tlaUNC">           0 :       throw Exception('Failed to save credentials: $e');</span></span>
<span id="L188"><span class="lineNum">     188</span>              :     }</span>
<span id="L189"><span class="lineNum">     189</span>              :   }</span>
<span id="L190"><span class="lineNum">     190</span>              : </span>
<span id="L191"><span class="lineNum">     191</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L192"><span class="lineNum">     192</span>              :   Future&lt;Map&lt;String, String&gt;?&gt; getCredentials() async {</span>
<span id="L193"><span class="lineNum">     193</span>              :     try {</span>
<span id="L194"><span class="lineNum">     194</span> <span class="tlaUNC">           0 :       final email = await secureStorage.read(key: _emailKey);</span></span>
<span id="L195"><span class="lineNum">     195</span> <span class="tlaUNC">           0 :       final password = await secureStorage.read(key: _passwordKey);</span></span>
<span id="L196"><span class="lineNum">     196</span>              : </span>
<span id="L197"><span class="lineNum">     197</span>              :       if (email == null || password == null) return null;</span>
<span id="L198"><span class="lineNum">     198</span>              : </span>
<span id="L199"><span class="lineNum">     199</span> <span class="tlaUNC">           0 :       return {'email': email, 'password': password};</span></span>
<span id="L200"><span class="lineNum">     200</span>              :     } catch (e) {</span>
<span id="L201"><span class="lineNum">     201</span>              :       return null;</span>
<span id="L202"><span class="lineNum">     202</span>              :     }</span>
<span id="L203"><span class="lineNum">     203</span>              :   }</span>
<span id="L204"><span class="lineNum">     204</span>              : </span>
<span id="L205"><span class="lineNum">     205</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L206"><span class="lineNum">     206</span>              :   Future&lt;void&gt; clearCredentials() async {</span>
<span id="L207"><span class="lineNum">     207</span>              :     try {</span>
<span id="L208"><span class="lineNum">     208</span> <span class="tlaUNC">           0 :       await Future.wait([</span></span>
<span id="L209"><span class="lineNum">     209</span> <span class="tlaUNC">           0 :         secureStorage.delete(key: _emailKey),</span></span>
<span id="L210"><span class="lineNum">     210</span> <span class="tlaUNC">           0 :         secureStorage.delete(key: _passwordKey),</span></span>
<span id="L211"><span class="lineNum">     211</span>              :       ]);</span>
<span id="L212"><span class="lineNum">     212</span>              :     } catch (e) {</span>
<span id="L213"><span class="lineNum">     213</span>              :       // Ignore errors when clearing</span>
<span id="L214"><span class="lineNum">     214</span>              :     }</span>
<span id="L215"><span class="lineNum">     215</span>              :   }</span>
<span id="L216"><span class="lineNum">     216</span>              : </span>
<span id="L217"><span class="lineNum">     217</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L218"><span class="lineNum">     218</span>              :   Future&lt;bool&gt; isBiometricAvailable() async {</span>
<span id="L219"><span class="lineNum">     219</span> <span class="tlaUNC">           0 :     return await biometricService.canUseBiometric();</span></span>
<span id="L220"><span class="lineNum">     220</span>              :   }</span>
<span id="L221"><span class="lineNum">     221</span>              : </span>
<span id="L222"><span class="lineNum">     222</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L223"><span class="lineNum">     223</span>              :   Future&lt;bool&gt; isBiometricEnabled() async {</span>
<span id="L224"><span class="lineNum">     224</span> <span class="tlaUNC">           0 :     return await biometricService.isBiometricEnabled();</span></span>
<span id="L225"><span class="lineNum">     225</span>              :   }</span>
<span id="L226"><span class="lineNum">     226</span>              : </span>
<span id="L227"><span class="lineNum">     227</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L228"><span class="lineNum">     228</span>              :   Future&lt;void&gt; setBiometricEnabled(bool enabled) async {</span>
<span id="L229"><span class="lineNum">     229</span> <span class="tlaUNC">           0 :     final result = await biometricService.setBiometricEnabled(enabled);</span></span>
<span id="L230"><span class="lineNum">     230</span> <span class="tlaUNC">           0 :     if (!result.success) {</span></span>
<span id="L231"><span class="lineNum">     231</span> <span class="tlaUNC">           0 :       throw Exception(</span></span>
<span id="L232"><span class="lineNum">     232</span> <span class="tlaUNC">           0 :           'Failed to set biometric enabled: ${result.failure?.message}');</span></span>
<span id="L233"><span class="lineNum">     233</span>              :     }</span>
<span id="L234"><span class="lineNum">     234</span>              :   }</span>
<span id="L235"><span class="lineNum">     235</span>              : </span>
<span id="L236"><span class="lineNum">     236</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L237"><span class="lineNum">     237</span>              :   Future&lt;bool&gt; authenticateWithBiometric({String? reason}) async {</span>
<span id="L238"><span class="lineNum">     238</span> <span class="tlaUNC">           0 :     final result = await biometricService.authenticate(</span></span>
<span id="L239"><span class="lineNum">     239</span>              :       reason: reason ?? 'Please authenticate to proceed',</span>
<span id="L240"><span class="lineNum">     240</span>              :     );</span>
<span id="L241"><span class="lineNum">     241</span> <span class="tlaUNC">           0 :     return result.success;</span></span>
<span id="L242"><span class="lineNum">     242</span>              :   }</span>
<span id="L243"><span class="lineNum">     243</span>              : </span>
<span id="L244"><span class="lineNum">     244</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L245"><span class="lineNum">     245</span>              :   Future&lt;void&gt; setTwoFactorEnabled(bool enabled) async {</span>
<span id="L246"><span class="lineNum">     246</span>              :     try {</span>
<span id="L247"><span class="lineNum">     247</span> <span class="tlaUNC">           0 :       await secureStorage.write(key: _twoFactorKey, value: enabled.toString());</span></span>
<span id="L248"><span class="lineNum">     248</span>              :     } catch (e) {</span>
<span id="L249"><span class="lineNum">     249</span> <span class="tlaUNC">           0 :       throw Exception('Failed to set two factor enabled: $e');</span></span>
<span id="L250"><span class="lineNum">     250</span>              :     }</span>
<span id="L251"><span class="lineNum">     251</span>              :   }</span>
<span id="L252"><span class="lineNum">     252</span>              : </span>
<span id="L253"><span class="lineNum">     253</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L254"><span class="lineNum">     254</span>              :   Future&lt;bool&gt; isTwoFactorEnabled() async {</span>
<span id="L255"><span class="lineNum">     255</span>              :     try {</span>
<span id="L256"><span class="lineNum">     256</span> <span class="tlaUNC">           0 :       final twoFactorValue = await secureStorage.read(key: _twoFactorKey);</span></span>
<span id="L257"><span class="lineNum">     257</span> <span class="tlaUNC">           0 :       return twoFactorValue == 'true';</span></span>
<span id="L258"><span class="lineNum">     258</span>              :     } catch (e) {</span>
<span id="L259"><span class="lineNum">     259</span>              :       return false;</span>
<span id="L260"><span class="lineNum">     260</span>              :     }</span>
<span id="L261"><span class="lineNum">     261</span>              :   }</span>
<span id="L262"><span class="lineNum">     262</span>              : </span>
<span id="L263"><span class="lineNum">     263</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L264"><span class="lineNum">     264</span>              :   Future&lt;void&gt; saveThemeMode(String themeMode) async {</span>
<span id="L265"><span class="lineNum">     265</span>              :     try {</span>
<span id="L266"><span class="lineNum">     266</span> <span class="tlaUNC">           0 :       await secureStorage.write(key: _themeModeKey, value: themeMode);</span></span>
<span id="L267"><span class="lineNum">     267</span>              :     } catch (e) {</span>
<span id="L268"><span class="lineNum">     268</span> <span class="tlaUNC">           0 :       throw Exception('Failed to save theme mode: $e');</span></span>
<span id="L269"><span class="lineNum">     269</span>              :     }</span>
<span id="L270"><span class="lineNum">     270</span>              :   }</span>
<span id="L271"><span class="lineNum">     271</span>              : </span>
<span id="L272"><span class="lineNum">     272</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L273"><span class="lineNum">     273</span>              :   Future&lt;String?&gt; getThemeMode() async {</span>
<span id="L274"><span class="lineNum">     274</span>              :     try {</span>
<span id="L275"><span class="lineNum">     275</span> <span class="tlaUNC">           0 :       return await secureStorage.read(key: _themeModeKey);</span></span>
<span id="L276"><span class="lineNum">     276</span>              :     } catch (e) {</span>
<span id="L277"><span class="lineNum">     277</span>              :       return null;</span>
<span id="L278"><span class="lineNum">     278</span>              :     }</span>
<span id="L279"><span class="lineNum">     279</span>              :   }</span>
<span id="L280"><span class="lineNum">     280</span>              : </span>
<span id="L281"><span class="lineNum">     281</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L282"><span class="lineNum">     282</span>              :   Future&lt;void&gt; clearAllData() async {</span>
<span id="L283"><span class="lineNum">     283</span>              :     try {</span>
<span id="L284"><span class="lineNum">     284</span> <span class="tlaUNC">           0 :       await secureStorage.deleteAll();</span></span>
<span id="L285"><span class="lineNum">     285</span>              :     } catch (e) {</span>
<span id="L286"><span class="lineNum">     286</span>              :       // Ignore errors when clearing all data</span>
<span id="L287"><span class="lineNum">     287</span>              :     }</span>
<span id="L288"><span class="lineNum">     288</span>              :   }</span>
<span id="L289"><span class="lineNum">     289</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
