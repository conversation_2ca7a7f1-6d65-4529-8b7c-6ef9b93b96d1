<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/datasources/auth_remote_datasource.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/datasources">lib/data/datasources</a> - auth_remote_datasource.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">108</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-07-03 12:38:23</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'dart:convert';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'dart:io';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:http/http.dart' as http;</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:connectivity_plus/connectivity_plus.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import '../models/auth_user_model.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import '../../view_model/custom_exception.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import '../../utils/getDeviceID.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : </span>
<span id="L9"><span class="lineNum">       9</span>              : /// Result of a login operation</span>
<span id="L10"><span class="lineNum">      10</span>              : class LoginResult {</span>
<span id="L11"><span class="lineNum">      11</span>              :   final bool success;</span>
<span id="L12"><span class="lineNum">      12</span>              :   final String? twoFactorRefCode;</span>
<span id="L13"><span class="lineNum">      13</span>              :   final Map&lt;String, String&gt;? tokens;</span>
<span id="L14"><span class="lineNum">      14</span>              :   final String? error;</span>
<span id="L15"><span class="lineNum">      15</span>              : </span>
<span id="L16"><span class="lineNum">      16</span> <span class="tlaUNC">           0 :   const LoginResult({</span></span>
<span id="L17"><span class="lineNum">      17</span>              :     required this.success,</span>
<span id="L18"><span class="lineNum">      18</span>              :     this.twoFactorRefCode,</span>
<span id="L19"><span class="lineNum">      19</span>              :     this.tokens,</span>
<span id="L20"><span class="lineNum">      20</span>              :     this.error,</span>
<span id="L21"><span class="lineNum">      21</span>              :   });</span>
<span id="L22"><span class="lineNum">      22</span>              : </span>
<span id="L23"><span class="lineNum">      23</span> <span class="tlaUNC">           0 :   factory LoginResult.success(Map&lt;String, String&gt; tokens) {</span></span>
<span id="L24"><span class="lineNum">      24</span> <span class="tlaUNC">           0 :     return LoginResult(success: true, tokens: tokens);</span></span>
<span id="L25"><span class="lineNum">      25</span>              :   }</span>
<span id="L26"><span class="lineNum">      26</span>              : </span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaUNC">           0 :   factory LoginResult.twoFactorRequired(String refCode) {</span></span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaUNC">           0 :     return LoginResult(success: false, twoFactorRefCode: refCode);</span></span>
<span id="L29"><span class="lineNum">      29</span>              :   }</span>
<span id="L30"><span class="lineNum">      30</span>              : </span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaUNC">           0 :   factory LoginResult.failure(String error) {</span></span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaUNC">           0 :     return LoginResult(success: false, error: error);</span></span>
<span id="L33"><span class="lineNum">      33</span>              :   }</span>
<span id="L34"><span class="lineNum">      34</span>              : }</span>
<span id="L35"><span class="lineNum">      35</span>              : </span>
<span id="L36"><span class="lineNum">      36</span>              : /// Abstract interface for remote authentication data operations</span>
<span id="L37"><span class="lineNum">      37</span>              : abstract class AuthRemoteDataSource {</span>
<span id="L38"><span class="lineNum">      38</span>              :   /// Authentication operations</span>
<span id="L39"><span class="lineNum">      39</span>              :   Future&lt;LoginResult&gt; login(String email, String password);</span>
<span id="L40"><span class="lineNum">      40</span>              :   Future&lt;void&gt; logout();</span>
<span id="L41"><span class="lineNum">      41</span>              :   Future&lt;Map&lt;String, String&gt;&gt; verifyTwoFactor(String refCode, String code);</span>
<span id="L42"><span class="lineNum">      42</span>              :   Future&lt;Map&lt;String, String&gt;&gt; refreshToken(String refreshToken);</span>
<span id="L43"><span class="lineNum">      43</span>              : </span>
<span id="L44"><span class="lineNum">      44</span>              :   /// User data operations</span>
<span id="L45"><span class="lineNum">      45</span>              :   Future&lt;AuthUserModel&gt; getUserInfo();</span>
<span id="L46"><span class="lineNum">      46</span>              : </span>
<span id="L47"><span class="lineNum">      47</span>              :   /// Account management</span>
<span id="L48"><span class="lineNum">      48</span>              :   Future&lt;void&gt; updateUserInfo({</span>
<span id="L49"><span class="lineNum">      49</span>              :     required String oldPassword,</span>
<span id="L50"><span class="lineNum">      50</span>              :     required String fullName,</span>
<span id="L51"><span class="lineNum">      51</span>              :     required String email,</span>
<span id="L52"><span class="lineNum">      52</span>              :     required String phone,</span>
<span id="L53"><span class="lineNum">      53</span>              :     required String newPassword,</span>
<span id="L54"><span class="lineNum">      54</span>              :   });</span>
<span id="L55"><span class="lineNum">      55</span>              : }</span>
<span id="L56"><span class="lineNum">      56</span>              : </span>
<span id="L57"><span class="lineNum">      57</span>              : /// Implementation of AuthRemoteDataSource using the existing API protocol</span>
<span id="L58"><span class="lineNum">      58</span>              : class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {</span>
<span id="L59"><span class="lineNum">      59</span>              :   final http.Client httpClient;</span>
<span id="L60"><span class="lineNum">      60</span>              : </span>
<span id="L61"><span class="lineNum">      61</span>              :   // API endpoints</span>
<span id="L62"><span class="lineNum">      62</span>              :   static const String _au1Url = 'https://api.nudron.com/prod/au1';</span>
<span id="L63"><span class="lineNum">      63</span>              :   static const String _au3Url = 'https://api.nudron.com/prod/au3';</span>
<span id="L64"><span class="lineNum">      64</span>              : </span>
<span id="L65"><span class="lineNum">      65</span>              :   // API constants</span>
<span id="L66"><span class="lineNum">      66</span>              :   static const String _tenantId = &quot;d14b3819-5e90-4b1e-8821-9fcb72684627&quot;;</span>
<span id="L67"><span class="lineNum">      67</span>              :   static const String _clientId = &quot;WaterMeteringMobile2&quot;;</span>
<span id="L68"><span class="lineNum">      68</span>              : </span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :   const AuthRemoteDataSourceImpl({</span></span>
<span id="L70"><span class="lineNum">      70</span>              :     required this.httpClient,</span>
<span id="L71"><span class="lineNum">      71</span>              :   });</span>
<span id="L72"><span class="lineNum">      72</span>              : </span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L74"><span class="lineNum">      74</span>              :   Future&lt;LoginResult&gt; login(String email, String password) async {</span>
<span id="L75"><span class="lineNum">      75</span>              :     try {</span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaUNC">           0 :       final passwordBase64 = base64.encode(utf8.encode(password));</span></span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :       final body = '02$email|$passwordBase64';</span></span>
<span id="L78"><span class="lineNum">      78</span>              : </span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaUNC">           0 :       final response = await _makeRequest(body, url: _au1Url);</span></span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaUNC">           0 :       final splitResponse = response.split('|');</span></span>
<span id="L81"><span class="lineNum">      81</span>              : </span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaUNC">           0 :       if (response == '0') {</span></span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaUNC">           0 :         return LoginResult.failure('Incorrect email or password');</span></span>
<span id="L84"><span class="lineNum">      84</span> <span class="tlaUNC">           0 :       } else if (response == '10' || response == '01' || response == '00') {</span></span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaUNC">           0 :         return LoginResult.failure('Email or phone unverified');</span></span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaUNC">           0 :       } else if (splitResponse.length == 2) {</span></span>
<span id="L87"><span class="lineNum">      87</span>              :         // Login successful - tokens returned</span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaUNC">           0 :         return LoginResult.success({</span></span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaUNC">           0 :           'access_token': splitResponse[0],</span></span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaUNC">           0 :           'refresh_token': splitResponse[1],</span></span>
<span id="L91"><span class="lineNum">      91</span>              :         });</span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaUNC">           0 :       } else if (splitResponse.length == 1) {</span></span>
<span id="L93"><span class="lineNum">      93</span>              :         // Two-factor authentication required</span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaUNC">           0 :         return LoginResult.twoFactorRequired(response);</span></span>
<span id="L95"><span class="lineNum">      95</span>              :       } else {</span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaUNC">           0 :         return LoginResult.failure('Unexpected response');</span></span>
<span id="L97"><span class="lineNum">      97</span>              :       }</span>
<span id="L98"><span class="lineNum">      98</span>              :     } catch (e) {</span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaUNC">           0 :       if (e is CustomException) {</span></span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaUNC">           0 :         return LoginResult.failure(e.toString());</span></span>
<span id="L101"><span class="lineNum">     101</span>              :       }</span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaUNC">           0 :       return LoginResult.failure('Login failed: ${e.toString()}');</span></span>
<span id="L103"><span class="lineNum">     103</span>              :     }</span>
<span id="L104"><span class="lineNum">     104</span>              :   }</span>
<span id="L105"><span class="lineNum">     105</span>              : </span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L107"><span class="lineNum">     107</span>              :   Future&lt;void&gt; logout() async {</span>
<span id="L108"><span class="lineNum">     108</span>              :     try {</span>
<span id="L109"><span class="lineNum">     109</span>              :       const body = '08';</span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaUNC">           0 :       final response = await _makeRequest(body, url: _au3Url);</span></span>
<span id="L111"><span class="lineNum">     111</span>              : </span>
<span id="L112"><span class="lineNum">     112</span> <span class="tlaUNC">           0 :       if (response == '0') {</span></span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaUNC">           0 :         throw CustomException('Error processing request');</span></span>
<span id="L114"><span class="lineNum">     114</span>              :       }</span>
<span id="L115"><span class="lineNum">     115</span>              :     } catch (e) {</span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaUNC">           0 :       if (e is CustomException) rethrow;</span></span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaUNC">           0 :       throw CustomException('Logout failed: ${e.toString()}');</span></span>
<span id="L118"><span class="lineNum">     118</span>              :     }</span>
<span id="L119"><span class="lineNum">     119</span>              :   }</span>
<span id="L120"><span class="lineNum">     120</span>              : </span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L122"><span class="lineNum">     122</span>              :   Future&lt;Map&lt;String, String&gt;&gt; verifyTwoFactor(</span>
<span id="L123"><span class="lineNum">     123</span>              :       String refCode, String code) async {</span>
<span id="L124"><span class="lineNum">     124</span>              :     try {</span>
<span id="L125"><span class="lineNum">     125</span> <span class="tlaUNC">           0 :       final body = '03$refCode|$code';</span></span>
<span id="L126"><span class="lineNum">     126</span> <span class="tlaUNC">           0 :       final response = await _makeRequest(body, url: _au1Url);</span></span>
<span id="L127"><span class="lineNum">     127</span> <span class="tlaUNC">           0 :       final splitResponse = response.split('|');</span></span>
<span id="L128"><span class="lineNum">     128</span>              : </span>
<span id="L129"><span class="lineNum">     129</span> <span class="tlaUNC">           0 :       if (response == '0') {</span></span>
<span id="L130"><span class="lineNum">     130</span> <span class="tlaUNC">           0 :         throw CustomException('Incorrect code');</span></span>
<span id="L131"><span class="lineNum">     131</span> <span class="tlaUNC">           0 :       } else if (response == '1') {</span></span>
<span id="L132"><span class="lineNum">     132</span> <span class="tlaUNC">           0 :         throw CustomException('Code expired');</span></span>
<span id="L133"><span class="lineNum">     133</span> <span class="tlaUNC">           0 :       } else if (splitResponse.length == 2) {</span></span>
<span id="L134"><span class="lineNum">     134</span> <span class="tlaUNC">           0 :         return {</span></span>
<span id="L135"><span class="lineNum">     135</span> <span class="tlaUNC">           0 :           'access_token': splitResponse[0],</span></span>
<span id="L136"><span class="lineNum">     136</span> <span class="tlaUNC">           0 :           'refresh_token': splitResponse[1],</span></span>
<span id="L137"><span class="lineNum">     137</span>              :         };</span>
<span id="L138"><span class="lineNum">     138</span>              :       } else {</span>
<span id="L139"><span class="lineNum">     139</span> <span class="tlaUNC">           0 :         throw CustomException('Unexpected response');</span></span>
<span id="L140"><span class="lineNum">     140</span>              :       }</span>
<span id="L141"><span class="lineNum">     141</span>              :     } catch (e) {</span>
<span id="L142"><span class="lineNum">     142</span> <span class="tlaUNC">           0 :       if (e is CustomException) rethrow;</span></span>
<span id="L143"><span class="lineNum">     143</span> <span class="tlaUNC">           0 :       throw CustomException('Two-factor verification failed: ${e.toString()}');</span></span>
<span id="L144"><span class="lineNum">     144</span>              :     }</span>
<span id="L145"><span class="lineNum">     145</span>              :   }</span>
<span id="L146"><span class="lineNum">     146</span>              : </span>
<span id="L147"><span class="lineNum">     147</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L148"><span class="lineNum">     148</span>              :   Future&lt;Map&lt;String, String&gt;&gt; refreshToken(String refreshToken) async {</span>
<span id="L149"><span class="lineNum">     149</span>              :     try {</span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaUNC">           0 :       final body = '04$refreshToken';</span></span>
<span id="L151"><span class="lineNum">     151</span> <span class="tlaUNC">           0 :       final response = await _makeRequest(body, url: _au1Url);</span></span>
<span id="L152"><span class="lineNum">     152</span> <span class="tlaUNC">           0 :       final splitResponse = response.split('|');</span></span>
<span id="L153"><span class="lineNum">     153</span>              : </span>
<span id="L154"><span class="lineNum">     154</span> <span class="tlaUNC">           0 :       if (response == '0') {</span></span>
<span id="L155"><span class="lineNum">     155</span> <span class="tlaUNC">           0 :         throw CustomException(</span></span>
<span id="L156"><span class="lineNum">     156</span>              :             'Redirecting to login page.. Please login again.');</span>
<span id="L157"><span class="lineNum">     157</span> <span class="tlaUNC">           0 :       } else if (splitResponse.length == 2) {</span></span>
<span id="L158"><span class="lineNum">     158</span> <span class="tlaUNC">           0 :         return {</span></span>
<span id="L159"><span class="lineNum">     159</span> <span class="tlaUNC">           0 :           'access_token': splitResponse[0],</span></span>
<span id="L160"><span class="lineNum">     160</span> <span class="tlaUNC">           0 :           'refresh_token': splitResponse[1],</span></span>
<span id="L161"><span class="lineNum">     161</span>              :         };</span>
<span id="L162"><span class="lineNum">     162</span>              :       } else {</span>
<span id="L163"><span class="lineNum">     163</span> <span class="tlaUNC">           0 :         throw CustomException('Unexpected response');</span></span>
<span id="L164"><span class="lineNum">     164</span>              :       }</span>
<span id="L165"><span class="lineNum">     165</span>              :     } catch (e) {</span>
<span id="L166"><span class="lineNum">     166</span> <span class="tlaUNC">           0 :       if (e is CustomException) rethrow;</span></span>
<span id="L167"><span class="lineNum">     167</span> <span class="tlaUNC">           0 :       throw CustomException('Token refresh failed: ${e.toString()}');</span></span>
<span id="L168"><span class="lineNum">     168</span>              :     }</span>
<span id="L169"><span class="lineNum">     169</span>              :   }</span>
<span id="L170"><span class="lineNum">     170</span>              : </span>
<span id="L171"><span class="lineNum">     171</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L172"><span class="lineNum">     172</span>              :   Future&lt;AuthUserModel&gt; getUserInfo() async {</span>
<span id="L173"><span class="lineNum">     173</span>              :     try {</span>
<span id="L174"><span class="lineNum">     174</span>              :       const body = '07';</span>
<span id="L175"><span class="lineNum">     175</span> <span class="tlaUNC">           0 :       final response = await _makeRequest(body, url: _au3Url);</span></span>
<span id="L176"><span class="lineNum">     176</span> <span class="tlaUNC">           0 :       final userMap = jsonDecode(response) as Map&lt;String, dynamic&gt;;</span></span>
<span id="L177"><span class="lineNum">     177</span>              : </span>
<span id="L178"><span class="lineNum">     178</span> <span class="tlaUNC">           0 :       return AuthUserModel.fromJson(userMap);</span></span>
<span id="L179"><span class="lineNum">     179</span>              :     } catch (e) {</span>
<span id="L180"><span class="lineNum">     180</span> <span class="tlaUNC">           0 :       if (e is CustomException) rethrow;</span></span>
<span id="L181"><span class="lineNum">     181</span> <span class="tlaUNC">           0 :       throw CustomException('Failed to get user info: ${e.toString()}');</span></span>
<span id="L182"><span class="lineNum">     182</span>              :     }</span>
<span id="L183"><span class="lineNum">     183</span>              :   }</span>
<span id="L184"><span class="lineNum">     184</span>              : </span>
<span id="L185"><span class="lineNum">     185</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L186"><span class="lineNum">     186</span>              :   Future&lt;void&gt; updateUserInfo({</span>
<span id="L187"><span class="lineNum">     187</span>              :     required String oldPassword,</span>
<span id="L188"><span class="lineNum">     188</span>              :     required String fullName,</span>
<span id="L189"><span class="lineNum">     189</span>              :     required String email,</span>
<span id="L190"><span class="lineNum">     190</span>              :     required String phone,</span>
<span id="L191"><span class="lineNum">     191</span>              :     required String newPassword,</span>
<span id="L192"><span class="lineNum">     192</span>              :   }) async {</span>
<span id="L193"><span class="lineNum">     193</span>              :     try {</span>
<span id="L194"><span class="lineNum">     194</span> <span class="tlaUNC">           0 :       final oldPassB64 = base64.encode(utf8.encode(oldPassword));</span></span>
<span id="L195"><span class="lineNum">     195</span> <span class="tlaUNC">           0 :       final newPassB64 = base64.encode(utf8.encode(newPassword));</span></span>
<span id="L196"><span class="lineNum">     196</span>              : </span>
<span id="L197"><span class="lineNum">     197</span> <span class="tlaUNC">           0 :       final body = '00$oldPassB64|$fullName|$email|$phone|$newPassB64';</span></span>
<span id="L198"><span class="lineNum">     198</span> <span class="tlaUNC">           0 :       final response = await _makeRequest(body, url: _au3Url);</span></span>
<span id="L199"><span class="lineNum">     199</span>              : </span>
<span id="L200"><span class="lineNum">     200</span> <span class="tlaUNC">           0 :       if (response == '0') {</span></span>
<span id="L201"><span class="lineNum">     201</span> <span class="tlaUNC">           0 :         throw CustomException('Incorrect old password');</span></span>
<span id="L202"><span class="lineNum">     202</span> <span class="tlaUNC">           0 :       } else if (response == '1') {</span></span>
<span id="L203"><span class="lineNum">     203</span> <span class="tlaUNC">           0 :         throw CustomException('Email already in use');</span></span>
<span id="L204"><span class="lineNum">     204</span> <span class="tlaUNC">           0 :       } else if (response == '2') {</span></span>
<span id="L205"><span class="lineNum">     205</span> <span class="tlaUNC">           0 :         throw CustomException('Number already in use');</span></span>
<span id="L206"><span class="lineNum">     206</span>              :       }</span>
<span id="L207"><span class="lineNum">     207</span>              :     } catch (e) {</span>
<span id="L208"><span class="lineNum">     208</span> <span class="tlaUNC">           0 :       if (e is CustomException) rethrow;</span></span>
<span id="L209"><span class="lineNum">     209</span> <span class="tlaUNC">           0 :       throw CustomException('Failed to update user info: ${e.toString()}');</span></span>
<span id="L210"><span class="lineNum">     210</span>              :     }</span>
<span id="L211"><span class="lineNum">     211</span>              :   }</span>
<span id="L212"><span class="lineNum">     212</span>              : </span>
<span id="L213"><span class="lineNum">     213</span>              :   /// Makes an HTTP request using the existing API protocol</span>
<span id="L214"><span class="lineNum">     214</span> <span class="tlaUNC">           0 :   Future&lt;String&gt; _makeRequest(</span></span>
<span id="L215"><span class="lineNum">     215</span>              :     String body, {</span>
<span id="L216"><span class="lineNum">     216</span>              :     String url = _au1Url,</span>
<span id="L217"><span class="lineNum">     217</span>              :     Duration? timeout,</span>
<span id="L218"><span class="lineNum">     218</span>              :     String? accessToken,</span>
<span id="L219"><span class="lineNum">     219</span>              :   }) async {</span>
<span id="L220"><span class="lineNum">     220</span>              :     try {</span>
<span id="L221"><span class="lineNum">     221</span>              :       // Check connectivity</span>
<span id="L222"><span class="lineNum">     222</span> <span class="tlaUNC">           0 :       final connectivityResult = await Connectivity().checkConnectivity();</span></span>
<span id="L223"><span class="lineNum">     223</span> <span class="tlaUNC">           0 :       if (connectivityResult == ConnectivityResult.none) {</span></span>
<span id="L224"><span class="lineNum">     224</span> <span class="tlaUNC">           0 :         throw CustomException('No internet connection');</span></span>
<span id="L225"><span class="lineNum">     225</span>              :       }</span>
<span id="L226"><span class="lineNum">     226</span>              : </span>
<span id="L227"><span class="lineNum">     227</span>              :       // Get user agent</span>
<span id="L228"><span class="lineNum">     228</span> <span class="tlaUNC">           0 :       final userAgent = await DeviceInfoUtil.getUserAgent();</span></span>
<span id="L229"><span class="lineNum">     229</span>              : </span>
<span id="L230"><span class="lineNum">     230</span>              :       // Prepare headers</span>
<span id="L231"><span class="lineNum">     231</span> <span class="tlaUNC">           0 :       final headers = {</span></span>
<span id="L232"><span class="lineNum">     232</span> <span class="tlaUNC">           0 :         'User-Agent': userAgent,</span></span>
<span id="L233"><span class="lineNum">     233</span> <span class="tlaUNC">           0 :         'medium': 'phone',</span></span>
<span id="L234"><span class="lineNum">     234</span> <span class="tlaUNC">           0 :         'Content-Type': 'text/plain',</span></span>
<span id="L235"><span class="lineNum">     235</span> <span class="tlaUNC">           0 :         if (accessToken != null) 'Authorization': 'Bearer $accessToken',</span></span>
<span id="L236"><span class="lineNum">     236</span> <span class="tlaUNC">           0 :         if (url == _au1Url) 'tenantID': _tenantId,</span></span>
<span id="L237"><span class="lineNum">     237</span> <span class="tlaUNC">           0 :         if (url == _au1Url) 'clientID': _clientId,</span></span>
<span id="L238"><span class="lineNum">     238</span>              :       };</span>
<span id="L239"><span class="lineNum">     239</span>              : </span>
<span id="L240"><span class="lineNum">     240</span>              :       // Make the request</span>
<span id="L241"><span class="lineNum">     241</span> <span class="tlaUNC">           0 :       final response = await httpClient</span></span>
<span id="L242"><span class="lineNum">     242</span> <span class="tlaUNC">           0 :           .post(</span></span>
<span id="L243"><span class="lineNum">     243</span> <span class="tlaUNC">           0 :             Uri.parse(url),</span></span>
<span id="L244"><span class="lineNum">     244</span>              :             headers: headers,</span>
<span id="L245"><span class="lineNum">     245</span>              :             body: body,</span>
<span id="L246"><span class="lineNum">     246</span>              :           )</span>
<span id="L247"><span class="lineNum">     247</span> <span class="tlaUNC">           0 :           .timeout(timeout ?? const Duration(seconds: 30));</span></span>
<span id="L248"><span class="lineNum">     248</span>              : </span>
<span id="L249"><span class="lineNum">     249</span>              :       // Handle HTTP errors</span>
<span id="L250"><span class="lineNum">     250</span> <span class="tlaUNC">           0 :       if (response.statusCode != 200) {</span></span>
<span id="L251"><span class="lineNum">     251</span> <span class="tlaUNC">           0 :         throw CustomException(</span></span>
<span id="L252"><span class="lineNum">     252</span> <span class="tlaUNC">           0 :           'HTTP ${response.statusCode}: ${response.reasonPhrase}',</span></span>
<span id="L253"><span class="lineNum">     253</span>              :         );</span>
<span id="L254"><span class="lineNum">     254</span>              :       }</span>
<span id="L255"><span class="lineNum">     255</span>              : </span>
<span id="L256"><span class="lineNum">     256</span> <span class="tlaUNC">           0 :       return response.body;</span></span>
<span id="L257"><span class="lineNum">     257</span> <span class="tlaUNC">           0 :     } on SocketException {</span></span>
<span id="L258"><span class="lineNum">     258</span> <span class="tlaUNC">           0 :       throw CustomException('No internet connection');</span></span>
<span id="L259"><span class="lineNum">     259</span> <span class="tlaUNC">           0 :     } on HttpException catch (e) {</span></span>
<span id="L260"><span class="lineNum">     260</span> <span class="tlaUNC">           0 :       throw CustomException('Network error: ${e.message}');</span></span>
<span id="L261"><span class="lineNum">     261</span> <span class="tlaUNC">           0 :     } on FormatException {</span></span>
<span id="L262"><span class="lineNum">     262</span> <span class="tlaUNC">           0 :       throw CustomException('Invalid response format');</span></span>
<span id="L263"><span class="lineNum">     263</span>              :     } catch (e) {</span>
<span id="L264"><span class="lineNum">     264</span> <span class="tlaUNC">           0 :       if (e is CustomException) rethrow;</span></span>
<span id="L265"><span class="lineNum">     265</span> <span class="tlaUNC">           0 :       throw CustomException('Request failed: ${e.toString()}');</span></span>
<span id="L266"><span class="lineNum">     266</span>              :     }</span>
<span id="L267"><span class="lineNum">     267</span>              :   }</span>
<span id="L268"><span class="lineNum">     268</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
