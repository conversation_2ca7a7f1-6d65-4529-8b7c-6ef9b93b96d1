<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/bloc/auth/auth_event.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/bloc/auth">lib/bloc/auth</a> - auth_event.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">4.8&nbsp;%</td>
            <td class="headerCovTableEntry">21</td>
            <td class="headerCovTableEntry">1</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-07-03 12:38:23</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:equatable/equatable.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : /// Abstract base class for all authentication events</span>
<span id="L4"><span class="lineNum">       4</span>              : /// </span>
<span id="L5"><span class="lineNum">       5</span>              : /// This class defines the contract for authentication events that can be</span>
<span id="L6"><span class="lineNum">       6</span>              : /// dispatched to the AuthBloc. All authentication events extend this class</span>
<span id="L7"><span class="lineNum">       7</span>              : /// and implement Equatable for efficient event comparison.</span>
<span id="L8"><span class="lineNum">       8</span>              : abstract class AuthEvent extends Equatable {</span>
<span id="L9"><span class="lineNum">       9</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L10"><span class="lineNum">      10</span> <span class="tlaUNC">           0 :   List&lt;Object?&gt; get props =&gt; [];</span></span>
<span id="L11"><span class="lineNum">      11</span>              : }</span>
<span id="L12"><span class="lineNum">      12</span>              : </span>
<span id="L13"><span class="lineNum">      13</span>              : /// Event to request standard email/password authentication</span>
<span id="L14"><span class="lineNum">      14</span>              : /// </span>
<span id="L15"><span class="lineNum">      15</span>              : /// This event is dispatched when the user attempts to log in using</span>
<span id="L16"><span class="lineNum">      16</span>              : /// their email address and password. The AuthBloc will validate</span>
<span id="L17"><span class="lineNum">      17</span>              : /// the credentials and emit appropriate states based on the result.</span>
<span id="L18"><span class="lineNum">      18</span>              : class AuthLoginRequested extends AuthEvent {</span>
<span id="L19"><span class="lineNum">      19</span>              :   /// User's email address</span>
<span id="L20"><span class="lineNum">      20</span>              :   final String email;</span>
<span id="L21"><span class="lineNum">      21</span>              :   </span>
<span id="L22"><span class="lineNum">      22</span>              :   /// User's password</span>
<span id="L23"><span class="lineNum">      23</span>              :   final String password;</span>
<span id="L24"><span class="lineNum">      24</span>              : </span>
<span id="L25"><span class="lineNum">      25</span> <span class="tlaGNC">           1 :   AuthLoginRequested(this.email, this.password);</span></span>
<span id="L26"><span class="lineNum">      26</span>              : </span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaUNC">           0 :   List&lt;Object?&gt; get props =&gt; [email, password];</span></span>
<span id="L29"><span class="lineNum">      29</span>              : </span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaUNC">           0 :   String toString() =&gt; 'AuthLoginRequested(email: $email)';</span></span>
<span id="L32"><span class="lineNum">      32</span>              : }</span>
<span id="L33"><span class="lineNum">      33</span>              : </span>
<span id="L34"><span class="lineNum">      34</span>              : /// Event to request biometric authentication</span>
<span id="L35"><span class="lineNum">      35</span>              : /// </span>
<span id="L36"><span class="lineNum">      36</span>              : /// This event is dispatched when the user attempts to log in using</span>
<span id="L37"><span class="lineNum">      37</span>              : /// biometric authentication (fingerprint, face recognition, etc.).</span>
<span id="L38"><span class="lineNum">      38</span>              : /// The email is used to identify the user account for biometric login.</span>
<span id="L39"><span class="lineNum">      39</span>              : class AuthBiometricLoginRequested extends AuthEvent {</span>
<span id="L40"><span class="lineNum">      40</span>              :   /// User's email address for biometric authentication</span>
<span id="L41"><span class="lineNum">      41</span>              :   final String email;</span>
<span id="L42"><span class="lineNum">      42</span>              : </span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaUNC">           0 :   AuthBiometricLoginRequested(this.email);</span></span>
<span id="L44"><span class="lineNum">      44</span>              : </span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :   List&lt;Object?&gt; get props =&gt; [email];</span></span>
<span id="L47"><span class="lineNum">      47</span>              : </span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaUNC">           0 :   String toString() =&gt; 'AuthBiometricLoginRequested(email: $email)';</span></span>
<span id="L50"><span class="lineNum">      50</span>              : }</span>
<span id="L51"><span class="lineNum">      51</span>              : </span>
<span id="L52"><span class="lineNum">      52</span>              : /// Event to submit two-factor authentication code</span>
<span id="L53"><span class="lineNum">      53</span>              : /// </span>
<span id="L54"><span class="lineNum">      54</span>              : /// This event is dispatched when the user enters their two-factor</span>
<span id="L55"><span class="lineNum">      55</span>              : /// authentication code after initial login. The refCode is the</span>
<span id="L56"><span class="lineNum">      56</span>              : /// reference code received from the initial login attempt.</span>
<span id="L57"><span class="lineNum">      57</span>              : class AuthTwoFactorSubmitted extends AuthEvent {</span>
<span id="L58"><span class="lineNum">      58</span>              :   /// Reference code from the initial login attempt</span>
<span id="L59"><span class="lineNum">      59</span>              :   final String refCode;</span>
<span id="L60"><span class="lineNum">      60</span>              :   </span>
<span id="L61"><span class="lineNum">      61</span>              :   /// Two-factor authentication code entered by the user</span>
<span id="L62"><span class="lineNum">      62</span>              :   final String code;</span>
<span id="L63"><span class="lineNum">      63</span>              : </span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :   AuthTwoFactorSubmitted(this.refCode, this.code);</span></span>
<span id="L65"><span class="lineNum">      65</span>              : </span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :   List&lt;Object?&gt; get props =&gt; [refCode, code];</span></span>
<span id="L68"><span class="lineNum">      68</span>              : </span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :   String toString() =&gt; 'AuthTwoFactorSubmitted(refCode: $refCode, code: $code)';</span></span>
<span id="L71"><span class="lineNum">      71</span>              : }</span>
<span id="L72"><span class="lineNum">      72</span>              : </span>
<span id="L73"><span class="lineNum">      73</span>              : /// Event to request user logout</span>
<span id="L74"><span class="lineNum">      74</span>              : /// </span>
<span id="L75"><span class="lineNum">      75</span>              : /// This event is dispatched when the user wants to log out of the application.</span>
<span id="L76"><span class="lineNum">      76</span>              : /// The AuthBloc will clear the authentication session and emit the</span>
<span id="L77"><span class="lineNum">      77</span>              : /// unauthenticated state.</span>
<span id="L78"><span class="lineNum">      78</span>              : class AuthLogoutRequested extends AuthEvent {</span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L80"><span class="lineNum">      80</span>              :   String toString() =&gt; 'AuthLogoutRequested()';</span>
<span id="L81"><span class="lineNum">      81</span>              : }</span>
<span id="L82"><span class="lineNum">      82</span>              : </span>
<span id="L83"><span class="lineNum">      83</span>              : /// Event to check current authentication status</span>
<span id="L84"><span class="lineNum">      84</span>              : /// </span>
<span id="L85"><span class="lineNum">      85</span>              : /// This event is dispatched to verify if the user is currently authenticated.</span>
<span id="L86"><span class="lineNum">      86</span>              : /// This is typically used when the app starts to check if there's an existing</span>
<span id="L87"><span class="lineNum">      87</span>              : /// valid session. The AuthBloc will check stored tokens and session validity.</span>
<span id="L88"><span class="lineNum">      88</span>              : class AuthCheckRequested extends AuthEvent {</span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L90"><span class="lineNum">      90</span>              :   String toString() =&gt; 'AuthCheckRequested()';</span>
<span id="L91"><span class="lineNum">      91</span>              : }</span>
<span id="L92"><span class="lineNum">      92</span>              : </span>
<span id="L93"><span class="lineNum">      93</span>              : /// Event to request global logout</span>
<span id="L94"><span class="lineNum">      94</span>              : /// </span>
<span id="L95"><span class="lineNum">      95</span>              : /// This event is dispatched when the user wants to log out from all devices.</span>
<span id="L96"><span class="lineNum">      96</span>              : /// This will invalidate all sessions and tokens across all devices where</span>
<span id="L97"><span class="lineNum">      97</span>              : /// the user is logged in.</span>
<span id="L98"><span class="lineNum">      98</span>              : class AuthGlobalLogoutRequested extends AuthEvent {</span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L100"><span class="lineNum">     100</span>              :   String toString() =&gt; 'AuthGlobalLogoutRequested()';</span>
<span id="L101"><span class="lineNum">     101</span>              : }</span>
<span id="L102"><span class="lineNum">     102</span>              : </span>
<span id="L103"><span class="lineNum">     103</span>              : /// Event to request token refresh</span>
<span id="L104"><span class="lineNum">     104</span>              : /// </span>
<span id="L105"><span class="lineNum">     105</span>              : /// This event is dispatched when the access token needs to be refreshed.</span>
<span id="L106"><span class="lineNum">     106</span>              : /// This is typically handled automatically by the AuthBloc when it detects</span>
<span id="L107"><span class="lineNum">     107</span>              : /// that the current token is expired or about to expire.</span>
<span id="L108"><span class="lineNum">     108</span>              : class AuthTokenRefreshRequested extends AuthEvent {</span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L110"><span class="lineNum">     110</span>              :   String toString() =&gt; 'AuthTokenRefreshRequested()';</span>
<span id="L111"><span class="lineNum">     111</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
