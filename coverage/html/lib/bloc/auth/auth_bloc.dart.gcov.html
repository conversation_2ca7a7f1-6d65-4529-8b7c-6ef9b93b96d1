<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/bloc/auth/auth_bloc.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/bloc/auth">lib/bloc/auth</a> - auth_bloc.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">68</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-07-03 12:38:23</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_bloc/flutter_bloc.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import '../../core/error/error_mapper.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import '../../domain/entities/auth_user.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import '../../domain/repositories/auth_repository.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import '../../domain/usecases/login_usecase.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import '../../domain/usecases/logout_usecase.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import '../../domain/usecases/verify_two_factor_usecase.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import 'auth_event.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import 'auth_state.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : </span>
<span id="L11"><span class="lineNum">      11</span>              : /// BLoC for managing authentication state and operations</span>
<span id="L12"><span class="lineNum">      12</span>              : ///</span>
<span id="L13"><span class="lineNum">      13</span>              : /// This BLoC handles all authentication-related business logic including:</span>
<span id="L14"><span class="lineNum">      14</span>              : /// - Standard email/password login</span>
<span id="L15"><span class="lineNum">      15</span>              : /// - Biometric authentication</span>
<span id="L16"><span class="lineNum">      16</span>              : /// - Two-factor authentication</span>
<span id="L17"><span class="lineNum">      17</span>              : /// - Logout operations</span>
<span id="L18"><span class="lineNum">      18</span>              : /// - Authentication status checking</span>
<span id="L19"><span class="lineNum">      19</span>              : /// - Token refresh</span>
<span id="L20"><span class="lineNum">      20</span>              : ///</span>
<span id="L21"><span class="lineNum">      21</span>              : /// The BLoC uses domain layer use cases to perform authentication operations</span>
<span id="L22"><span class="lineNum">      22</span>              : /// and maintains the current authentication state of the application.</span>
<span id="L23"><span class="lineNum">      23</span>              : class AuthBloc extends Bloc&lt;AuthEvent, AuthState&gt; {</span>
<span id="L24"><span class="lineNum">      24</span>              :   final LoginUseCase _loginUseCase;</span>
<span id="L25"><span class="lineNum">      25</span>              :   final BiometricLoginUseCase _biometricLoginUseCase;</span>
<span id="L26"><span class="lineNum">      26</span>              :   final LogoutUseCase _logoutUseCase;</span>
<span id="L27"><span class="lineNum">      27</span>              :   final VerifyTwoFactorUseCase _verifyTwoFactorUseCase;</span>
<span id="L28"><span class="lineNum">      28</span>              :   final AuthRepository _authRepository;</span>
<span id="L29"><span class="lineNum">      29</span>              : </span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaUNC">           0 :   AuthBloc({</span></span>
<span id="L31"><span class="lineNum">      31</span>              :     required LoginUseCase loginUseCase,</span>
<span id="L32"><span class="lineNum">      32</span>              :     required BiometricLoginUseCase biometricLoginUseCase,</span>
<span id="L33"><span class="lineNum">      33</span>              :     required LogoutUseCase logoutUseCase,</span>
<span id="L34"><span class="lineNum">      34</span>              :     required VerifyTwoFactorUseCase verifyTwoFactorUseCase,</span>
<span id="L35"><span class="lineNum">      35</span>              :     required AuthRepository authRepository,</span>
<span id="L36"><span class="lineNum">      36</span>              :   })  : _loginUseCase = loginUseCase,</span>
<span id="L37"><span class="lineNum">      37</span>              :         _biometricLoginUseCase = biometricLoginUseCase,</span>
<span id="L38"><span class="lineNum">      38</span>              :         _logoutUseCase = logoutUseCase,</span>
<span id="L39"><span class="lineNum">      39</span>              :         _verifyTwoFactorUseCase = verifyTwoFactorUseCase,</span>
<span id="L40"><span class="lineNum">      40</span>              :         _authRepository = authRepository,</span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaUNC">           0 :         super(AuthInitial()) {</span></span>
<span id="L42"><span class="lineNum">      42</span>              :     // Register event handlers</span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaUNC">           0 :     on&lt;AuthLoginRequested&gt;(_onLoginRequested);</span></span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaUNC">           0 :     on&lt;AuthBiometricLoginRequested&gt;(_onBiometricLoginRequested);</span></span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC">           0 :     on&lt;AuthTwoFactorSubmitted&gt;(_onTwoFactorSubmitted);</span></span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :     on&lt;AuthLogoutRequested&gt;(_onLogoutRequested);</span></span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaUNC">           0 :     on&lt;AuthGlobalLogoutRequested&gt;(_onGlobalLogoutRequested);</span></span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaUNC">           0 :     on&lt;AuthCheckRequested&gt;(_onAuthCheckRequested);</span></span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaUNC">           0 :     on&lt;AuthTokenRefreshRequested&gt;(_onTokenRefreshRequested);</span></span>
<span id="L50"><span class="lineNum">      50</span>              :   }</span>
<span id="L51"><span class="lineNum">      51</span>              : </span>
<span id="L52"><span class="lineNum">      52</span>              :   /// Handles standard email/password login requests</span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; _onLoginRequested(</span></span>
<span id="L54"><span class="lineNum">      54</span>              :     AuthLoginRequested event,</span>
<span id="L55"><span class="lineNum">      55</span>              :     Emitter&lt;AuthState&gt; emit,</span>
<span id="L56"><span class="lineNum">      56</span>              :   ) async {</span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaUNC">           0 :     emit(AuthLoading());</span></span>
<span id="L58"><span class="lineNum">      58</span>              : </span>
<span id="L59"><span class="lineNum">      59</span>              :     try {</span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaUNC">           0 :       final result = await _loginUseCase.call(event.email, event.password);</span></span>
<span id="L61"><span class="lineNum">      61</span>              : </span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaUNC">           0 :       if (result.success &amp;&amp; result.user != null) {</span></span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaUNC">           0 :         emit(AuthAuthenticated(result.user!));</span></span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :       } else if (result.requiresTwoFactor &amp;&amp; result.twoFactorRefCode != null) {</span></span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaUNC">           0 :         emit(AuthTwoFactorRequired(result.twoFactorRefCode!));</span></span>
<span id="L66"><span class="lineNum">      66</span>              :       } else {</span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :         final failure = result.failure ??</span></span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaUNC">           0 :             ErrorMapper.mapExceptionToFailure(result.error ?? 'Login failed');</span></span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :         emit(AuthError(failure));</span></span>
<span id="L70"><span class="lineNum">      70</span>              :       }</span>
<span id="L71"><span class="lineNum">      71</span>              :     } catch (e) {</span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaUNC">           0 :       final failure = ErrorMapper.mapExceptionToFailure(e);</span></span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :       emit(AuthError(failure));</span></span>
<span id="L74"><span class="lineNum">      74</span>              :     }</span>
<span id="L75"><span class="lineNum">      75</span>              :   }</span>
<span id="L76"><span class="lineNum">      76</span>              : </span>
<span id="L77"><span class="lineNum">      77</span>              :   /// Handles biometric login requests</span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; _onBiometricLoginRequested(</span></span>
<span id="L79"><span class="lineNum">      79</span>              :     AuthBiometricLoginRequested event,</span>
<span id="L80"><span class="lineNum">      80</span>              :     Emitter&lt;AuthState&gt; emit,</span>
<span id="L81"><span class="lineNum">      81</span>              :   ) async {</span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaUNC">           0 :     emit(AuthLoading());</span></span>
<span id="L83"><span class="lineNum">      83</span>              : </span>
<span id="L84"><span class="lineNum">      84</span>              :     try {</span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaUNC">           0 :       final result = await _biometricLoginUseCase.call(event.email);</span></span>
<span id="L86"><span class="lineNum">      86</span>              : </span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaUNC">           0 :       if (result.success &amp;&amp; result.user != null) {</span></span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaUNC">           0 :         emit(AuthAuthenticated(result.user!));</span></span>
<span id="L89"><span class="lineNum">      89</span>              :       } else {</span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaUNC">           0 :         final failure = result.failure ??</span></span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaUNC">           0 :             ErrorMapper.mapExceptionToFailure(</span></span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaUNC">           0 :                 result.error ?? 'Biometric login failed');</span></span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaUNC">           0 :         emit(AuthError(failure));</span></span>
<span id="L94"><span class="lineNum">      94</span>              :       }</span>
<span id="L95"><span class="lineNum">      95</span>              :     } catch (e) {</span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaUNC">           0 :       final failure = ErrorMapper.mapExceptionToFailure(e);</span></span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaUNC">           0 :       emit(AuthError(failure));</span></span>
<span id="L98"><span class="lineNum">      98</span>              :     }</span>
<span id="L99"><span class="lineNum">      99</span>              :   }</span>
<span id="L100"><span class="lineNum">     100</span>              : </span>
<span id="L101"><span class="lineNum">     101</span>              :   /// Handles two-factor authentication code submission</span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; _onTwoFactorSubmitted(</span></span>
<span id="L103"><span class="lineNum">     103</span>              :     AuthTwoFactorSubmitted event,</span>
<span id="L104"><span class="lineNum">     104</span>              :     Emitter&lt;AuthState&gt; emit,</span>
<span id="L105"><span class="lineNum">     105</span>              :   ) async {</span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaUNC">           0 :     emit(AuthLoading());</span></span>
<span id="L107"><span class="lineNum">     107</span>              : </span>
<span id="L108"><span class="lineNum">     108</span>              :     try {</span>
<span id="L109"><span class="lineNum">     109</span>              :       final result =</span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaUNC">           0 :           await _verifyTwoFactorUseCase.call(event.refCode, event.code);</span></span>
<span id="L111"><span class="lineNum">     111</span>              : </span>
<span id="L112"><span class="lineNum">     112</span> <span class="tlaUNC">           0 :       if (result.success &amp;&amp; result.user != null) {</span></span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaUNC">           0 :         emit(AuthAuthenticated(result.user!));</span></span>
<span id="L114"><span class="lineNum">     114</span>              :       } else {</span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaUNC">           0 :         final failure = result.failure ??</span></span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaUNC">           0 :             ErrorMapper.mapExceptionToFailure(</span></span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaUNC">           0 :                 result.error ?? 'Two-factor authentication failed');</span></span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaUNC">           0 :         emit(AuthError(failure));</span></span>
<span id="L119"><span class="lineNum">     119</span>              :       }</span>
<span id="L120"><span class="lineNum">     120</span>              :     } catch (e) {</span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaUNC">           0 :       final failure = ErrorMapper.mapExceptionToFailure(e);</span></span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaUNC">           0 :       emit(AuthError(failure));</span></span>
<span id="L123"><span class="lineNum">     123</span>              :     }</span>
<span id="L124"><span class="lineNum">     124</span>              :   }</span>
<span id="L125"><span class="lineNum">     125</span>              : </span>
<span id="L126"><span class="lineNum">     126</span>              :   /// Handles logout requests</span>
<span id="L127"><span class="lineNum">     127</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; _onLogoutRequested(</span></span>
<span id="L128"><span class="lineNum">     128</span>              :     AuthLogoutRequested event,</span>
<span id="L129"><span class="lineNum">     129</span>              :     Emitter&lt;AuthState&gt; emit,</span>
<span id="L130"><span class="lineNum">     130</span>              :   ) async {</span>
<span id="L131"><span class="lineNum">     131</span> <span class="tlaUNC">           0 :     emit(AuthLoading());</span></span>
<span id="L132"><span class="lineNum">     132</span>              : </span>
<span id="L133"><span class="lineNum">     133</span>              :     try {</span>
<span id="L134"><span class="lineNum">     134</span> <span class="tlaUNC">           0 :       await _logoutUseCase.call();</span></span>
<span id="L135"><span class="lineNum">     135</span> <span class="tlaUNC">           0 :       emit(AuthUnauthenticated());</span></span>
<span id="L136"><span class="lineNum">     136</span>              :     } catch (e) {</span>
<span id="L137"><span class="lineNum">     137</span>              :       // Even if logout fails, we should still consider the user logged out locally</span>
<span id="L138"><span class="lineNum">     138</span> <span class="tlaUNC">           0 :       emit(AuthUnauthenticated());</span></span>
<span id="L139"><span class="lineNum">     139</span>              :     }</span>
<span id="L140"><span class="lineNum">     140</span>              :   }</span>
<span id="L141"><span class="lineNum">     141</span>              : </span>
<span id="L142"><span class="lineNum">     142</span>              :   /// Handles global logout requests</span>
<span id="L143"><span class="lineNum">     143</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; _onGlobalLogoutRequested(</span></span>
<span id="L144"><span class="lineNum">     144</span>              :     AuthGlobalLogoutRequested event,</span>
<span id="L145"><span class="lineNum">     145</span>              :     Emitter&lt;AuthState&gt; emit,</span>
<span id="L146"><span class="lineNum">     146</span>              :   ) async {</span>
<span id="L147"><span class="lineNum">     147</span> <span class="tlaUNC">           0 :     emit(AuthLoading());</span></span>
<span id="L148"><span class="lineNum">     148</span>              : </span>
<span id="L149"><span class="lineNum">     149</span>              :     try {</span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaUNC">           0 :       await _authRepository.globalLogout();</span></span>
<span id="L151"><span class="lineNum">     151</span> <span class="tlaUNC">           0 :       emit(AuthUnauthenticated());</span></span>
<span id="L152"><span class="lineNum">     152</span>              :     } catch (e) {</span>
<span id="L153"><span class="lineNum">     153</span>              :       // Even if global logout fails, we should still consider the user logged out locally</span>
<span id="L154"><span class="lineNum">     154</span> <span class="tlaUNC">           0 :       emit(AuthUnauthenticated());</span></span>
<span id="L155"><span class="lineNum">     155</span>              :     }</span>
<span id="L156"><span class="lineNum">     156</span>              :   }</span>
<span id="L157"><span class="lineNum">     157</span>              : </span>
<span id="L158"><span class="lineNum">     158</span>              :   /// Handles authentication status check requests</span>
<span id="L159"><span class="lineNum">     159</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; _onAuthCheckRequested(</span></span>
<span id="L160"><span class="lineNum">     160</span>              :     AuthCheckRequested event,</span>
<span id="L161"><span class="lineNum">     161</span>              :     Emitter&lt;AuthState&gt; emit,</span>
<span id="L162"><span class="lineNum">     162</span>              :   ) async {</span>
<span id="L163"><span class="lineNum">     163</span>              :     try {</span>
<span id="L164"><span class="lineNum">     164</span> <span class="tlaUNC">           0 :       final isAuthenticated = await _authRepository.isAuthenticated();</span></span>
<span id="L165"><span class="lineNum">     165</span>              : </span>
<span id="L166"><span class="lineNum">     166</span>              :       if (isAuthenticated) {</span>
<span id="L167"><span class="lineNum">     167</span> <span class="tlaUNC">           0 :         final user = await _authRepository.getCurrentUser();</span></span>
<span id="L168"><span class="lineNum">     168</span>              :         if (user != null) {</span>
<span id="L169"><span class="lineNum">     169</span> <span class="tlaUNC">           0 :           emit(AuthAuthenticated(user));</span></span>
<span id="L170"><span class="lineNum">     170</span>              :         } else {</span>
<span id="L171"><span class="lineNum">     171</span> <span class="tlaUNC">           0 :           emit(AuthUnauthenticated());</span></span>
<span id="L172"><span class="lineNum">     172</span>              :         }</span>
<span id="L173"><span class="lineNum">     173</span>              :       } else {</span>
<span id="L174"><span class="lineNum">     174</span> <span class="tlaUNC">           0 :         emit(AuthUnauthenticated());</span></span>
<span id="L175"><span class="lineNum">     175</span>              :       }</span>
<span id="L176"><span class="lineNum">     176</span>              :     } catch (e) {</span>
<span id="L177"><span class="lineNum">     177</span> <span class="tlaUNC">           0 :       emit(AuthUnauthenticated());</span></span>
<span id="L178"><span class="lineNum">     178</span>              :     }</span>
<span id="L179"><span class="lineNum">     179</span>              :   }</span>
<span id="L180"><span class="lineNum">     180</span>              : </span>
<span id="L181"><span class="lineNum">     181</span>              :   /// Handles token refresh requests</span>
<span id="L182"><span class="lineNum">     182</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; _onTokenRefreshRequested(</span></span>
<span id="L183"><span class="lineNum">     183</span>              :     AuthTokenRefreshRequested event,</span>
<span id="L184"><span class="lineNum">     184</span>              :     Emitter&lt;AuthState&gt; emit,</span>
<span id="L185"><span class="lineNum">     185</span>              :   ) async {</span>
<span id="L186"><span class="lineNum">     186</span>              :     try {</span>
<span id="L187"><span class="lineNum">     187</span> <span class="tlaUNC">           0 :       await _authRepository.refreshToken();</span></span>
<span id="L188"><span class="lineNum">     188</span>              :       // Token refresh doesn't change the authentication state</span>
<span id="L189"><span class="lineNum">     189</span>              :       // The current state should remain the same</span>
<span id="L190"><span class="lineNum">     190</span>              :     } catch (e) {</span>
<span id="L191"><span class="lineNum">     191</span>              :       // If token refresh fails, the user should be logged out</span>
<span id="L192"><span class="lineNum">     192</span> <span class="tlaUNC">           0 :       emit(AuthUnauthenticated());</span></span>
<span id="L193"><span class="lineNum">     193</span>              :     }</span>
<span id="L194"><span class="lineNum">     194</span>              :   }</span>
<span id="L195"><span class="lineNum">     195</span>              : </span>
<span id="L196"><span class="lineNum">     196</span>              :   /// Convenience method to check if user is currently authenticated</span>
<span id="L197"><span class="lineNum">     197</span> <span class="tlaUNC">           0 :   bool get isAuthenticated =&gt; state is AuthAuthenticated;</span></span>
<span id="L198"><span class="lineNum">     198</span>              : </span>
<span id="L199"><span class="lineNum">     199</span>              :   /// Convenience method to get the current authenticated user</span>
<span id="L200"><span class="lineNum">     200</span> <span class="tlaUNC">           0 :   AuthUser? get currentUser {</span></span>
<span id="L201"><span class="lineNum">     201</span> <span class="tlaUNC">           0 :     final currentState = state;</span></span>
<span id="L202"><span class="lineNum">     202</span> <span class="tlaUNC">           0 :     if (currentState is AuthAuthenticated) {</span></span>
<span id="L203"><span class="lineNum">     203</span> <span class="tlaUNC">           0 :       return currentState.user;</span></span>
<span id="L204"><span class="lineNum">     204</span>              :     }</span>
<span id="L205"><span class="lineNum">     205</span>              :     return null;</span>
<span id="L206"><span class="lineNum">     206</span>              :   }</span>
<span id="L207"><span class="lineNum">     207</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
