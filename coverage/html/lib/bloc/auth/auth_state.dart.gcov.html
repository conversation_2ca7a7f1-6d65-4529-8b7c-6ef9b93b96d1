<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/bloc/auth/auth_state.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/bloc/auth">lib/bloc/auth</a> - auth_state.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">18</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-07-03 12:38:23</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:equatable/equatable.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import '../../domain/entities/auth_user.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import '../../core/error/auth_failures.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : /// Abstract base class for all authentication states</span>
<span id="L6"><span class="lineNum">       6</span>              : ///</span>
<span id="L7"><span class="lineNum">       7</span>              : /// This class defines the contract for authentication states in the AuthBloc.</span>
<span id="L8"><span class="lineNum">       8</span>              : /// All authentication states extend this class and implement Equatable for</span>
<span id="L9"><span class="lineNum">       9</span>              : /// efficient state comparison and rebuilds.</span>
<span id="L10"><span class="lineNum">      10</span>              : abstract class AuthState extends Equatable {</span>
<span id="L11"><span class="lineNum">      11</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L12"><span class="lineNum">      12</span> <span class="tlaUNC">           0 :   List&lt;Object?&gt; get props =&gt; [];</span></span>
<span id="L13"><span class="lineNum">      13</span>              : }</span>
<span id="L14"><span class="lineNum">      14</span>              : </span>
<span id="L15"><span class="lineNum">      15</span>              : /// Initial state when the AuthBloc is first created</span>
<span id="L16"><span class="lineNum">      16</span>              : ///</span>
<span id="L17"><span class="lineNum">      17</span>              : /// This state represents the initial condition before any authentication</span>
<span id="L18"><span class="lineNum">      18</span>              : /// operations have been performed. The app should check authentication</span>
<span id="L19"><span class="lineNum">      19</span>              : /// status when this state is active.</span>
<span id="L20"><span class="lineNum">      20</span>              : class AuthInitial extends AuthState {}</span>
<span id="L21"><span class="lineNum">      21</span>              : </span>
<span id="L22"><span class="lineNum">      22</span>              : /// State indicating an authentication operation is in progress</span>
<span id="L23"><span class="lineNum">      23</span>              : ///</span>
<span id="L24"><span class="lineNum">      24</span>              : /// This state is emitted during login, logout, two-factor verification,</span>
<span id="L25"><span class="lineNum">      25</span>              : /// or any other authentication operation that requires network communication</span>
<span id="L26"><span class="lineNum">      26</span>              : /// or processing time.</span>
<span id="L27"><span class="lineNum">      27</span>              : class AuthLoading extends AuthState {}</span>
<span id="L28"><span class="lineNum">      28</span>              : </span>
<span id="L29"><span class="lineNum">      29</span>              : /// State indicating the user is successfully authenticated</span>
<span id="L30"><span class="lineNum">      30</span>              : ///</span>
<span id="L31"><span class="lineNum">      31</span>              : /// This state contains the authenticated user's information and is emitted</span>
<span id="L32"><span class="lineNum">      32</span>              : /// when login is successful, two-factor authentication is completed, or</span>
<span id="L33"><span class="lineNum">      33</span>              : /// when checking existing authentication status returns a valid session.</span>
<span id="L34"><span class="lineNum">      34</span>              : class AuthAuthenticated extends AuthState {</span>
<span id="L35"><span class="lineNum">      35</span>              :   /// The authenticated user's information</span>
<span id="L36"><span class="lineNum">      36</span>              :   final AuthUser user;</span>
<span id="L37"><span class="lineNum">      37</span>              : </span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaUNC">           0 :   AuthAuthenticated(this.user);</span></span>
<span id="L39"><span class="lineNum">      39</span>              : </span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaUNC">           0 :   List&lt;Object?&gt; get props =&gt; [user];</span></span>
<span id="L42"><span class="lineNum">      42</span>              : </span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaUNC">           0 :   String toString() =&gt; 'AuthAuthenticated(user: ${user.email})';</span></span>
<span id="L45"><span class="lineNum">      45</span>              : }</span>
<span id="L46"><span class="lineNum">      46</span>              : </span>
<span id="L47"><span class="lineNum">      47</span>              : /// State indicating the user is not authenticated</span>
<span id="L48"><span class="lineNum">      48</span>              : ///</span>
<span id="L49"><span class="lineNum">      49</span>              : /// This state is emitted when:</span>
<span id="L50"><span class="lineNum">      50</span>              : /// - User logs out successfully</span>
<span id="L51"><span class="lineNum">      51</span>              : /// - Authentication check fails</span>
<span id="L52"><span class="lineNum">      52</span>              : /// - Session expires</span>
<span id="L53"><span class="lineNum">      53</span>              : /// - Initial authentication check returns no valid session</span>
<span id="L54"><span class="lineNum">      54</span>              : class AuthUnauthenticated extends AuthState {</span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L56"><span class="lineNum">      56</span>              :   String toString() =&gt; 'AuthUnauthenticated()';</span>
<span id="L57"><span class="lineNum">      57</span>              : }</span>
<span id="L58"><span class="lineNum">      58</span>              : </span>
<span id="L59"><span class="lineNum">      59</span>              : /// State indicating two-factor authentication is required</span>
<span id="L60"><span class="lineNum">      60</span>              : ///</span>
<span id="L61"><span class="lineNum">      61</span>              : /// This state is emitted when initial login is successful but the user</span>
<span id="L62"><span class="lineNum">      62</span>              : /// has two-factor authentication enabled. The UI should navigate to</span>
<span id="L63"><span class="lineNum">      63</span>              : /// the two-factor code entry screen when this state is active.</span>
<span id="L64"><span class="lineNum">      64</span>              : class AuthTwoFactorRequired extends AuthState {</span>
<span id="L65"><span class="lineNum">      65</span>              :   /// Reference code for the two-factor authentication session</span>
<span id="L66"><span class="lineNum">      66</span>              :   /// This code is used to verify the two-factor authentication code</span>
<span id="L67"><span class="lineNum">      67</span>              :   final String refCode;</span>
<span id="L68"><span class="lineNum">      68</span>              : </span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :   AuthTwoFactorRequired(this.refCode);</span></span>
<span id="L70"><span class="lineNum">      70</span>              : </span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaUNC">           0 :   List&lt;Object?&gt; get props =&gt; [refCode];</span></span>
<span id="L73"><span class="lineNum">      73</span>              : </span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaUNC">           0 :   String toString() =&gt; 'AuthTwoFactorRequired(refCode: $refCode)';</span></span>
<span id="L76"><span class="lineNum">      76</span>              : }</span>
<span id="L77"><span class="lineNum">      77</span>              : </span>
<span id="L78"><span class="lineNum">      78</span>              : /// State indicating an authentication error has occurred</span>
<span id="L79"><span class="lineNum">      79</span>              : ///</span>
<span id="L80"><span class="lineNum">      80</span>              : /// This state is emitted when any authentication operation fails,</span>
<span id="L81"><span class="lineNum">      81</span>              : /// including login failures, network errors, invalid credentials,</span>
<span id="L82"><span class="lineNum">      82</span>              : /// or two-factor authentication failures.</span>
<span id="L83"><span class="lineNum">      83</span>              : class AuthError extends AuthState {</span>
<span id="L84"><span class="lineNum">      84</span>              :   /// The authentication failure that occurred</span>
<span id="L85"><span class="lineNum">      85</span>              :   final AuthFailure failure;</span>
<span id="L86"><span class="lineNum">      86</span>              : </span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaUNC">           0 :   AuthError(this.failure);</span></span>
<span id="L88"><span class="lineNum">      88</span>              : </span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaUNC">           0 :   List&lt;Object?&gt; get props =&gt; [failure];</span></span>
<span id="L91"><span class="lineNum">      91</span>              : </span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaUNC">           0 :   String toString() =&gt; 'AuthError(failure: $failure)';</span></span>
<span id="L94"><span class="lineNum">      94</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
