<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/utils/getDeviceID.dart</title>
  <link rel="stylesheet" type="text/css" href="../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/utils">lib/utils</a> - getDeviceID.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">27</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-07-03 12:38:23</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:device_info_plus/device_info_plus.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'dart:io';</span>
<span id="L3"><span class="lineNum">       3</span>              : </span>
<span id="L4"><span class="lineNum">       4</span>              : class DeviceInfoUtil {</span>
<span id="L5"><span class="lineNum">       5</span>              :   static String _deviceName = 'WaterMeteringMobile2';</span>
<span id="L6"><span class="lineNum">       6</span>              :   static String _deviceVersion = 'Unknown';</span>
<span id="L7"><span class="lineNum">       7</span>              : </span>
<span id="L8"><span class="lineNum">       8</span>              :   // A private method to get device info, only called on first-time access</span>
<span id="L9"><span class="lineNum">       9</span> <span class="tlaUNC">           0 :   static Future&lt;void&gt; _initDeviceInfo() async {</span></span>
<span id="L10"><span class="lineNum">      10</span>              :     try {</span>
<span id="L11"><span class="lineNum">      11</span> <span class="tlaUNC">           0 :       DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();</span></span>
<span id="L12"><span class="lineNum">      12</span>              : </span>
<span id="L13"><span class="lineNum">      13</span> <span class="tlaUNC">           0 :       if (Platform.isAndroid) {</span></span>
<span id="L14"><span class="lineNum">      14</span> <span class="tlaUNC">           0 :         AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;</span></span>
<span id="L15"><span class="lineNum">      15</span> <span class="tlaUNC">           0 :         _deviceName = androidInfo.model;</span></span>
<span id="L16"><span class="lineNum">      16</span>              : </span>
<span id="L17"><span class="lineNum">      17</span> <span class="tlaUNC">           0 :         _deviceVersion = 'Android ${androidInfo.version.release}';</span></span>
<span id="L18"><span class="lineNum">      18</span> <span class="tlaUNC">           0 :       } else if (Platform.isIOS) {</span></span>
<span id="L19"><span class="lineNum">      19</span> <span class="tlaUNC">           0 :         IosDeviceInfo iosInfo = await deviceInfo.iosInfo;</span></span>
<span id="L20"><span class="lineNum">      20</span> <span class="tlaUNC">           0 :         _deviceName = iosInfo.name;</span></span>
<span id="L21"><span class="lineNum">      21</span> <span class="tlaUNC">           0 :         _deviceVersion = 'iOS ${iosInfo.systemVersion}';</span></span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaUNC">           0 :       } else if (Platform.isWindows) {</span></span>
<span id="L23"><span class="lineNum">      23</span> <span class="tlaUNC">           0 :         WindowsDeviceInfo windowsInfo = await deviceInfo.windowsInfo;</span></span>
<span id="L24"><span class="lineNum">      24</span> <span class="tlaUNC">           0 :         _deviceName = windowsInfo.computerName;</span></span>
<span id="L25"><span class="lineNum">      25</span> <span class="tlaUNC">           0 :         _deviceVersion = windowsInfo.editionId; // Customize as needed</span></span>
<span id="L26"><span class="lineNum">      26</span> <span class="tlaUNC">           0 :       } else if (Platform.isMacOS) {</span></span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaUNC">           0 :         MacOsDeviceInfo macInfo = await deviceInfo.macOsInfo;</span></span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaUNC">           0 :         _deviceName = macInfo.model;</span></span>
<span id="L29"><span class="lineNum">      29</span> <span class="tlaUNC">           0 :         _deviceVersion = macInfo.osRelease;</span></span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaUNC">           0 :       } else if (Platform.isLinux) {</span></span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaUNC">           0 :         LinuxDeviceInfo linuxInfo = await deviceInfo.linuxInfo;</span></span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaUNC">           0 :         _deviceName = linuxInfo.prettyName;</span></span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaUNC">           0 :         _deviceVersion = linuxInfo.id;</span></span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaUNC">           0 :       } else if (Platform.isFuchsia) {</span></span>
<span id="L35"><span class="lineNum">      35</span>              :         _deviceName = 'Fuchsia Device';</span>
<span id="L36"><span class="lineNum">      36</span>              :         _deviceVersion = 'Fuchsia';</span>
<span id="L37"><span class="lineNum">      37</span>              :       }</span>
<span id="L38"><span class="lineNum">      38</span>              :     } catch (e) {</span>
<span id="L39"><span class="lineNum">      39</span>              :       // If any error occurs, keep the default values</span>
<span id="L40"><span class="lineNum">      40</span>              :       _deviceName = 'WaterMeteringMobile2';</span>
<span id="L41"><span class="lineNum">      41</span>              :       _deviceVersion = 'Unknown';</span>
<span id="L42"><span class="lineNum">      42</span>              :     }</span>
<span id="L43"><span class="lineNum">      43</span>              :   }</span>
<span id="L44"><span class="lineNum">      44</span>              : </span>
<span id="L45"><span class="lineNum">      45</span>              :   // Static method to get device name</span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :   static Future&lt;String&gt; getUserAgent() async {</span></span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaUNC">           0 :     if (_deviceName == 'WaterMeteringMobile2' || _deviceName == 'Unknown') {</span></span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaUNC">           0 :       await _initDeviceInfo();</span></span>
<span id="L49"><span class="lineNum">      49</span>              :     }</span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaUNC">           0 :     return '${_deviceName}\/$_deviceVersion';</span></span>
<span id="L51"><span class="lineNum">      51</span>              :   }</span>
<span id="L52"><span class="lineNum">      52</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
