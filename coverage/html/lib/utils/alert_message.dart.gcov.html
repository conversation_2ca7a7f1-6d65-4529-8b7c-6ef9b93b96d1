<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/utils/alert_message.dart</title>
  <link rel="stylesheet" type="text/css" href="../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/utils">lib/utils</a> - alert_message.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">61.0&nbsp;%</td>
            <td class="headerCovTableEntry">41</td>
            <td class="headerCovTableEntry">25</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-07-03 12:38:23</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/material.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:google_fonts/google_fonts.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:water_metering/utils/pok.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : enum AlertType { success, error, warning, info }</span>
<span id="L6"><span class="lineNum">       6</span>              : </span>
<span id="L7"><span class="lineNum">       7</span>              : class CustomAlert {</span>
<span id="L8"><span class="lineNum">       8</span> <span class="tlaGNC">           1 :   static showCustomScaffoldMessenger(</span></span>
<span id="L9"><span class="lineNum">       9</span>              :       BuildContext context, String message, AlertType alertType,</span>
<span id="L10"><span class="lineNum">      10</span>              :       {Duration duration = const Duration(seconds: 2)}) {</span>
<span id="L11"><span class="lineNum">      11</span> <span class="tlaGNC">           3 :     Color color = _alertColors[alertType]![0];</span></span>
<span id="L12"><span class="lineNum">      12</span> <span class="tlaGNC">           3 :     Color colorAccent = _alertColors[alertType]![1];</span></span>
<span id="L13"><span class="lineNum">      13</span> <span class="tlaGNC">           4 :     String alertName = _capitalize(alertType.toString().split('.').last);</span></span>
<span id="L14"><span class="lineNum">      14</span> <span class="tlaGNC">           2 :     ScaffoldMessenger.of(context).showSnackBar(</span></span>
<span id="L15"><span class="lineNum">      15</span> <span class="tlaGNC">           1 :       SnackBar(</span></span>
<span id="L16"><span class="lineNum">      16</span>              :           duration: duration,</span>
<span id="L17"><span class="lineNum">      17</span> <span class="tlaGNC">           1 :           content: RichText(</span></span>
<span id="L18"><span class="lineNum">      18</span> <span class="tlaGNC">           1 :             text: TextSpan(</span></span>
<span id="L19"><span class="lineNum">      19</span> <span class="tlaGNC">           1 :               children: [</span></span>
<span id="L20"><span class="lineNum">      20</span> <span class="tlaGNC">           1 :                 TextSpan(</span></span>
<span id="L21"><span class="lineNum">      21</span> <span class="tlaGNC">           1 :                   text: '$alertName! ',</span></span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaGNC">           1 :                   style: GoogleFonts.roboto(</span></span>
<span id="L23"><span class="lineNum">      23</span> <span class="tlaGNC">           1 :                       fontSize: 18.minSp,</span></span>
<span id="L24"><span class="lineNum">      24</span>              :                       fontWeight: FontWeight.bold,</span>
<span id="L25"><span class="lineNum">      25</span>              :                       color: color),</span>
<span id="L26"><span class="lineNum">      26</span>              :                 ),</span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaGNC">           1 :                 TextSpan(</span></span>
<span id="L28"><span class="lineNum">      28</span>              :                   text: message,</span>
<span id="L29"><span class="lineNum">      29</span> <span class="tlaGNC">           2 :                   style: GoogleFonts.roboto(fontSize: 18.minSp, color: color),</span></span>
<span id="L30"><span class="lineNum">      30</span>              :                 ),</span>
<span id="L31"><span class="lineNum">      31</span>              :               ],</span>
<span id="L32"><span class="lineNum">      32</span>              :             ),</span>
<span id="L33"><span class="lineNum">      33</span>              :           ),</span>
<span id="L34"><span class="lineNum">      34</span>              :           backgroundColor: colorAccent,</span>
<span id="L35"><span class="lineNum">      35</span>              :           behavior: SnackBarBehavior.floating,</span>
<span id="L36"><span class="lineNum">      36</span> <span class="tlaGNC">           1 :           shape: RoundedRectangleBorder(</span></span>
<span id="L37"><span class="lineNum">      37</span> <span class="tlaGNC">           1 :             borderRadius: BorderRadius.circular(10),</span></span>
<span id="L38"><span class="lineNum">      38</span>              :           )),</span>
<span id="L39"><span class="lineNum">      39</span>              :     );</span>
<span id="L40"><span class="lineNum">      40</span>              :   }</span>
<span id="L41"><span class="lineNum">      41</span>              : </span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaUNC">           0 :   static Widget customAlertWidget(String message, AlertType alertType,</span></span>
<span id="L43"><span class="lineNum">      43</span>              :       {required BuildContext context}) {</span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaUNC">           0 :     Color color = _alertColors[alertType]![0];</span></span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC">           0 :     Color colorAccent = _alertColors[alertType]![1];</span></span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :     String alertName = _capitalize(alertType.toString().split('.').last);</span></span>
<span id="L47"><span class="lineNum">      47</span>              : </span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaUNC">           0 :     return Container(</span></span>
<span id="L49"><span class="lineNum">      49</span>              :       width: double.infinity,</span>
<span id="L50"><span class="lineNum">      50</span>              :       margin: const EdgeInsets.all(8.0),</span>
<span id="L51"><span class="lineNum">      51</span>              :       padding: const EdgeInsets.all(16.0),</span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaUNC">           0 :       decoration: BoxDecoration(</span></span>
<span id="L53"><span class="lineNum">      53</span>              :         color: colorAccent,</span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :         borderRadius: BorderRadius.circular(10),</span></span>
<span id="L55"><span class="lineNum">      55</span>              :       ),</span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaUNC">           0 :       child: RichText(</span></span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaUNC">           0 :         text: TextSpan(</span></span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaUNC">           0 :           children: [</span></span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaUNC">           0 :             TextSpan(</span></span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaUNC">           0 :               text: '$alertName! ',</span></span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaUNC">           0 :               style: GoogleFonts.roboto(</span></span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaUNC">           0 :                   fontSize: 18.minSp,</span></span>
<span id="L63"><span class="lineNum">      63</span>              :                   fontWeight: FontWeight.bold,</span>
<span id="L64"><span class="lineNum">      64</span>              :                   color: color),</span>
<span id="L65"><span class="lineNum">      65</span>              :             ),</span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaUNC">           0 :             TextSpan(</span></span>
<span id="L67"><span class="lineNum">      67</span>              :               text: message,</span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaUNC">           0 :               style: GoogleFonts.roboto(fontSize: 18.minSp, color: color),</span></span>
<span id="L69"><span class="lineNum">      69</span>              :             ),</span>
<span id="L70"><span class="lineNum">      70</span>              :           ],</span>
<span id="L71"><span class="lineNum">      71</span>              :         ),</span>
<span id="L72"><span class="lineNum">      72</span>              :       ),</span>
<span id="L73"><span class="lineNum">      73</span>              :     );</span>
<span id="L74"><span class="lineNum">      74</span>              :   }</span>
<span id="L75"><span class="lineNum">      75</span>              : </span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaGNC">           1 :   static String _capitalize(String text) {</span></span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaGNC">           1 :     if (text.isEmpty) {</span></span>
<span id="L78"><span class="lineNum">      78</span>              :       return text;</span>
<span id="L79"><span class="lineNum">      79</span>              :     } else {</span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaGNC">           4 :       return text[0].toUpperCase() + text.substring(1);</span></span>
<span id="L81"><span class="lineNum">      81</span>              :     }</span>
<span id="L82"><span class="lineNum">      82</span>              :   }</span>
<span id="L83"><span class="lineNum">      83</span>              : </span>
<span id="L84"><span class="lineNum">      84</span> <span class="tlaGNC">           3 :   static final Map&lt;AlertType, List&lt;Color&gt;&gt; _alertColors = {</span></span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaGNC">           1 :     AlertType.success: [const Color(0xFF0b5b6a), const Color(0xFFd1e7dd)],</span></span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaGNC">           1 :     AlertType.error: [const Color(0xFF992837), const Color(0xFFf8d7da)],</span></span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaGNC">           1 :     AlertType.warning: [const Color(0xFF694d03), const Color(0xFFfff3cd)],</span></span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaGNC">           1 :     AlertType.info: [const Color(0xFF075165), const Color(0xFFcff4fc)],</span></span>
<span id="L89"><span class="lineNum">      89</span>              :   };</span>
<span id="L90"><span class="lineNum">      90</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
