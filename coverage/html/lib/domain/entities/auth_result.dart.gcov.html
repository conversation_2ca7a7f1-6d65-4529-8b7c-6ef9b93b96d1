<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/domain/entities/auth_result.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/domain/entities">lib/domain/entities</a> - auth_result.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">61</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-07-03 12:38:23</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:equatable/equatable.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'auth_user.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import '../../core/error/auth_failures.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : /// Domain entity representing the result of an authentication operation</span>
<span id="L6"><span class="lineNum">       6</span>              : ///</span>
<span id="L7"><span class="lineNum">       7</span>              : /// This entity encapsulates the outcome of authentication attempts including</span>
<span id="L8"><span class="lineNum">       8</span>              : /// login, two-factor verification, and token refresh operations.</span>
<span id="L9"><span class="lineNum">       9</span>              : /// It is immutable and follows clean architecture principles.</span>
<span id="L10"><span class="lineNum">      10</span>              : class AuthResult extends Equatable {</span>
<span id="L11"><span class="lineNum">      11</span>              :   /// Whether the authentication operation was successful</span>
<span id="L12"><span class="lineNum">      12</span>              :   final bool success;</span>
<span id="L13"><span class="lineNum">      13</span>              : </span>
<span id="L14"><span class="lineNum">      14</span>              :   /// The authenticated user (if authentication was successful)</span>
<span id="L15"><span class="lineNum">      15</span>              :   final AuthUser? user;</span>
<span id="L16"><span class="lineNum">      16</span>              : </span>
<span id="L17"><span class="lineNum">      17</span>              :   /// Two-factor authentication reference code (if 2FA is required)</span>
<span id="L18"><span class="lineNum">      18</span>              :   final String? twoFactorRefCode;</span>
<span id="L19"><span class="lineNum">      19</span>              : </span>
<span id="L20"><span class="lineNum">      20</span>              :   /// Error message (if authentication failed) - deprecated, use failure instead</span>
<span id="L21"><span class="lineNum">      21</span>              :   @Deprecated('Use failure instead for better error handling')</span>
<span id="L22"><span class="lineNum">      22</span>              :   final String? error;</span>
<span id="L23"><span class="lineNum">      23</span>              : </span>
<span id="L24"><span class="lineNum">      24</span>              :   /// Authentication failure (if authentication failed)</span>
<span id="L25"><span class="lineNum">      25</span>              :   final AuthFailure? failure;</span>
<span id="L26"><span class="lineNum">      26</span>              : </span>
<span id="L27"><span class="lineNum">      27</span>              :   /// Whether two-factor authentication is required</span>
<span id="L28"><span class="lineNum">      28</span>              :   final bool requiresTwoFactor;</span>
<span id="L29"><span class="lineNum">      29</span>              : </span>
<span id="L30"><span class="lineNum">      30</span>              :   /// Access token (if authentication was successful)</span>
<span id="L31"><span class="lineNum">      31</span>              :   final String? accessToken;</span>
<span id="L32"><span class="lineNum">      32</span>              : </span>
<span id="L33"><span class="lineNum">      33</span>              :   /// Refresh token (if authentication was successful)</span>
<span id="L34"><span class="lineNum">      34</span>              :   final String? refreshToken;</span>
<span id="L35"><span class="lineNum">      35</span>              : </span>
<span id="L36"><span class="lineNum">      36</span> <span class="tlaUNC">           0 :   const AuthResult({</span></span>
<span id="L37"><span class="lineNum">      37</span>              :     required this.success,</span>
<span id="L38"><span class="lineNum">      38</span>              :     this.user,</span>
<span id="L39"><span class="lineNum">      39</span>              :     this.twoFactorRefCode,</span>
<span id="L40"><span class="lineNum">      40</span>              :     this.error,</span>
<span id="L41"><span class="lineNum">      41</span>              :     this.failure,</span>
<span id="L42"><span class="lineNum">      42</span>              :     this.requiresTwoFactor = false,</span>
<span id="L43"><span class="lineNum">      43</span>              :     this.accessToken,</span>
<span id="L44"><span class="lineNum">      44</span>              :     this.refreshToken,</span>
<span id="L45"><span class="lineNum">      45</span>              :   });</span>
<span id="L46"><span class="lineNum">      46</span>              : </span>
<span id="L47"><span class="lineNum">      47</span>              :   /// Creates a successful authentication result</span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaUNC">           0 :   factory AuthResult.success({</span></span>
<span id="L49"><span class="lineNum">      49</span>              :     required AuthUser user,</span>
<span id="L50"><span class="lineNum">      50</span>              :     String? accessToken,</span>
<span id="L51"><span class="lineNum">      51</span>              :     String? refreshToken,</span>
<span id="L52"><span class="lineNum">      52</span>              :   }) {</span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaUNC">           0 :     return AuthResult(</span></span>
<span id="L54"><span class="lineNum">      54</span>              :       success: true,</span>
<span id="L55"><span class="lineNum">      55</span>              :       user: user,</span>
<span id="L56"><span class="lineNum">      56</span>              :       accessToken: accessToken,</span>
<span id="L57"><span class="lineNum">      57</span>              :       refreshToken: refreshToken,</span>
<span id="L58"><span class="lineNum">      58</span>              :     );</span>
<span id="L59"><span class="lineNum">      59</span>              :   }</span>
<span id="L60"><span class="lineNum">      60</span>              : </span>
<span id="L61"><span class="lineNum">      61</span>              :   /// Creates a result indicating two-factor authentication is required</span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaUNC">           0 :   factory AuthResult.twoFactorRequired({</span></span>
<span id="L63"><span class="lineNum">      63</span>              :     required String twoFactorRefCode,</span>
<span id="L64"><span class="lineNum">      64</span>              :   }) {</span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaUNC">           0 :     return AuthResult(</span></span>
<span id="L66"><span class="lineNum">      66</span>              :       success: false,</span>
<span id="L67"><span class="lineNum">      67</span>              :       requiresTwoFactor: true,</span>
<span id="L68"><span class="lineNum">      68</span>              :       twoFactorRefCode: twoFactorRefCode,</span>
<span id="L69"><span class="lineNum">      69</span>              :     );</span>
<span id="L70"><span class="lineNum">      70</span>              :   }</span>
<span id="L71"><span class="lineNum">      71</span>              : </span>
<span id="L72"><span class="lineNum">      72</span>              :   /// Creates a failed authentication result</span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :   factory AuthResult.failure({</span></span>
<span id="L74"><span class="lineNum">      74</span>              :     String? error,</span>
<span id="L75"><span class="lineNum">      75</span>              :     AuthFailure? failure,</span>
<span id="L76"><span class="lineNum">      76</span>              :   }) {</span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :     return AuthResult(</span></span>
<span id="L78"><span class="lineNum">      78</span>              :       success: false,</span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaUNC">           0 :       error: error ?? failure?.message,</span></span>
<span id="L80"><span class="lineNum">      80</span>              :       failure: failure,</span>
<span id="L81"><span class="lineNum">      81</span>              :     );</span>
<span id="L82"><span class="lineNum">      82</span>              :   }</span>
<span id="L83"><span class="lineNum">      83</span>              : </span>
<span id="L84"><span class="lineNum">      84</span>              :   /// Creates a failed authentication result with AuthFailure</span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaUNC">           0 :   factory AuthResult.withFailure({</span></span>
<span id="L86"><span class="lineNum">      86</span>              :     required AuthFailure failure,</span>
<span id="L87"><span class="lineNum">      87</span>              :   }) {</span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaUNC">           0 :     return AuthResult(</span></span>
<span id="L89"><span class="lineNum">      89</span>              :       success: false,</span>
<span id="L90"><span class="lineNum">      90</span>              :       failure: failure,</span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaUNC">           0 :       error: failure.message, // Keep for backward compatibility</span></span>
<span id="L92"><span class="lineNum">      92</span>              :     );</span>
<span id="L93"><span class="lineNum">      93</span>              :   }</span>
<span id="L94"><span class="lineNum">      94</span>              : </span>
<span id="L95"><span class="lineNum">      95</span>              :   /// Creates an AuthResult instance from a JSON map</span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaUNC">           0 :   factory AuthResult.fromJson(Map&lt;String, dynamic&gt; json) {</span></span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaUNC">           0 :     return AuthResult(</span></span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaUNC">           0 :       success: json['success'] ?? false,</span></span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaUNC">           0 :       user: json['user'] != null ? AuthUser.fromJson(json['user']) : null,</span></span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaUNC">           0 :       twoFactorRefCode: json['twoFactorRefCode'],</span></span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaUNC">           0 :       error: json['error'],</span></span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaUNC">           0 :       requiresTwoFactor: json['requiresTwoFactor'] ?? false,</span></span>
<span id="L103"><span class="lineNum">     103</span> <span class="tlaUNC">           0 :       accessToken: json['accessToken'],</span></span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaUNC">           0 :       refreshToken: json['refreshToken'],</span></span>
<span id="L105"><span class="lineNum">     105</span>              :     );</span>
<span id="L106"><span class="lineNum">     106</span>              :   }</span>
<span id="L107"><span class="lineNum">     107</span>              : </span>
<span id="L108"><span class="lineNum">     108</span>              :   /// Converts the AuthResult instance to a JSON map</span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaUNC">           0 :   Map&lt;String, dynamic&gt; toJson() {</span></span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaUNC">           0 :     return {</span></span>
<span id="L111"><span class="lineNum">     111</span> <span class="tlaUNC">           0 :       'success': success,</span></span>
<span id="L112"><span class="lineNum">     112</span> <span class="tlaUNC">           0 :       if (user != null) 'user': user!.toJson(),</span></span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaUNC">           0 :       if (twoFactorRefCode != null) 'twoFactorRefCode': twoFactorRefCode,</span></span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaUNC">           0 :       if (error != null) 'error': error,</span></span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaUNC">           0 :       'requiresTwoFactor': requiresTwoFactor,</span></span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaUNC">           0 :       if (accessToken != null) 'accessToken': accessToken,</span></span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaUNC">           0 :       if (refreshToken != null) 'refreshToken': refreshToken,</span></span>
<span id="L118"><span class="lineNum">     118</span>              :     };</span>
<span id="L119"><span class="lineNum">     119</span>              :   }</span>
<span id="L120"><span class="lineNum">     120</span>              : </span>
<span id="L121"><span class="lineNum">     121</span>              :   /// Creates a copy of this AuthResult with the given fields replaced with new values</span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaUNC">           0 :   AuthResult copyWith({</span></span>
<span id="L123"><span class="lineNum">     123</span>              :     bool? success,</span>
<span id="L124"><span class="lineNum">     124</span>              :     AuthUser? user,</span>
<span id="L125"><span class="lineNum">     125</span>              :     String? twoFactorRefCode,</span>
<span id="L126"><span class="lineNum">     126</span>              :     String? error,</span>
<span id="L127"><span class="lineNum">     127</span>              :     AuthFailure? failure,</span>
<span id="L128"><span class="lineNum">     128</span>              :     bool? requiresTwoFactor,</span>
<span id="L129"><span class="lineNum">     129</span>              :     String? accessToken,</span>
<span id="L130"><span class="lineNum">     130</span>              :     String? refreshToken,</span>
<span id="L131"><span class="lineNum">     131</span>              :   }) {</span>
<span id="L132"><span class="lineNum">     132</span> <span class="tlaUNC">           0 :     return AuthResult(</span></span>
<span id="L133"><span class="lineNum">     133</span> <span class="tlaUNC">           0 :       success: success ?? this.success,</span></span>
<span id="L134"><span class="lineNum">     134</span> <span class="tlaUNC">           0 :       user: user ?? this.user,</span></span>
<span id="L135"><span class="lineNum">     135</span> <span class="tlaUNC">           0 :       twoFactorRefCode: twoFactorRefCode ?? this.twoFactorRefCode,</span></span>
<span id="L136"><span class="lineNum">     136</span> <span class="tlaUNC">           0 :       error: error ?? this.error,</span></span>
<span id="L137"><span class="lineNum">     137</span> <span class="tlaUNC">           0 :       failure: failure ?? this.failure,</span></span>
<span id="L138"><span class="lineNum">     138</span> <span class="tlaUNC">           0 :       requiresTwoFactor: requiresTwoFactor ?? this.requiresTwoFactor,</span></span>
<span id="L139"><span class="lineNum">     139</span> <span class="tlaUNC">           0 :       accessToken: accessToken ?? this.accessToken,</span></span>
<span id="L140"><span class="lineNum">     140</span> <span class="tlaUNC">           0 :       refreshToken: refreshToken ?? this.refreshToken,</span></span>
<span id="L141"><span class="lineNum">     141</span>              :     );</span>
<span id="L142"><span class="lineNum">     142</span>              :   }</span>
<span id="L143"><span class="lineNum">     143</span>              : </span>
<span id="L144"><span class="lineNum">     144</span>              :   /// Returns whether the result has authentication tokens</span>
<span id="L145"><span class="lineNum">     145</span> <span class="tlaUNC">           0 :   bool get hasTokens {</span></span>
<span id="L146"><span class="lineNum">     146</span> <span class="tlaUNC">           0 :     return accessToken != null &amp;&amp; refreshToken != null;</span></span>
<span id="L147"><span class="lineNum">     147</span>              :   }</span>
<span id="L148"><span class="lineNum">     148</span>              : </span>
<span id="L149"><span class="lineNum">     149</span>              :   /// Returns whether the authentication is complete (successful and has tokens)</span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaUNC">           0 :   bool get isComplete {</span></span>
<span id="L151"><span class="lineNum">     151</span> <span class="tlaUNC">           0 :     return success &amp;&amp; hasTokens &amp;&amp; user != null;</span></span>
<span id="L152"><span class="lineNum">     152</span>              :   }</span>
<span id="L153"><span class="lineNum">     153</span>              : </span>
<span id="L154"><span class="lineNum">     154</span>              :   /// Returns whether the result indicates a failure</span>
<span id="L155"><span class="lineNum">     155</span> <span class="tlaUNC">           0 :   bool get isFailure {</span></span>
<span id="L156"><span class="lineNum">     156</span> <span class="tlaUNC">           0 :     return !success &amp;&amp; !requiresTwoFactor;</span></span>
<span id="L157"><span class="lineNum">     157</span>              :   }</span>
<span id="L158"><span class="lineNum">     158</span>              : </span>
<span id="L159"><span class="lineNum">     159</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L160"><span class="lineNum">     160</span> <span class="tlaUNC">           0 :   List&lt;Object?&gt; get props =&gt; [</span></span>
<span id="L161"><span class="lineNum">     161</span> <span class="tlaUNC">           0 :         success,</span></span>
<span id="L162"><span class="lineNum">     162</span> <span class="tlaUNC">           0 :         user,</span></span>
<span id="L163"><span class="lineNum">     163</span> <span class="tlaUNC">           0 :         twoFactorRefCode,</span></span>
<span id="L164"><span class="lineNum">     164</span> <span class="tlaUNC">           0 :         error,</span></span>
<span id="L165"><span class="lineNum">     165</span> <span class="tlaUNC">           0 :         failure,</span></span>
<span id="L166"><span class="lineNum">     166</span> <span class="tlaUNC">           0 :         requiresTwoFactor,</span></span>
<span id="L167"><span class="lineNum">     167</span> <span class="tlaUNC">           0 :         accessToken,</span></span>
<span id="L168"><span class="lineNum">     168</span> <span class="tlaUNC">           0 :         refreshToken,</span></span>
<span id="L169"><span class="lineNum">     169</span>              :       ];</span>
<span id="L170"><span class="lineNum">     170</span>              : </span>
<span id="L171"><span class="lineNum">     171</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L172"><span class="lineNum">     172</span>              :   String toString() {</span>
<span id="L173"><span class="lineNum">     173</span> <span class="tlaUNC">           0 :     if (success) {</span></span>
<span id="L174"><span class="lineNum">     174</span> <span class="tlaUNC">           0 :       return 'AuthResult.success(user: ${user?.email}, hasTokens: $hasTokens)';</span></span>
<span id="L175"><span class="lineNum">     175</span> <span class="tlaUNC">           0 :     } else if (requiresTwoFactor) {</span></span>
<span id="L176"><span class="lineNum">     176</span> <span class="tlaUNC">           0 :       return 'AuthResult.twoFactorRequired(refCode: $twoFactorRefCode)';</span></span>
<span id="L177"><span class="lineNum">     177</span>              :     } else {</span>
<span id="L178"><span class="lineNum">     178</span> <span class="tlaUNC">           0 :       return 'AuthResult.failure(failure: ${failure?.code ?? error})';</span></span>
<span id="L179"><span class="lineNum">     179</span>              :     }</span>
<span id="L180"><span class="lineNum">     180</span>              :   }</span>
<span id="L181"><span class="lineNum">     181</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
