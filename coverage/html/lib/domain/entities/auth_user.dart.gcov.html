<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/domain/entities/auth_user.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/domain/entities">lib/domain/entities</a> - auth_user.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">53</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-07-03 12:38:23</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:equatable/equatable.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : /// Domain entity representing an authenticated user</span>
<span id="L4"><span class="lineNum">       4</span>              : /// </span>
<span id="L5"><span class="lineNum">       5</span>              : /// This entity encapsulates all user-related information in the authentication domain.</span>
<span id="L6"><span class="lineNum">       6</span>              : /// It is immutable and follows clean architecture principles by having no external dependencies.</span>
<span id="L7"><span class="lineNum">       7</span>              : class AuthUser extends Equatable {</span>
<span id="L8"><span class="lineNum">       8</span>              :   /// Unique identifier for the user</span>
<span id="L9"><span class="lineNum">       9</span>              :   final String id;</span>
<span id="L10"><span class="lineNum">      10</span>              :   </span>
<span id="L11"><span class="lineNum">      11</span>              :   /// User's full name</span>
<span id="L12"><span class="lineNum">      12</span>              :   final String name;</span>
<span id="L13"><span class="lineNum">      13</span>              :   </span>
<span id="L14"><span class="lineNum">      14</span>              :   /// User's email address</span>
<span id="L15"><span class="lineNum">      15</span>              :   final String email;</span>
<span id="L16"><span class="lineNum">      16</span>              :   </span>
<span id="L17"><span class="lineNum">      17</span>              :   /// Whether the user's email has been verified</span>
<span id="L18"><span class="lineNum">      18</span>              :   final bool emailVerified;</span>
<span id="L19"><span class="lineNum">      19</span>              :   </span>
<span id="L20"><span class="lineNum">      20</span>              :   /// User's phone number</span>
<span id="L21"><span class="lineNum">      21</span>              :   final String phone;</span>
<span id="L22"><span class="lineNum">      22</span>              :   </span>
<span id="L23"><span class="lineNum">      23</span>              :   /// Whether the user's phone number has been verified</span>
<span id="L24"><span class="lineNum">      24</span>              :   final bool phoneVerified;</span>
<span id="L25"><span class="lineNum">      25</span>              :   </span>
<span id="L26"><span class="lineNum">      26</span>              :   /// Timestamp of the last password change (in milliseconds since epoch)</span>
<span id="L27"><span class="lineNum">      27</span>              :   final String? lastPassChange;</span>
<span id="L28"><span class="lineNum">      28</span>              :   </span>
<span id="L29"><span class="lineNum">      29</span>              :   /// Timestamp of the last profile update (in milliseconds since epoch)</span>
<span id="L30"><span class="lineNum">      30</span>              :   final int? lastUpdate;</span>
<span id="L31"><span class="lineNum">      31</span>              :   </span>
<span id="L32"><span class="lineNum">      32</span>              :   /// Multi-factor authentication status ('0', 'app', 'sms')</span>
<span id="L33"><span class="lineNum">      33</span>              :   final String? multiFactor;</span>
<span id="L34"><span class="lineNum">      34</span>              : </span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaUNC">           0 :   const AuthUser({</span></span>
<span id="L36"><span class="lineNum">      36</span>              :     required this.id,</span>
<span id="L37"><span class="lineNum">      37</span>              :     required this.name,</span>
<span id="L38"><span class="lineNum">      38</span>              :     required this.email,</span>
<span id="L39"><span class="lineNum">      39</span>              :     required this.emailVerified,</span>
<span id="L40"><span class="lineNum">      40</span>              :     required this.phone,</span>
<span id="L41"><span class="lineNum">      41</span>              :     required this.phoneVerified,</span>
<span id="L42"><span class="lineNum">      42</span>              :     this.lastPassChange,</span>
<span id="L43"><span class="lineNum">      43</span>              :     this.lastUpdate,</span>
<span id="L44"><span class="lineNum">      44</span>              :     this.multiFactor,</span>
<span id="L45"><span class="lineNum">      45</span>              :   });</span>
<span id="L46"><span class="lineNum">      46</span>              : </span>
<span id="L47"><span class="lineNum">      47</span>              :   /// Creates an AuthUser instance from a JSON map</span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaUNC">           0 :   factory AuthUser.fromJson(Map&lt;String, dynamic&gt; json) {</span></span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaUNC">           0 :     return AuthUser(</span></span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaUNC">           0 :       id: json['userID'] ?? json['id'] ?? '',</span></span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaUNC">           0 :       name: json['name'] ?? '',</span></span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaUNC">           0 :       email: json['email'] ?? '',</span></span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaUNC">           0 :       emailVerified: json['emailVerified'] ?? false,</span></span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :       phone: json['phone'] ?? '',</span></span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaUNC">           0 :       phoneVerified: json['phoneVerified'] ?? false,</span></span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaUNC">           0 :       lastPassChange: json['lastPassChange'],</span></span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaUNC">           0 :       lastUpdate: json['lastUpdate'],</span></span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaUNC">           0 :       multiFactor: json['multiFactor'],</span></span>
<span id="L59"><span class="lineNum">      59</span>              :     );</span>
<span id="L60"><span class="lineNum">      60</span>              :   }</span>
<span id="L61"><span class="lineNum">      61</span>              : </span>
<span id="L62"><span class="lineNum">      62</span>              :   /// Converts the AuthUser instance to a JSON map</span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaUNC">           0 :   Map&lt;String, dynamic&gt; toJson() {</span></span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :     return {</span></span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaUNC">           0 :       'id': id,</span></span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaUNC">           0 :       'userID': id, // For backward compatibility</span></span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :       'name': name,</span></span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaUNC">           0 :       'email': email,</span></span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :       'emailVerified': emailVerified,</span></span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :       'phone': phone,</span></span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :       'phoneVerified': phoneVerified,</span></span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaUNC">           0 :       if (lastPassChange != null) 'lastPassChange': lastPassChange,</span></span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :       if (lastUpdate != null) 'lastUpdate': lastUpdate,</span></span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaUNC">           0 :       if (multiFactor != null) 'multiFactor': multiFactor,</span></span>
<span id="L75"><span class="lineNum">      75</span>              :     };</span>
<span id="L76"><span class="lineNum">      76</span>              :   }</span>
<span id="L77"><span class="lineNum">      77</span>              : </span>
<span id="L78"><span class="lineNum">      78</span>              :   /// Creates a copy of this AuthUser with the given fields replaced with new values</span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaUNC">           0 :   AuthUser copyWith({</span></span>
<span id="L80"><span class="lineNum">      80</span>              :     String? id,</span>
<span id="L81"><span class="lineNum">      81</span>              :     String? name,</span>
<span id="L82"><span class="lineNum">      82</span>              :     String? email,</span>
<span id="L83"><span class="lineNum">      83</span>              :     bool? emailVerified,</span>
<span id="L84"><span class="lineNum">      84</span>              :     String? phone,</span>
<span id="L85"><span class="lineNum">      85</span>              :     bool? phoneVerified,</span>
<span id="L86"><span class="lineNum">      86</span>              :     String? lastPassChange,</span>
<span id="L87"><span class="lineNum">      87</span>              :     int? lastUpdate,</span>
<span id="L88"><span class="lineNum">      88</span>              :     String? multiFactor,</span>
<span id="L89"><span class="lineNum">      89</span>              :   }) {</span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaUNC">           0 :     return AuthUser(</span></span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaUNC">           0 :       id: id ?? this.id,</span></span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaUNC">           0 :       name: name ?? this.name,</span></span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaUNC">           0 :       email: email ?? this.email,</span></span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaUNC">           0 :       emailVerified: emailVerified ?? this.emailVerified,</span></span>
<span id="L95"><span class="lineNum">      95</span> <span class="tlaUNC">           0 :       phone: phone ?? this.phone,</span></span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaUNC">           0 :       phoneVerified: phoneVerified ?? this.phoneVerified,</span></span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaUNC">           0 :       lastPassChange: lastPassChange ?? this.lastPassChange,</span></span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaUNC">           0 :       lastUpdate: lastUpdate ?? this.lastUpdate,</span></span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaUNC">           0 :       multiFactor: multiFactor ?? this.multiFactor,</span></span>
<span id="L100"><span class="lineNum">     100</span>              :     );</span>
<span id="L101"><span class="lineNum">     101</span>              :   }</span>
<span id="L102"><span class="lineNum">     102</span>              : </span>
<span id="L103"><span class="lineNum">     103</span>              :   /// Returns whether the user has two-factor authentication enabled</span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaUNC">           0 :   bool get hasTwoFactorEnabled {</span></span>
<span id="L105"><span class="lineNum">     105</span> <span class="tlaUNC">           0 :     return multiFactor == 'app' || multiFactor == 'sms';</span></span>
<span id="L106"><span class="lineNum">     106</span>              :   }</span>
<span id="L107"><span class="lineNum">     107</span>              : </span>
<span id="L108"><span class="lineNum">     108</span>              :   /// Returns whether both email and phone are verified</span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaUNC">           0 :   bool get isFullyVerified {</span></span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaUNC">           0 :     return emailVerified &amp;&amp; phoneVerified;</span></span>
<span id="L111"><span class="lineNum">     111</span>              :   }</span>
<span id="L112"><span class="lineNum">     112</span>              : </span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaUNC">           0 :   List&lt;Object?&gt; get props =&gt; [</span></span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaUNC">           0 :         id,</span></span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaUNC">           0 :         name,</span></span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaUNC">           0 :         email,</span></span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaUNC">           0 :         emailVerified,</span></span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaUNC">           0 :         phone,</span></span>
<span id="L120"><span class="lineNum">     120</span> <span class="tlaUNC">           0 :         phoneVerified,</span></span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaUNC">           0 :         lastPassChange,</span></span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaUNC">           0 :         lastUpdate,</span></span>
<span id="L123"><span class="lineNum">     123</span> <span class="tlaUNC">           0 :         multiFactor,</span></span>
<span id="L124"><span class="lineNum">     124</span>              :       ];</span>
<span id="L125"><span class="lineNum">     125</span>              : </span>
<span id="L126"><span class="lineNum">     126</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L127"><span class="lineNum">     127</span>              :   String toString() {</span>
<span id="L128"><span class="lineNum">     128</span> <span class="tlaUNC">           0 :     return 'AuthUser(id: $id, name: $name, email: $email, emailVerified: $emailVerified, '</span></span>
<span id="L129"><span class="lineNum">     129</span> <span class="tlaUNC">           0 :         'phone: $phone, phoneVerified: $phoneVerified, hasTwoFactor: $hasTwoFactorEnabled)';</span></span>
<span id="L130"><span class="lineNum">     130</span>              :   }</span>
<span id="L131"><span class="lineNum">     131</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
