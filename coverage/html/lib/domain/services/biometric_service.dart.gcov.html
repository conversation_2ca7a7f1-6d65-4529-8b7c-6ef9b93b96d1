<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/domain/services/biometric_service.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/domain/services">lib/domain/services</a> - biometric_service.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">16.7&nbsp;%</td>
            <td class="headerCovTableEntry">12</td>
            <td class="headerCovTableEntry">2</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-07-03 12:38:23</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:local_auth/local_auth.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import '../../core/error/auth_failures.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : </span>
<span id="L4"><span class="lineNum">       4</span>              : /// Domain service interface for biometric authentication operations</span>
<span id="L5"><span class="lineNum">       5</span>              : ///</span>
<span id="L6"><span class="lineNum">       6</span>              : /// This service defines the contract for biometric authentication functionality</span>
<span id="L7"><span class="lineNum">       7</span>              : /// following clean architecture principles. It serves as the boundary between</span>
<span id="L8"><span class="lineNum">       8</span>              : /// the domain layer and the data layer for biometric operations.</span>
<span id="L9"><span class="lineNum">       9</span>              : ///</span>
<span id="L10"><span class="lineNum">      10</span>              : /// All implementations of this service should handle:</span>
<span id="L11"><span class="lineNum">      11</span>              : /// - Platform-specific biometric authentication (iOS Face ID/Touch ID, Android fingerprint/face unlock)</span>
<span id="L12"><span class="lineNum">      12</span>              : /// - Biometric availability checking</span>
<span id="L13"><span class="lineNum">      13</span>              : /// - Biometric enrollment status</span>
<span id="L14"><span class="lineNum">      14</span>              : /// - Secure biometric authentication flows</span>
<span id="L15"><span class="lineNum">      15</span>              : /// - Error handling and mapping to domain failures</span>
<span id="L16"><span class="lineNum">      16</span>              : abstract class BiometricService {</span>
<span id="L17"><span class="lineNum">      17</span>              :   /// Checks if biometric authentication is available on the device</span>
<span id="L18"><span class="lineNum">      18</span>              :   ///</span>
<span id="L19"><span class="lineNum">      19</span>              :   /// This method verifies:</span>
<span id="L20"><span class="lineNum">      20</span>              :   /// - Device hardware support for biometric authentication</span>
<span id="L21"><span class="lineNum">      21</span>              :   /// - Operating system support for biometric APIs</span>
<span id="L22"><span class="lineNum">      22</span>              :   /// - Biometric authentication framework availability</span>
<span id="L23"><span class="lineNum">      23</span>              :   ///</span>
<span id="L24"><span class="lineNum">      24</span>              :   /// Returns true if biometric authentication is supported by the device,</span>
<span id="L25"><span class="lineNum">      25</span>              :   /// false otherwise.</span>
<span id="L26"><span class="lineNum">      26</span>              :   ///</span>
<span id="L27"><span class="lineNum">      27</span>              :   /// This method should not throw exceptions and should return false</span>
<span id="L28"><span class="lineNum">      28</span>              :   /// for any error conditions.</span>
<span id="L29"><span class="lineNum">      29</span>              :   Future&lt;bool&gt; isAvailable();</span>
<span id="L30"><span class="lineNum">      30</span>              : </span>
<span id="L31"><span class="lineNum">      31</span>              :   /// Checks if biometric authentication is set up and enrolled on the device</span>
<span id="L32"><span class="lineNum">      32</span>              :   ///</span>
<span id="L33"><span class="lineNum">      33</span>              :   /// This method verifies:</span>
<span id="L34"><span class="lineNum">      34</span>              :   /// - Biometric authentication is available (calls isAvailable internally)</span>
<span id="L35"><span class="lineNum">      35</span>              :   /// - At least one biometric (fingerprint, face, etc.) is enrolled</span>
<span id="L36"><span class="lineNum">      36</span>              :   /// - Biometric authentication is ready to use</span>
<span id="L37"><span class="lineNum">      37</span>              :   ///</span>
<span id="L38"><span class="lineNum">      38</span>              :   /// Returns true if biometric authentication is set up and ready to use,</span>
<span id="L39"><span class="lineNum">      39</span>              :   /// false otherwise.</span>
<span id="L40"><span class="lineNum">      40</span>              :   ///</span>
<span id="L41"><span class="lineNum">      41</span>              :   /// This method should not throw exceptions and should return false</span>
<span id="L42"><span class="lineNum">      42</span>              :   /// for any error conditions.</span>
<span id="L43"><span class="lineNum">      43</span>              :   Future&lt;bool&gt; isEnrolled();</span>
<span id="L44"><span class="lineNum">      44</span>              : </span>
<span id="L45"><span class="lineNum">      45</span>              :   /// Authenticates the user using biometric authentication</span>
<span id="L46"><span class="lineNum">      46</span>              :   ///</span>
<span id="L47"><span class="lineNum">      47</span>              :   /// This method performs biometric authentication with the following behavior:</span>
<span id="L48"><span class="lineNum">      48</span>              :   /// - Presents the platform-specific biometric authentication UI</span>
<span id="L49"><span class="lineNum">      49</span>              :   /// - Uses the provided reason text in the authentication prompt</span>
<span id="L50"><span class="lineNum">      50</span>              :   /// - Handles platform-specific authentication options</span>
<span id="L51"><span class="lineNum">      51</span>              :   /// - Returns authentication result</span>
<span id="L52"><span class="lineNum">      52</span>              :   ///</span>
<span id="L53"><span class="lineNum">      53</span>              :   /// Parameters:</span>
<span id="L54"><span class="lineNum">      54</span>              :   /// - [reason]: Localized reason text displayed to the user during authentication</span>
<span id="L55"><span class="lineNum">      55</span>              :   ///</span>
<span id="L56"><span class="lineNum">      56</span>              :   /// Returns [BiometricAuthResult] containing:</span>
<span id="L57"><span class="lineNum">      57</span>              :   /// - Success if biometric authentication succeeds</span>
<span id="L58"><span class="lineNum">      58</span>              :   /// - Failure with specific error type if authentication fails</span>
<span id="L59"><span class="lineNum">      59</span>              :   ///</span>
<span id="L60"><span class="lineNum">      60</span>              :   /// Possible failure types:</span>
<span id="L61"><span class="lineNum">      61</span>              :   /// - BiometricNotAvailableFailure: Device doesn't support biometrics</span>
<span id="L62"><span class="lineNum">      62</span>              :   /// - BiometricNotEnrolledFailure: No biometrics are enrolled</span>
<span id="L63"><span class="lineNum">      63</span>              :   /// - BiometricAuthenticationFailure: Authentication failed (wrong biometric)</span>
<span id="L64"><span class="lineNum">      64</span>              :   /// - BiometricLockedFailure: Biometric authentication is locked</span>
<span id="L65"><span class="lineNum">      65</span>              :   /// - BiometricCancelledFailure: User cancelled authentication</span>
<span id="L66"><span class="lineNum">      66</span>              :   /// - BiometricSystemFailure: System error during authentication</span>
<span id="L67"><span class="lineNum">      67</span>              :   Future&lt;BiometricAuthResult&gt; authenticate({</span>
<span id="L68"><span class="lineNum">      68</span>              :     required String reason,</span>
<span id="L69"><span class="lineNum">      69</span>              :   });</span>
<span id="L70"><span class="lineNum">      70</span>              : </span>
<span id="L71"><span class="lineNum">      71</span>              :   /// Gets the list of available biometric types on the device</span>
<span id="L72"><span class="lineNum">      72</span>              :   ///</span>
<span id="L73"><span class="lineNum">      73</span>              :   /// This method returns the specific biometric authentication methods</span>
<span id="L74"><span class="lineNum">      74</span>              :   /// available on the current device (e.g., fingerprint, face, iris).</span>
<span id="L75"><span class="lineNum">      75</span>              :   ///</span>
<span id="L76"><span class="lineNum">      76</span>              :   /// Returns a list of [BiometricType] values representing the available</span>
<span id="L77"><span class="lineNum">      77</span>              :   /// biometric authentication methods. Returns an empty list if no</span>
<span id="L78"><span class="lineNum">      78</span>              :   /// biometric methods are available.</span>
<span id="L79"><span class="lineNum">      79</span>              :   ///</span>
<span id="L80"><span class="lineNum">      80</span>              :   /// This method should not throw exceptions and should return an empty</span>
<span id="L81"><span class="lineNum">      81</span>              :   /// list for any error conditions.</span>
<span id="L82"><span class="lineNum">      82</span>              :   Future&lt;List&lt;BiometricType&gt;&gt; getAvailableBiometrics();</span>
<span id="L83"><span class="lineNum">      83</span>              : </span>
<span id="L84"><span class="lineNum">      84</span>              :   /// Checks if biometric authentication is enabled for the current user</span>
<span id="L85"><span class="lineNum">      85</span>              :   ///</span>
<span id="L86"><span class="lineNum">      86</span>              :   /// This method checks the user's preference for biometric authentication</span>
<span id="L87"><span class="lineNum">      87</span>              :   /// stored in secure storage. This is separate from device enrollment</span>
<span id="L88"><span class="lineNum">      88</span>              :   /// and represents the user's choice to use biometric authentication</span>
<span id="L89"><span class="lineNum">      89</span>              :   /// for this application.</span>
<span id="L90"><span class="lineNum">      90</span>              :   ///</span>
<span id="L91"><span class="lineNum">      91</span>              :   /// Returns true if the user has enabled biometric authentication</span>
<span id="L92"><span class="lineNum">      92</span>              :   /// for this application, false otherwise.</span>
<span id="L93"><span class="lineNum">      93</span>              :   ///</span>
<span id="L94"><span class="lineNum">      94</span>              :   /// This method should not throw exceptions and should return false</span>
<span id="L95"><span class="lineNum">      95</span>              :   /// for any error conditions.</span>
<span id="L96"><span class="lineNum">      96</span>              :   Future&lt;bool&gt; isBiometricEnabled();</span>
<span id="L97"><span class="lineNum">      97</span>              : </span>
<span id="L98"><span class="lineNum">      98</span>              :   /// Sets the user's preference for biometric authentication</span>
<span id="L99"><span class="lineNum">      99</span>              :   ///</span>
<span id="L100"><span class="lineNum">     100</span>              :   /// This method stores the user's choice to enable or disable biometric</span>
<span id="L101"><span class="lineNum">     101</span>              :   /// authentication for this application in secure storage.</span>
<span id="L102"><span class="lineNum">     102</span>              :   ///</span>
<span id="L103"><span class="lineNum">     103</span>              :   /// Parameters:</span>
<span id="L104"><span class="lineNum">     104</span>              :   /// - [enabled]: Whether to enable biometric authentication for the user</span>
<span id="L105"><span class="lineNum">     105</span>              :   ///</span>
<span id="L106"><span class="lineNum">     106</span>              :   /// Returns [BiometricSettingsResult] containing:</span>
<span id="L107"><span class="lineNum">     107</span>              :   /// - Success if the setting was saved successfully</span>
<span id="L108"><span class="lineNum">     108</span>              :   /// - Failure if there was an error saving the setting</span>
<span id="L109"><span class="lineNum">     109</span>              :   ///</span>
<span id="L110"><span class="lineNum">     110</span>              :   /// This operation should be atomic and either succeed completely or fail</span>
<span id="L111"><span class="lineNum">     111</span>              :   /// without partial state changes.</span>
<span id="L112"><span class="lineNum">     112</span>              :   Future&lt;BiometricSettingsResult&gt; setBiometricEnabled(bool enabled);</span>
<span id="L113"><span class="lineNum">     113</span>              : </span>
<span id="L114"><span class="lineNum">     114</span>              :   /// Checks if the device supports biometric authentication and has enrolled biometrics</span>
<span id="L115"><span class="lineNum">     115</span>              :   ///</span>
<span id="L116"><span class="lineNum">     116</span>              :   /// This is a convenience method that combines [isAvailable] and [isEnrolled]</span>
<span id="L117"><span class="lineNum">     117</span>              :   /// checks to determine if biometric authentication can be used.</span>
<span id="L118"><span class="lineNum">     118</span>              :   ///</span>
<span id="L119"><span class="lineNum">     119</span>              :   /// Returns true if biometric authentication is both available and enrolled,</span>
<span id="L120"><span class="lineNum">     120</span>              :   /// false otherwise.</span>
<span id="L121"><span class="lineNum">     121</span>              :   ///</span>
<span id="L122"><span class="lineNum">     122</span>              :   /// This method should not throw exceptions and should return false</span>
<span id="L123"><span class="lineNum">     123</span>              :   /// for any error conditions.</span>
<span id="L124"><span class="lineNum">     124</span>              :   Future&lt;bool&gt; canUseBiometric();</span>
<span id="L125"><span class="lineNum">     125</span>              : }</span>
<span id="L126"><span class="lineNum">     126</span>              : </span>
<span id="L127"><span class="lineNum">     127</span>              : /// Result of a biometric authentication operation</span>
<span id="L128"><span class="lineNum">     128</span>              : class BiometricAuthResult {</span>
<span id="L129"><span class="lineNum">     129</span>              :   /// Whether the authentication was successful</span>
<span id="L130"><span class="lineNum">     130</span>              :   final bool success;</span>
<span id="L131"><span class="lineNum">     131</span>              : </span>
<span id="L132"><span class="lineNum">     132</span>              :   /// Authentication failure (if authentication failed)</span>
<span id="L133"><span class="lineNum">     133</span>              :   final AuthFailure? failure;</span>
<span id="L134"><span class="lineNum">     134</span>              : </span>
<span id="L135"><span class="lineNum">     135</span> <span class="tlaGNC">           1 :   const BiometricAuthResult._({</span></span>
<span id="L136"><span class="lineNum">     136</span>              :     required this.success,</span>
<span id="L137"><span class="lineNum">     137</span>              :     this.failure,</span>
<span id="L138"><span class="lineNum">     138</span>              :   });</span>
<span id="L139"><span class="lineNum">     139</span>              : </span>
<span id="L140"><span class="lineNum">     140</span>              :   /// Creates a successful biometric authentication result</span>
<span id="L141"><span class="lineNum">     141</span> <span class="tlaUNC">           0 :   factory BiometricAuthResult.success() {</span></span>
<span id="L142"><span class="lineNum">     142</span>              :     return const BiometricAuthResult._(success: true);</span>
<span id="L143"><span class="lineNum">     143</span>              :   }</span>
<span id="L144"><span class="lineNum">     144</span>              : </span>
<span id="L145"><span class="lineNum">     145</span>              :   /// Creates a failed biometric authentication result</span>
<span id="L146"><span class="lineNum">     146</span> <span class="tlaUNC">           0 :   factory BiometricAuthResult.failure(AuthFailure failure) {</span></span>
<span id="L147"><span class="lineNum">     147</span> <span class="tlaUNC">           0 :     return BiometricAuthResult._(</span></span>
<span id="L148"><span class="lineNum">     148</span>              :       success: false,</span>
<span id="L149"><span class="lineNum">     149</span>              :       failure: failure,</span>
<span id="L150"><span class="lineNum">     150</span>              :     );</span>
<span id="L151"><span class="lineNum">     151</span>              :   }</span>
<span id="L152"><span class="lineNum">     152</span>              : </span>
<span id="L153"><span class="lineNum">     153</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L154"><span class="lineNum">     154</span>              :   String toString() {</span>
<span id="L155"><span class="lineNum">     155</span> <span class="tlaUNC">           0 :     return 'BiometricAuthResult(success: $success, failure: $failure)';</span></span>
<span id="L156"><span class="lineNum">     156</span>              :   }</span>
<span id="L157"><span class="lineNum">     157</span>              : }</span>
<span id="L158"><span class="lineNum">     158</span>              : </span>
<span id="L159"><span class="lineNum">     159</span>              : /// Result of a biometric settings operation</span>
<span id="L160"><span class="lineNum">     160</span>              : class BiometricSettingsResult {</span>
<span id="L161"><span class="lineNum">     161</span>              :   /// Whether the operation was successful</span>
<span id="L162"><span class="lineNum">     162</span>              :   final bool success;</span>
<span id="L163"><span class="lineNum">     163</span>              : </span>
<span id="L164"><span class="lineNum">     164</span>              :   /// Settings failure (if operation failed)</span>
<span id="L165"><span class="lineNum">     165</span>              :   final AuthFailure? failure;</span>
<span id="L166"><span class="lineNum">     166</span>              : </span>
<span id="L167"><span class="lineNum">     167</span> <span class="tlaGNC">           1 :   const BiometricSettingsResult._({</span></span>
<span id="L168"><span class="lineNum">     168</span>              :     required this.success,</span>
<span id="L169"><span class="lineNum">     169</span>              :     this.failure,</span>
<span id="L170"><span class="lineNum">     170</span>              :   });</span>
<span id="L171"><span class="lineNum">     171</span>              : </span>
<span id="L172"><span class="lineNum">     172</span>              :   /// Creates a successful biometric settings result</span>
<span id="L173"><span class="lineNum">     173</span> <span class="tlaUNC">           0 :   factory BiometricSettingsResult.success() {</span></span>
<span id="L174"><span class="lineNum">     174</span>              :     return const BiometricSettingsResult._(success: true);</span>
<span id="L175"><span class="lineNum">     175</span>              :   }</span>
<span id="L176"><span class="lineNum">     176</span>              : </span>
<span id="L177"><span class="lineNum">     177</span>              :   /// Creates a failed biometric settings result</span>
<span id="L178"><span class="lineNum">     178</span> <span class="tlaUNC">           0 :   factory BiometricSettingsResult.failure(AuthFailure failure) {</span></span>
<span id="L179"><span class="lineNum">     179</span> <span class="tlaUNC">           0 :     return BiometricSettingsResult._(</span></span>
<span id="L180"><span class="lineNum">     180</span>              :       success: false,</span>
<span id="L181"><span class="lineNum">     181</span>              :       failure: failure,</span>
<span id="L182"><span class="lineNum">     182</span>              :     );</span>
<span id="L183"><span class="lineNum">     183</span>              :   }</span>
<span id="L184"><span class="lineNum">     184</span>              : </span>
<span id="L185"><span class="lineNum">     185</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L186"><span class="lineNum">     186</span>              :   String toString() {</span>
<span id="L187"><span class="lineNum">     187</span> <span class="tlaUNC">           0 :     return 'BiometricSettingsResult(success: $success, failure: $failure)';</span></span>
<span id="L188"><span class="lineNum">     188</span>              :   }</span>
<span id="L189"><span class="lineNum">     189</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
