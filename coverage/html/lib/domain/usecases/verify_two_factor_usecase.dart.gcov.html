<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/domain/usecases/verify_two_factor_usecase.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/domain/usecases">lib/domain/usecases</a> - verify_two_factor_usecase.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">33</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-07-03 12:38:23</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import '../entities/auth_result.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import '../repositories/auth_repository.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : </span>
<span id="L4"><span class="lineNum">       4</span>              : /// Use case for two-factor authentication verification</span>
<span id="L5"><span class="lineNum">       5</span>              : /// </span>
<span id="L6"><span class="lineNum">       6</span>              : /// This use case encapsulates the business logic for verifying two-factor</span>
<span id="L7"><span class="lineNum">       7</span>              : /// authentication codes. It follows the single responsibility principle by</span>
<span id="L8"><span class="lineNum">       8</span>              : /// handling only 2FA verification-related business rules and delegating</span>
<span id="L9"><span class="lineNum">       9</span>              : /// data operations to the repository.</span>
<span id="L10"><span class="lineNum">      10</span>              : /// </span>
<span id="L11"><span class="lineNum">      11</span>              : /// The use case validates input parameters and coordinates with the repository</span>
<span id="L12"><span class="lineNum">      12</span>              : /// to complete the two-factor authentication process.</span>
<span id="L13"><span class="lineNum">      13</span>              : class VerifyTwoFactorUseCase {</span>
<span id="L14"><span class="lineNum">      14</span>              :   final AuthRepository _authRepository;</span>
<span id="L15"><span class="lineNum">      15</span>              : </span>
<span id="L16"><span class="lineNum">      16</span> <span class="tlaUNC">           0 :   const VerifyTwoFactorUseCase(this._authRepository);</span></span>
<span id="L17"><span class="lineNum">      17</span>              : </span>
<span id="L18"><span class="lineNum">      18</span>              :   /// Executes the two-factor verification use case</span>
<span id="L19"><span class="lineNum">      19</span>              :   /// </span>
<span id="L20"><span class="lineNum">      20</span>              :   /// This method performs the following operations:</span>
<span id="L21"><span class="lineNum">      21</span>              :   /// 1. Validates the input parameters</span>
<span id="L22"><span class="lineNum">      22</span>              :   /// 2. Delegates to the repository for 2FA verification</span>
<span id="L23"><span class="lineNum">      23</span>              :   /// 3. Returns the authentication result</span>
<span id="L24"><span class="lineNum">      24</span>              :   /// </span>
<span id="L25"><span class="lineNum">      25</span>              :   /// Parameters:</span>
<span id="L26"><span class="lineNum">      26</span>              :   /// - [refCode]: Reference code received from the initial login attempt</span>
<span id="L27"><span class="lineNum">      27</span>              :   /// - [code]: Two-factor authentication code provided by the user</span>
<span id="L28"><span class="lineNum">      28</span>              :   /// </span>
<span id="L29"><span class="lineNum">      29</span>              :   /// Returns [AuthResult] containing:</span>
<span id="L30"><span class="lineNum">      30</span>              :   /// - Success with user data and tokens if 2FA verification succeeds</span>
<span id="L31"><span class="lineNum">      31</span>              :   /// - Failure result with error message if 2FA verification fails or validation fails</span>
<span id="L32"><span class="lineNum">      32</span>              :   /// </span>
<span id="L33"><span class="lineNum">      33</span>              :   /// Business Rules:</span>
<span id="L34"><span class="lineNum">      34</span>              :   /// - Reference code must not be empty</span>
<span id="L35"><span class="lineNum">      35</span>              :   /// - 2FA code must be exactly 6 digits (numeric)</span>
<span id="L36"><span class="lineNum">      36</span>              :   /// - 2FA code must not contain spaces or special characters</span>
<span id="L37"><span class="lineNum">      37</span>              :   /// - Both parameters are required and cannot be null</span>
<span id="L38"><span class="lineNum">      38</span>              :   /// </span>
<span id="L39"><span class="lineNum">      39</span>              :   /// Throws:</span>
<span id="L40"><span class="lineNum">      40</span>              :   /// - NetworkException for connectivity issues</span>
<span id="L41"><span class="lineNum">      41</span>              :   /// - ServerException for server-side errors</span>
<span id="L42"><span class="lineNum">      42</span>              :   /// - AuthException for authentication-specific errors</span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaUNC">           0 :   Future&lt;AuthResult&gt; call(String refCode, String code) async {</span></span>
<span id="L44"><span class="lineNum">      44</span>              :     // Validate input parameters</span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC">           0 :     final validationResult = _validateTwoFactorParameters(refCode, code);</span></span>
<span id="L46"><span class="lineNum">      46</span>              :     if (validationResult != null) {</span>
<span id="L47"><span class="lineNum">      47</span>              :       return validationResult;</span>
<span id="L48"><span class="lineNum">      48</span>              :     }</span>
<span id="L49"><span class="lineNum">      49</span>              : </span>
<span id="L50"><span class="lineNum">      50</span>              :     // Trim whitespace from parameters</span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaUNC">           0 :     final trimmedRefCode = refCode.trim();</span></span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaUNC">           0 :     final trimmedCode = code.trim();</span></span>
<span id="L53"><span class="lineNum">      53</span>              : </span>
<span id="L54"><span class="lineNum">      54</span>              :     // Delegate to repository for 2FA verification</span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaUNC">           0 :     return await _authRepository.verifyTwoFactor(trimmedRefCode, trimmedCode);</span></span>
<span id="L56"><span class="lineNum">      56</span>              :   }</span>
<span id="L57"><span class="lineNum">      57</span>              : </span>
<span id="L58"><span class="lineNum">      58</span>              :   /// Validates two-factor authentication parameters according to business rules</span>
<span id="L59"><span class="lineNum">      59</span>              :   /// </span>
<span id="L60"><span class="lineNum">      60</span>              :   /// Returns null if validation passes, or an AuthResult.failure if validation fails</span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaUNC">           0 :   AuthResult? _validateTwoFactorParameters(String refCode, String code) {</span></span>
<span id="L62"><span class="lineNum">      62</span>              :     // Check if reference code is provided</span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaUNC">           0 :     if (refCode.trim().isEmpty) {</span></span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :       return AuthResult.failure(</span></span>
<span id="L65"><span class="lineNum">      65</span>              :         error: 'Reference code is required for two-factor verification',</span>
<span id="L66"><span class="lineNum">      66</span>              :       );</span>
<span id="L67"><span class="lineNum">      67</span>              :     }</span>
<span id="L68"><span class="lineNum">      68</span>              : </span>
<span id="L69"><span class="lineNum">      69</span>              :     // Check if 2FA code is provided</span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :     if (code.trim().isEmpty) {</span></span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :       return AuthResult.failure(</span></span>
<span id="L72"><span class="lineNum">      72</span>              :         error: 'Two-factor authentication code is required',</span>
<span id="L73"><span class="lineNum">      73</span>              :       );</span>
<span id="L74"><span class="lineNum">      74</span>              :     }</span>
<span id="L75"><span class="lineNum">      75</span>              : </span>
<span id="L76"><span class="lineNum">      76</span>              :     // Validate 2FA code format (should be 6 digits)</span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :     final codeRegex = RegExp(r'^\d{6}$');</span></span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :     if (!codeRegex.hasMatch(code.trim())) {</span></span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaUNC">           0 :       return AuthResult.failure(</span></span>
<span id="L80"><span class="lineNum">      80</span>              :         error: 'Two-factor authentication code must be exactly 6 digits',</span>
<span id="L81"><span class="lineNum">      81</span>              :       );</span>
<span id="L82"><span class="lineNum">      82</span>              :     }</span>
<span id="L83"><span class="lineNum">      83</span>              : </span>
<span id="L84"><span class="lineNum">      84</span>              :     // Validate reference code format (should not be empty and have reasonable length)</span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaUNC">           0 :     final trimmedRefCode = refCode.trim();</span></span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaUNC">           0 :     if (trimmedRefCode.length &lt; 10 || trimmedRefCode.length &gt; 100) {</span></span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaUNC">           0 :       return AuthResult.failure(</span></span>
<span id="L88"><span class="lineNum">      88</span>              :         error: 'Invalid reference code format',</span>
<span id="L89"><span class="lineNum">      89</span>              :       );</span>
<span id="L90"><span class="lineNum">      90</span>              :     }</span>
<span id="L91"><span class="lineNum">      91</span>              : </span>
<span id="L92"><span class="lineNum">      92</span>              :     return null; // Validation passed</span>
<span id="L93"><span class="lineNum">      93</span>              :   }</span>
<span id="L94"><span class="lineNum">      94</span>              : }</span>
<span id="L95"><span class="lineNum">      95</span>              : </span>
<span id="L96"><span class="lineNum">      96</span>              : /// Use case for refreshing authentication tokens</span>
<span id="L97"><span class="lineNum">      97</span>              : /// </span>
<span id="L98"><span class="lineNum">      98</span>              : /// This use case encapsulates the business logic for refreshing expired</span>
<span id="L99"><span class="lineNum">      99</span>              : /// or expiring authentication tokens. It provides a clean interface for</span>
<span id="L100"><span class="lineNum">     100</span>              : /// token management throughout the application.</span>
<span id="L101"><span class="lineNum">     101</span>              : class RefreshTokenUseCase {</span>
<span id="L102"><span class="lineNum">     102</span>              :   final AuthRepository _authRepository;</span>
<span id="L103"><span class="lineNum">     103</span>              : </span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaUNC">           0 :   const RefreshTokenUseCase(this._authRepository);</span></span>
<span id="L105"><span class="lineNum">     105</span>              : </span>
<span id="L106"><span class="lineNum">     106</span>              :   /// Executes the token refresh use case</span>
<span id="L107"><span class="lineNum">     107</span>              :   /// </span>
<span id="L108"><span class="lineNum">     108</span>              :   /// This method performs the following operations:</span>
<span id="L109"><span class="lineNum">     109</span>              :   /// 1. Attempts to refresh the current authentication token</span>
<span id="L110"><span class="lineNum">     110</span>              :   /// 2. Returns the new access token if successful</span>
<span id="L111"><span class="lineNum">     111</span>              :   /// 3. Handles token refresh failures appropriately</span>
<span id="L112"><span class="lineNum">     112</span>              :   /// </span>
<span id="L113"><span class="lineNum">     113</span>              :   /// Returns the new access token as a String if refresh succeeds.</span>
<span id="L114"><span class="lineNum">     114</span>              :   /// </span>
<span id="L115"><span class="lineNum">     115</span>              :   /// Business Rules:</span>
<span id="L116"><span class="lineNum">     116</span>              :   /// - Refresh token must be valid and not expired</span>
<span id="L117"><span class="lineNum">     117</span>              :   /// - New access token is automatically stored securely</span>
<span id="L118"><span class="lineNum">     118</span>              :   /// - Refresh operation should be atomic (all or nothing)</span>
<span id="L119"><span class="lineNum">     119</span>              :   /// </span>
<span id="L120"><span class="lineNum">     120</span>              :   /// Throws:</span>
<span id="L121"><span class="lineNum">     121</span>              :   /// - TokenExpiredException if the refresh token has expired</span>
<span id="L122"><span class="lineNum">     122</span>              :   /// - NetworkException for connectivity issues</span>
<span id="L123"><span class="lineNum">     123</span>              :   /// - ServerException for server-side errors</span>
<span id="L124"><span class="lineNum">     124</span>              :   /// - AuthException if token refresh fails</span>
<span id="L125"><span class="lineNum">     125</span>              :   /// </span>
<span id="L126"><span class="lineNum">     126</span>              :   /// Note: If token refresh fails due to expired refresh token,</span>
<span id="L127"><span class="lineNum">     127</span>              :   /// the user should be redirected to login again.</span>
<span id="L128"><span class="lineNum">     128</span> <span class="tlaUNC">           0 :   Future&lt;String&gt; call() async {</span></span>
<span id="L129"><span class="lineNum">     129</span> <span class="tlaUNC">           0 :     return await _authRepository.refreshToken();</span></span>
<span id="L130"><span class="lineNum">     130</span>              :   }</span>
<span id="L131"><span class="lineNum">     131</span>              : }</span>
<span id="L132"><span class="lineNum">     132</span>              : </span>
<span id="L133"><span class="lineNum">     133</span>              : /// Use case for validating two-factor authentication setup</span>
<span id="L134"><span class="lineNum">     134</span>              : /// </span>
<span id="L135"><span class="lineNum">     135</span>              : /// This use case provides utility methods for validating 2FA-related</span>
<span id="L136"><span class="lineNum">     136</span>              : /// operations and checking 2FA requirements.</span>
<span id="L137"><span class="lineNum">     137</span>              : class TwoFactorValidationUseCase {</span>
<span id="L138"><span class="lineNum">     138</span>              :   /// Validates if a two-factor authentication code has the correct format</span>
<span id="L139"><span class="lineNum">     139</span>              :   /// </span>
<span id="L140"><span class="lineNum">     140</span>              :   /// This method checks if the provided code meets the format requirements</span>
<span id="L141"><span class="lineNum">     141</span>              :   /// for two-factor authentication codes without making any network calls.</span>
<span id="L142"><span class="lineNum">     142</span>              :   /// </span>
<span id="L143"><span class="lineNum">     143</span>              :   /// Parameters:</span>
<span id="L144"><span class="lineNum">     144</span>              :   /// - [code]: The 2FA code to validate</span>
<span id="L145"><span class="lineNum">     145</span>              :   /// </span>
<span id="L146"><span class="lineNum">     146</span>              :   /// Returns true if the code format is valid, false otherwise.</span>
<span id="L147"><span class="lineNum">     147</span>              :   /// </span>
<span id="L148"><span class="lineNum">     148</span>              :   /// Business Rules:</span>
<span id="L149"><span class="lineNum">     149</span>              :   /// - Code must be exactly 6 digits</span>
<span id="L150"><span class="lineNum">     150</span>              :   /// - Code must contain only numeric characters</span>
<span id="L151"><span class="lineNum">     151</span>              :   /// - Code cannot be empty or contain whitespace</span>
<span id="L152"><span class="lineNum">     152</span> <span class="tlaUNC">           0 :   static bool isValidTwoFactorCode(String code) {</span></span>
<span id="L153"><span class="lineNum">     153</span> <span class="tlaUNC">           0 :     if (code.trim().isEmpty) {</span></span>
<span id="L154"><span class="lineNum">     154</span>              :       return false;</span>
<span id="L155"><span class="lineNum">     155</span>              :     }</span>
<span id="L156"><span class="lineNum">     156</span>              : </span>
<span id="L157"><span class="lineNum">     157</span> <span class="tlaUNC">           0 :     final codeRegex = RegExp(r'^\d{6}$');</span></span>
<span id="L158"><span class="lineNum">     158</span> <span class="tlaUNC">           0 :     return codeRegex.hasMatch(code.trim());</span></span>
<span id="L159"><span class="lineNum">     159</span>              :   }</span>
<span id="L160"><span class="lineNum">     160</span>              : </span>
<span id="L161"><span class="lineNum">     161</span>              :   /// Validates if a reference code has the correct format</span>
<span id="L162"><span class="lineNum">     162</span>              :   /// </span>
<span id="L163"><span class="lineNum">     163</span>              :   /// This method checks if the provided reference code meets the format</span>
<span id="L164"><span class="lineNum">     164</span>              :   /// requirements without making any network calls.</span>
<span id="L165"><span class="lineNum">     165</span>              :   /// </span>
<span id="L166"><span class="lineNum">     166</span>              :   /// Parameters:</span>
<span id="L167"><span class="lineNum">     167</span>              :   /// - [refCode]: The reference code to validate</span>
<span id="L168"><span class="lineNum">     168</span>              :   /// </span>
<span id="L169"><span class="lineNum">     169</span>              :   /// Returns true if the reference code format is valid, false otherwise.</span>
<span id="L170"><span class="lineNum">     170</span>              :   /// </span>
<span id="L171"><span class="lineNum">     171</span>              :   /// Business Rules:</span>
<span id="L172"><span class="lineNum">     172</span>              :   /// - Reference code must not be empty</span>
<span id="L173"><span class="lineNum">     173</span>              :   /// - Reference code must have reasonable length (10-100 characters)</span>
<span id="L174"><span class="lineNum">     174</span>              :   /// - Reference code cannot contain only whitespace</span>
<span id="L175"><span class="lineNum">     175</span> <span class="tlaUNC">           0 :   static bool isValidReferenceCode(String refCode) {</span></span>
<span id="L176"><span class="lineNum">     176</span> <span class="tlaUNC">           0 :     final trimmedRefCode = refCode.trim();</span></span>
<span id="L177"><span class="lineNum">     177</span>              :     </span>
<span id="L178"><span class="lineNum">     178</span> <span class="tlaUNC">           0 :     if (trimmedRefCode.isEmpty) {</span></span>
<span id="L179"><span class="lineNum">     179</span>              :       return false;</span>
<span id="L180"><span class="lineNum">     180</span>              :     }</span>
<span id="L181"><span class="lineNum">     181</span>              : </span>
<span id="L182"><span class="lineNum">     182</span> <span class="tlaUNC">           0 :     return trimmedRefCode.length &gt;= 10 &amp;&amp; trimmedRefCode.length &lt;= 100;</span></span>
<span id="L183"><span class="lineNum">     183</span>              :   }</span>
<span id="L184"><span class="lineNum">     184</span>              : </span>
<span id="L185"><span class="lineNum">     185</span>              :   /// Generates a user-friendly error message for invalid 2FA codes</span>
<span id="L186"><span class="lineNum">     186</span>              :   /// </span>
<span id="L187"><span class="lineNum">     187</span>              :   /// This method provides consistent error messaging for 2FA validation failures.</span>
<span id="L188"><span class="lineNum">     188</span>              :   /// </span>
<span id="L189"><span class="lineNum">     189</span>              :   /// Parameters:</span>
<span id="L190"><span class="lineNum">     190</span>              :   /// - [code]: The invalid 2FA code</span>
<span id="L191"><span class="lineNum">     191</span>              :   /// </span>
<span id="L192"><span class="lineNum">     192</span>              :   /// Returns a user-friendly error message explaining what's wrong with the code.</span>
<span id="L193"><span class="lineNum">     193</span> <span class="tlaUNC">           0 :   static String getTwoFactorCodeErrorMessage(String code) {</span></span>
<span id="L194"><span class="lineNum">     194</span> <span class="tlaUNC">           0 :     if (code.trim().isEmpty) {</span></span>
<span id="L195"><span class="lineNum">     195</span>              :       return 'Two-factor authentication code is required';</span>
<span id="L196"><span class="lineNum">     196</span>              :     }</span>
<span id="L197"><span class="lineNum">     197</span>              : </span>
<span id="L198"><span class="lineNum">     198</span> <span class="tlaUNC">           0 :     if (code.trim().length != 6) {</span></span>
<span id="L199"><span class="lineNum">     199</span>              :       return 'Two-factor authentication code must be exactly 6 digits';</span>
<span id="L200"><span class="lineNum">     200</span>              :     }</span>
<span id="L201"><span class="lineNum">     201</span>              : </span>
<span id="L202"><span class="lineNum">     202</span> <span class="tlaUNC">           0 :     final codeRegex = RegExp(r'^\d+$');</span></span>
<span id="L203"><span class="lineNum">     203</span> <span class="tlaUNC">           0 :     if (!codeRegex.hasMatch(code.trim())) {</span></span>
<span id="L204"><span class="lineNum">     204</span>              :       return 'Two-factor authentication code must contain only numbers';</span>
<span id="L205"><span class="lineNum">     205</span>              :     }</span>
<span id="L206"><span class="lineNum">     206</span>              : </span>
<span id="L207"><span class="lineNum">     207</span>              :     return 'Invalid two-factor authentication code format';</span>
<span id="L208"><span class="lineNum">     208</span>              :   }</span>
<span id="L209"><span class="lineNum">     209</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
