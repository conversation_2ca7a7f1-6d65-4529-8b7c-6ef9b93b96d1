<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/domain/usecases/login_usecase.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/domain/usecases">lib/domain/usecases</a> - login_usecase.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">27</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-07-03 12:38:23</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import '../entities/auth_result.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import '../repositories/auth_repository.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import '../../core/error/auth_failures.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : /// Use case for user authentication (login)</span>
<span id="L6"><span class="lineNum">       6</span>              : ///</span>
<span id="L7"><span class="lineNum">       7</span>              : /// This use case encapsulates the business logic for user login operations.</span>
<span id="L8"><span class="lineNum">       8</span>              : /// It follows the single responsibility principle by handling only login-related</span>
<span id="L9"><span class="lineNum">       9</span>              : /// business rules and delegating data operations to the repository.</span>
<span id="L10"><span class="lineNum">      10</span>              : ///</span>
<span id="L11"><span class="lineNum">      11</span>              : /// The use case validates input parameters and coordinates with the repository</span>
<span id="L12"><span class="lineNum">      12</span>              : /// to perform the actual authentication operation.</span>
<span id="L13"><span class="lineNum">      13</span>              : class LoginUseCase {</span>
<span id="L14"><span class="lineNum">      14</span>              :   final AuthRepository _authRepository;</span>
<span id="L15"><span class="lineNum">      15</span>              : </span>
<span id="L16"><span class="lineNum">      16</span> <span class="tlaUNC">           0 :   const LoginUseCase(this._authRepository);</span></span>
<span id="L17"><span class="lineNum">      17</span>              : </span>
<span id="L18"><span class="lineNum">      18</span>              :   /// Executes the login use case</span>
<span id="L19"><span class="lineNum">      19</span>              :   ///</span>
<span id="L20"><span class="lineNum">      20</span>              :   /// This method performs the following operations:</span>
<span id="L21"><span class="lineNum">      21</span>              :   /// 1. Validates the input parameters</span>
<span id="L22"><span class="lineNum">      22</span>              :   /// 2. Delegates to the repository for authentication</span>
<span id="L23"><span class="lineNum">      23</span>              :   /// 3. Returns the authentication result</span>
<span id="L24"><span class="lineNum">      24</span>              :   ///</span>
<span id="L25"><span class="lineNum">      25</span>              :   /// Parameters:</span>
<span id="L26"><span class="lineNum">      26</span>              :   /// - [email]: User's email address (must be valid email format)</span>
<span id="L27"><span class="lineNum">      27</span>              :   /// - [password]: User's password (must not be empty)</span>
<span id="L28"><span class="lineNum">      28</span>              :   ///</span>
<span id="L29"><span class="lineNum">      29</span>              :   /// Returns [AuthResult] containing:</span>
<span id="L30"><span class="lineNum">      30</span>              :   /// - Success with user data and tokens if authentication succeeds</span>
<span id="L31"><span class="lineNum">      31</span>              :   /// - Two-factor required result if 2FA is needed</span>
<span id="L32"><span class="lineNum">      32</span>              :   /// - Failure result with error message if authentication fails or validation fails</span>
<span id="L33"><span class="lineNum">      33</span>              :   ///</span>
<span id="L34"><span class="lineNum">      34</span>              :   /// Business Rules:</span>
<span id="L35"><span class="lineNum">      35</span>              :   /// - Email must be in valid email format</span>
<span id="L36"><span class="lineNum">      36</span>              :   /// - Password must not be empty or whitespace only</span>
<span id="L37"><span class="lineNum">      37</span>              :   /// - Email is case-insensitive (converted to lowercase)</span>
<span id="L38"><span class="lineNum">      38</span>              :   ///</span>
<span id="L39"><span class="lineNum">      39</span>              :   /// Throws:</span>
<span id="L40"><span class="lineNum">      40</span>              :   /// - NetworkException for connectivity issues</span>
<span id="L41"><span class="lineNum">      41</span>              :   /// - ServerException for server-side errors</span>
<span id="L42"><span class="lineNum">      42</span>              :   /// - AuthException for authentication-specific errors</span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaUNC">           0 :   Future&lt;AuthResult&gt; call(String email, String password) async {</span></span>
<span id="L44"><span class="lineNum">      44</span>              :     // Validate input parameters</span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC">           0 :     final validationResult = _validateLoginParameters(email, password);</span></span>
<span id="L46"><span class="lineNum">      46</span>              :     if (validationResult != null) {</span>
<span id="L47"><span class="lineNum">      47</span>              :       return validationResult;</span>
<span id="L48"><span class="lineNum">      48</span>              :     }</span>
<span id="L49"><span class="lineNum">      49</span>              : </span>
<span id="L50"><span class="lineNum">      50</span>              :     // Normalize email to lowercase for consistency</span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaUNC">           0 :     final normalizedEmail = email.trim().toLowerCase();</span></span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaUNC">           0 :     final trimmedPassword = password.trim();</span></span>
<span id="L53"><span class="lineNum">      53</span>              : </span>
<span id="L54"><span class="lineNum">      54</span>              :     // Delegate to repository for authentication</span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaUNC">           0 :     return await _authRepository.login(normalizedEmail, trimmedPassword);</span></span>
<span id="L56"><span class="lineNum">      56</span>              :   }</span>
<span id="L57"><span class="lineNum">      57</span>              : </span>
<span id="L58"><span class="lineNum">      58</span>              :   /// Validates login parameters according to business rules</span>
<span id="L59"><span class="lineNum">      59</span>              :   ///</span>
<span id="L60"><span class="lineNum">      60</span>              :   /// Returns null if validation passes, or an AuthResult.failure if validation fails</span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaUNC">           0 :   AuthResult? _validateLoginParameters(String email, String password) {</span></span>
<span id="L62"><span class="lineNum">      62</span>              :     // Check if email is provided</span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaUNC">           0 :     if (email.trim().isEmpty) {</span></span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :       return AuthResult.failure(</span></span>
<span id="L65"><span class="lineNum">      65</span>              :         failure: const ValidationFailure(</span>
<span id="L66"><span class="lineNum">      66</span>              :           field: 'email',</span>
<span id="L67"><span class="lineNum">      67</span>              :           message: 'Email address is required',</span>
<span id="L68"><span class="lineNum">      68</span>              :         ),</span>
<span id="L69"><span class="lineNum">      69</span>              :       );</span>
<span id="L70"><span class="lineNum">      70</span>              :     }</span>
<span id="L71"><span class="lineNum">      71</span>              : </span>
<span id="L72"><span class="lineNum">      72</span>              :     // Check if password is provided</span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :     if (password.trim().isEmpty) {</span></span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaUNC">           0 :       return AuthResult.failure(error: 'Password is required');</span></span>
<span id="L75"><span class="lineNum">      75</span>              :     }</span>
<span id="L76"><span class="lineNum">      76</span>              : </span>
<span id="L77"><span class="lineNum">      77</span>              :     // Validate email format using a simple regex</span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :     final emailRegex = RegExp(</span></span>
<span id="L79"><span class="lineNum">      79</span>              :       r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',</span>
<span id="L80"><span class="lineNum">      80</span>              :     );</span>
<span id="L81"><span class="lineNum">      81</span>              : </span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaUNC">           0 :     if (!emailRegex.hasMatch(email.trim())) {</span></span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaUNC">           0 :       return AuthResult.failure(error: 'Please enter a valid email address');</span></span>
<span id="L84"><span class="lineNum">      84</span>              :     }</span>
<span id="L85"><span class="lineNum">      85</span>              : </span>
<span id="L86"><span class="lineNum">      86</span>              :     // Check password minimum length (business rule)</span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaUNC">           0 :     if (password.trim().length &lt; 6) {</span></span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaUNC">           0 :       return AuthResult.failure(</span></span>
<span id="L89"><span class="lineNum">      89</span>              :           error: 'Password must be at least 6 characters long');</span>
<span id="L90"><span class="lineNum">      90</span>              :     }</span>
<span id="L91"><span class="lineNum">      91</span>              : </span>
<span id="L92"><span class="lineNum">      92</span>              :     return null; // Validation passed</span>
<span id="L93"><span class="lineNum">      93</span>              :   }</span>
<span id="L94"><span class="lineNum">      94</span>              : }</span>
<span id="L95"><span class="lineNum">      95</span>              : </span>
<span id="L96"><span class="lineNum">      96</span>              : /// Use case for biometric authentication</span>
<span id="L97"><span class="lineNum">      97</span>              : ///</span>
<span id="L98"><span class="lineNum">      98</span>              : /// This use case encapsulates the business logic for biometric login operations.</span>
<span id="L99"><span class="lineNum">      99</span>              : /// It handles the specific requirements and validations for biometric authentication.</span>
<span id="L100"><span class="lineNum">     100</span>              : class BiometricLoginUseCase {</span>
<span id="L101"><span class="lineNum">     101</span>              :   final AuthRepository _authRepository;</span>
<span id="L102"><span class="lineNum">     102</span>              : </span>
<span id="L103"><span class="lineNum">     103</span> <span class="tlaUNC">           0 :   const BiometricLoginUseCase(this._authRepository);</span></span>
<span id="L104"><span class="lineNum">     104</span>              : </span>
<span id="L105"><span class="lineNum">     105</span>              :   /// Executes the biometric login use case</span>
<span id="L106"><span class="lineNum">     106</span>              :   ///</span>
<span id="L107"><span class="lineNum">     107</span>              :   /// This method performs the following operations:</span>
<span id="L108"><span class="lineNum">     108</span>              :   /// 1. Validates the email parameter</span>
<span id="L109"><span class="lineNum">     109</span>              :   /// 2. Delegates to the repository for biometric authentication</span>
<span id="L110"><span class="lineNum">     110</span>              :   /// 3. Returns the authentication result</span>
<span id="L111"><span class="lineNum">     111</span>              :   ///</span>
<span id="L112"><span class="lineNum">     112</span>              :   /// Parameters:</span>
<span id="L113"><span class="lineNum">     113</span>              :   /// - [email]: User's email address (must be valid email format)</span>
<span id="L114"><span class="lineNum">     114</span>              :   ///</span>
<span id="L115"><span class="lineNum">     115</span>              :   /// Returns [AuthResult] containing:</span>
<span id="L116"><span class="lineNum">     116</span>              :   /// - Success with user data and tokens if biometric authentication succeeds</span>
<span id="L117"><span class="lineNum">     117</span>              :   /// - Failure result with error message if biometric authentication fails</span>
<span id="L118"><span class="lineNum">     118</span>              :   ///</span>
<span id="L119"><span class="lineNum">     119</span>              :   /// Business Rules:</span>
<span id="L120"><span class="lineNum">     120</span>              :   /// - Email must be in valid email format</span>
<span id="L121"><span class="lineNum">     121</span>              :   /// - User must have previously set up biometric authentication</span>
<span id="L122"><span class="lineNum">     122</span>              :   /// - Email is case-insensitive (converted to lowercase)</span>
<span id="L123"><span class="lineNum">     123</span>              :   ///</span>
<span id="L124"><span class="lineNum">     124</span>              :   /// Throws:</span>
<span id="L125"><span class="lineNum">     125</span>              :   /// - BiometricException for biometric-specific errors</span>
<span id="L126"><span class="lineNum">     126</span>              :   /// - NetworkException for connectivity issues</span>
<span id="L127"><span class="lineNum">     127</span>              :   /// - AuthException for authentication-specific errors</span>
<span id="L128"><span class="lineNum">     128</span> <span class="tlaUNC">           0 :   Future&lt;AuthResult&gt; call(String email) async {</span></span>
<span id="L129"><span class="lineNum">     129</span>              :     // Validate email parameter</span>
<span id="L130"><span class="lineNum">     130</span> <span class="tlaUNC">           0 :     final validationResult = _validateEmail(email);</span></span>
<span id="L131"><span class="lineNum">     131</span>              :     if (validationResult != null) {</span>
<span id="L132"><span class="lineNum">     132</span>              :       return validationResult;</span>
<span id="L133"><span class="lineNum">     133</span>              :     }</span>
<span id="L134"><span class="lineNum">     134</span>              : </span>
<span id="L135"><span class="lineNum">     135</span>              :     // Normalize email to lowercase for consistency</span>
<span id="L136"><span class="lineNum">     136</span> <span class="tlaUNC">           0 :     final normalizedEmail = email.trim().toLowerCase();</span></span>
<span id="L137"><span class="lineNum">     137</span>              : </span>
<span id="L138"><span class="lineNum">     138</span>              :     // Delegate to repository for biometric authentication</span>
<span id="L139"><span class="lineNum">     139</span> <span class="tlaUNC">           0 :     return await _authRepository.loginWithBiometric(normalizedEmail);</span></span>
<span id="L140"><span class="lineNum">     140</span>              :   }</span>
<span id="L141"><span class="lineNum">     141</span>              : </span>
<span id="L142"><span class="lineNum">     142</span>              :   /// Validates email parameter according to business rules</span>
<span id="L143"><span class="lineNum">     143</span>              :   ///</span>
<span id="L144"><span class="lineNum">     144</span>              :   /// Returns null if validation passes, or an AuthResult.failure if validation fails</span>
<span id="L145"><span class="lineNum">     145</span> <span class="tlaUNC">           0 :   AuthResult? _validateEmail(String email) {</span></span>
<span id="L146"><span class="lineNum">     146</span>              :     // Check if email is provided</span>
<span id="L147"><span class="lineNum">     147</span> <span class="tlaUNC">           0 :     if (email.trim().isEmpty) {</span></span>
<span id="L148"><span class="lineNum">     148</span> <span class="tlaUNC">           0 :       return AuthResult.failure(</span></span>
<span id="L149"><span class="lineNum">     149</span>              :           error: 'Email address is required for biometric login');</span>
<span id="L150"><span class="lineNum">     150</span>              :     }</span>
<span id="L151"><span class="lineNum">     151</span>              : </span>
<span id="L152"><span class="lineNum">     152</span>              :     // Validate email format using a simple regex</span>
<span id="L153"><span class="lineNum">     153</span> <span class="tlaUNC">           0 :     final emailRegex = RegExp(</span></span>
<span id="L154"><span class="lineNum">     154</span>              :       r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',</span>
<span id="L155"><span class="lineNum">     155</span>              :     );</span>
<span id="L156"><span class="lineNum">     156</span>              : </span>
<span id="L157"><span class="lineNum">     157</span> <span class="tlaUNC">           0 :     if (!emailRegex.hasMatch(email.trim())) {</span></span>
<span id="L158"><span class="lineNum">     158</span> <span class="tlaUNC">           0 :       return AuthResult.failure(error: 'Please enter a valid email address');</span></span>
<span id="L159"><span class="lineNum">     159</span>              :     }</span>
<span id="L160"><span class="lineNum">     160</span>              : </span>
<span id="L161"><span class="lineNum">     161</span>              :     return null; // Validation passed</span>
<span id="L162"><span class="lineNum">     162</span>              :   }</span>
<span id="L163"><span class="lineNum">     163</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
