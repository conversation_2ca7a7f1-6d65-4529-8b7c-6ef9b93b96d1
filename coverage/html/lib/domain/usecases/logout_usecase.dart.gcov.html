<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/domain/usecases/logout_usecase.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/domain/usecases">lib/domain/usecases</a> - logout_usecase.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">12</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-07-03 12:38:23</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import '../entities/auth_user.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import '../repositories/auth_repository.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : </span>
<span id="L4"><span class="lineNum">       4</span>              : /// Use case for user logout operations</span>
<span id="L5"><span class="lineNum">       5</span>              : ///</span>
<span id="L6"><span class="lineNum">       6</span>              : /// This use case encapsulates the business logic for user logout operations.</span>
<span id="L7"><span class="lineNum">       7</span>              : /// It follows the single responsibility principle by handling only logout-related</span>
<span id="L8"><span class="lineNum">       8</span>              : /// business rules and delegating data operations to the repository.</span>
<span id="L9"><span class="lineNum">       9</span>              : ///</span>
<span id="L10"><span class="lineNum">      10</span>              : /// The use case provides both regular logout (current session) and global logout</span>
<span id="L11"><span class="lineNum">      11</span>              : /// (all sessions) functionality.</span>
<span id="L12"><span class="lineNum">      12</span>              : class LogoutUseCase {</span>
<span id="L13"><span class="lineNum">      13</span>              :   final AuthRepository _authRepository;</span>
<span id="L14"><span class="lineNum">      14</span>              : </span>
<span id="L15"><span class="lineNum">      15</span> <span class="tlaUNC">           0 :   const LogoutUseCase(this._authRepository);</span></span>
<span id="L16"><span class="lineNum">      16</span>              : </span>
<span id="L17"><span class="lineNum">      17</span>              :   /// Executes the logout use case for the current session</span>
<span id="L18"><span class="lineNum">      18</span>              :   ///</span>
<span id="L19"><span class="lineNum">      19</span>              :   /// This method performs the following operations:</span>
<span id="L20"><span class="lineNum">      20</span>              :   /// 1. Logs out the user from the current session</span>
<span id="L21"><span class="lineNum">      21</span>              :   /// 2. Invalidates the current session on the server</span>
<span id="L22"><span class="lineNum">      22</span>              :   /// 3. Clears local authentication tokens and cached data</span>
<span id="L23"><span class="lineNum">      23</span>              :   ///</span>
<span id="L24"><span class="lineNum">      24</span>              :   /// This operation is designed to always succeed locally even if the server</span>
<span id="L25"><span class="lineNum">      25</span>              :   /// logout fails, ensuring the user can always log out from the app.</span>
<span id="L26"><span class="lineNum">      26</span>              :   ///</span>
<span id="L27"><span class="lineNum">      27</span>              :   /// Business Rules:</span>
<span id="L28"><span class="lineNum">      28</span>              :   /// - Local logout always succeeds regardless of network connectivity</span>
<span id="L29"><span class="lineNum">      29</span>              :   /// - Server logout is attempted but failures don't prevent local logout</span>
<span id="L30"><span class="lineNum">      30</span>              :   /// - All local authentication data is cleared</span>
<span id="L31"><span class="lineNum">      31</span>              :   ///</span>
<span id="L32"><span class="lineNum">      32</span>              :   /// Does not throw exceptions - any errors are handled internally</span>
<span id="L33"><span class="lineNum">      33</span>              :   /// to ensure logout always completes successfully.</span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; call() async {</span></span>
<span id="L35"><span class="lineNum">      35</span>              :     try {</span>
<span id="L36"><span class="lineNum">      36</span> <span class="tlaUNC">           0 :       await _authRepository.logout();</span></span>
<span id="L37"><span class="lineNum">      37</span>              :     } catch (e) {</span>
<span id="L38"><span class="lineNum">      38</span>              :       // Log the error but don't throw it - logout should always succeed locally</span>
<span id="L39"><span class="lineNum">      39</span>              :       // In a real implementation, you might want to log this error to a logging service</span>
<span id="L40"><span class="lineNum">      40</span>              :       // For now, we silently handle the error to ensure logout completes</span>
<span id="L41"><span class="lineNum">      41</span>              :     }</span>
<span id="L42"><span class="lineNum">      42</span>              :   }</span>
<span id="L43"><span class="lineNum">      43</span>              : </span>
<span id="L44"><span class="lineNum">      44</span>              :   /// Executes the global logout use case for all sessions</span>
<span id="L45"><span class="lineNum">      45</span>              :   ///</span>
<span id="L46"><span class="lineNum">      46</span>              :   /// This method performs the following operations:</span>
<span id="L47"><span class="lineNum">      47</span>              :   /// 1. Logs out the user from all active sessions globally</span>
<span id="L48"><span class="lineNum">      48</span>              :   /// 2. Invalidates all user sessions on the server</span>
<span id="L49"><span class="lineNum">      49</span>              :   /// 3. Clears local authentication tokens and cached data</span>
<span id="L50"><span class="lineNum">      50</span>              :   ///</span>
<span id="L51"><span class="lineNum">      51</span>              :   /// This operation requires network connectivity to invalidate server sessions.</span>
<span id="L52"><span class="lineNum">      52</span>              :   /// If the operation fails, the user remains logged in on other devices.</span>
<span id="L53"><span class="lineNum">      53</span>              :   ///</span>
<span id="L54"><span class="lineNum">      54</span>              :   /// Business Rules:</span>
<span id="L55"><span class="lineNum">      55</span>              :   /// - Requires active network connection</span>
<span id="L56"><span class="lineNum">      56</span>              :   /// - Invalidates sessions on all devices</span>
<span id="L57"><span class="lineNum">      57</span>              :   /// - Local logout always succeeds regardless of server response</span>
<span id="L58"><span class="lineNum">      58</span>              :   ///</span>
<span id="L59"><span class="lineNum">      59</span>              :   /// Throws:</span>
<span id="L60"><span class="lineNum">      60</span>              :   /// - NetworkException for connectivity issues</span>
<span id="L61"><span class="lineNum">      61</span>              :   /// - ServerException for server-side errors</span>
<span id="L62"><span class="lineNum">      62</span>              :   ///</span>
<span id="L63"><span class="lineNum">      63</span>              :   /// Note: Even if server logout fails, local logout will still be performed</span>
<span id="L64"><span class="lineNum">      64</span>              :   /// to ensure the user is logged out from the current device.</span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; callGlobal() async {</span></span>
<span id="L66"><span class="lineNum">      66</span>              :     try {</span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :       await _authRepository.globalLogout();</span></span>
<span id="L68"><span class="lineNum">      68</span>              :     } catch (e) {</span>
<span id="L69"><span class="lineNum">      69</span>              :       // Ensure local logout happens even if global logout fails</span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :       await call();</span></span>
<span id="L71"><span class="lineNum">      71</span>              :       // Re-throw the exception to inform the caller about the global logout failure</span>
<span id="L72"><span class="lineNum">      72</span>              :       rethrow;</span>
<span id="L73"><span class="lineNum">      73</span>              :     }</span>
<span id="L74"><span class="lineNum">      74</span>              :   }</span>
<span id="L75"><span class="lineNum">      75</span>              : }</span>
<span id="L76"><span class="lineNum">      76</span>              : </span>
<span id="L77"><span class="lineNum">      77</span>              : /// Use case for checking authentication status</span>
<span id="L78"><span class="lineNum">      78</span>              : ///</span>
<span id="L79"><span class="lineNum">      79</span>              : /// This use case encapsulates the business logic for checking if a user</span>
<span id="L80"><span class="lineNum">      80</span>              : /// is currently authenticated. It provides a clean interface for other</span>
<span id="L81"><span class="lineNum">      81</span>              : /// parts of the application to verify authentication status.</span>
<span id="L82"><span class="lineNum">      82</span>              : class CheckAuthenticationUseCase {</span>
<span id="L83"><span class="lineNum">      83</span>              :   final AuthRepository _authRepository;</span>
<span id="L84"><span class="lineNum">      84</span>              : </span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaUNC">           0 :   const CheckAuthenticationUseCase(this._authRepository);</span></span>
<span id="L86"><span class="lineNum">      86</span>              : </span>
<span id="L87"><span class="lineNum">      87</span>              :   /// Checks if the user is currently authenticated</span>
<span id="L88"><span class="lineNum">      88</span>              :   ///</span>
<span id="L89"><span class="lineNum">      89</span>              :   /// This method performs the following checks:</span>
<span id="L90"><span class="lineNum">      90</span>              :   /// 1. Verifies presence of valid authentication tokens</span>
<span id="L91"><span class="lineNum">      91</span>              :   /// 2. Checks token expiration status</span>
<span id="L92"><span class="lineNum">      92</span>              :   /// 3. Validates session status</span>
<span id="L93"><span class="lineNum">      93</span>              :   ///</span>
<span id="L94"><span class="lineNum">      94</span>              :   /// Returns true if the user is authenticated and the session is valid,</span>
<span id="L95"><span class="lineNum">      95</span>              :   /// false otherwise.</span>
<span id="L96"><span class="lineNum">      96</span>              :   ///</span>
<span id="L97"><span class="lineNum">      97</span>              :   /// Business Rules:</span>
<span id="L98"><span class="lineNum">      98</span>              :   /// - Returns false for any error conditions</span>
<span id="L99"><span class="lineNum">      99</span>              :   /// - Does not throw exceptions</span>
<span id="L100"><span class="lineNum">     100</span>              :   /// - Performs comprehensive authentication validation</span>
<span id="L101"><span class="lineNum">     101</span>              :   ///</span>
<span id="L102"><span class="lineNum">     102</span>              :   /// This method is safe to call frequently as it's designed to be lightweight</span>
<span id="L103"><span class="lineNum">     103</span>              :   /// and not perform expensive operations.</span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaUNC">           0 :   Future&lt;bool&gt; call() async {</span></span>
<span id="L105"><span class="lineNum">     105</span>              :     try {</span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaUNC">           0 :       return await _authRepository.isAuthenticated();</span></span>
<span id="L107"><span class="lineNum">     107</span>              :     } catch (e) {</span>
<span id="L108"><span class="lineNum">     108</span>              :       // Return false for any error conditions</span>
<span id="L109"><span class="lineNum">     109</span>              :       return false;</span>
<span id="L110"><span class="lineNum">     110</span>              :     }</span>
<span id="L111"><span class="lineNum">     111</span>              :   }</span>
<span id="L112"><span class="lineNum">     112</span>              : }</span>
<span id="L113"><span class="lineNum">     113</span>              : </span>
<span id="L114"><span class="lineNum">     114</span>              : /// Use case for retrieving the current user</span>
<span id="L115"><span class="lineNum">     115</span>              : ///</span>
<span id="L116"><span class="lineNum">     116</span>              : /// This use case encapsulates the business logic for retrieving the currently</span>
<span id="L117"><span class="lineNum">     117</span>              : /// authenticated user's information. It provides a clean interface for accessing</span>
<span id="L118"><span class="lineNum">     118</span>              : /// user data throughout the application.</span>
<span id="L119"><span class="lineNum">     119</span>              : class GetCurrentUserUseCase {</span>
<span id="L120"><span class="lineNum">     120</span>              :   final AuthRepository _authRepository;</span>
<span id="L121"><span class="lineNum">     121</span>              : </span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaUNC">           0 :   const GetCurrentUserUseCase(this._authRepository);</span></span>
<span id="L123"><span class="lineNum">     123</span>              : </span>
<span id="L124"><span class="lineNum">     124</span>              :   /// Retrieves the currently authenticated user</span>
<span id="L125"><span class="lineNum">     125</span>              :   ///</span>
<span id="L126"><span class="lineNum">     126</span>              :   /// This method performs the following operations:</span>
<span id="L127"><span class="lineNum">     127</span>              :   /// 1. Checks if a user is currently authenticated</span>
<span id="L128"><span class="lineNum">     128</span>              :   /// 2. Retrieves the user data from secure storage or cache</span>
<span id="L129"><span class="lineNum">     129</span>              :   /// 3. Returns the user information</span>
<span id="L130"><span class="lineNum">     130</span>              :   ///</span>
<span id="L131"><span class="lineNum">     131</span>              :   /// Returns the authenticated user if available, null otherwise.</span>
<span id="L132"><span class="lineNum">     132</span>              :   ///</span>
<span id="L133"><span class="lineNum">     133</span>              :   /// Business Rules:</span>
<span id="L134"><span class="lineNum">     134</span>              :   /// - Returns null if no user is authenticated</span>
<span id="L135"><span class="lineNum">     135</span>              :   /// - Returns null for any error conditions</span>
<span id="L136"><span class="lineNum">     136</span>              :   /// - Does not throw exceptions</span>
<span id="L137"><span class="lineNum">     137</span>              :   /// - May return cached user data for performance</span>
<span id="L138"><span class="lineNum">     138</span>              :   ///</span>
<span id="L139"><span class="lineNum">     139</span>              :   /// This method is safe to call frequently and is designed to be lightweight.</span>
<span id="L140"><span class="lineNum">     140</span> <span class="tlaUNC">           0 :   Future&lt;AuthUser?&gt; call() async {</span></span>
<span id="L141"><span class="lineNum">     141</span>              :     try {</span>
<span id="L142"><span class="lineNum">     142</span> <span class="tlaUNC">           0 :       return await _authRepository.getCurrentUser();</span></span>
<span id="L143"><span class="lineNum">     143</span>              :     } catch (e) {</span>
<span id="L144"><span class="lineNum">     144</span>              :       // Return null for any error conditions</span>
<span id="L145"><span class="lineNum">     145</span>              :       return null;</span>
<span id="L146"><span class="lineNum">     146</span>              :     }</span>
<span id="L147"><span class="lineNum">     147</span>              :   }</span>
<span id="L148"><span class="lineNum">     148</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
