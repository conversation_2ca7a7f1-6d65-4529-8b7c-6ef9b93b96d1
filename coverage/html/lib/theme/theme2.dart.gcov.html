<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/theme/theme2.dart</title>
  <link rel="stylesheet" type="text/css" href="../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/theme">lib/theme</a> - theme2.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">3.7&nbsp;%</td>
            <td class="headerCovTableEntry">27</td>
            <td class="headerCovTableEntry">1</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-07-03 12:38:23</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/material.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_secure_storage/flutter_secure_storage.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : </span>
<span id="L4"><span class="lineNum">       4</span>              : enum ThemeType {</span>
<span id="L5"><span class="lineNum">       5</span>              :   Light,</span>
<span id="L6"><span class="lineNum">       6</span>              :   Dark,</span>
<span id="L7"><span class="lineNum">       7</span>              : }</span>
<span id="L8"><span class="lineNum">       8</span>              : </span>
<span id="L9"><span class="lineNum">       9</span>              : class CustomThemeData {</span>
<span id="L10"><span class="lineNum">      10</span>              :   Color bgColor;</span>
<span id="L11"><span class="lineNum">      11</span>              :   Color gridLineColor;</span>
<span id="L12"><span class="lineNum">      12</span>              :   Color onSecondaryContainer;</span>
<span id="L13"><span class="lineNum">      13</span>              :   Color primaryContainer;</span>
<span id="L14"><span class="lineNum">      14</span>              :   Color profileChamferColor;</span>
<span id="L15"><span class="lineNum">      15</span>              :   Color loginTitleColor;</span>
<span id="L16"><span class="lineNum">      16</span>              :   Color dropDownColor;</span>
<span id="L17"><span class="lineNum">      17</span>              :   Color signInColor;</span>
<span id="L18"><span class="lineNum">      18</span>              :   Color pleaseSignInColor;</span>
<span id="L19"><span class="lineNum">      19</span>              :   Color gridHeadingColor;</span>
<span id="L20"><span class="lineNum">      20</span>              :   Color textFieldFillColor;</span>
<span id="L21"><span class="lineNum">      21</span>              :   Color textfieldTextColor;</span>
<span id="L22"><span class="lineNum">      22</span>              :   Color textfieldHintColor;</span>
<span id="L23"><span class="lineNum">      23</span>              :   Color textfieldCursorColor;</span>
<span id="L24"><span class="lineNum">      24</span>              :   Color bottomNavColor;</span>
<span id="L25"><span class="lineNum">      25</span>              :   Color headingColor;</span>
<span id="L26"><span class="lineNum">      26</span>              :   Color basicAdvanceTextColor;</span>
<span id="L27"><span class="lineNum">      27</span>              :   Color drawerHeadingColor;</span>
<span id="L28"><span class="lineNum">      28</span>              :   Color tableText;</span>
<span id="L29"><span class="lineNum">      29</span>              :   Color editIconColor;</span>
<span id="L30"><span class="lineNum">      30</span>              :   Color noEntriesColor;</span>
<span id="L31"><span class="lineNum">      31</span>              :   Color numberWheelSelectedBG;</span>
<span id="L32"><span class="lineNum">      32</span>              :   Color editIconBG;</span>
<span id="L33"><span class="lineNum">      33</span>              :   Color dialogBG;</span>
<span id="L34"><span class="lineNum">      34</span>              :   Color inactiveBottomNavbarIconColor;</span>
<span id="L35"><span class="lineNum">      35</span>              :   Color toggleColor;</span>
<span id="L36"><span class="lineNum">      36</span>              :   Color popupcolor;</span>
<span id="L37"><span class="lineNum">      37</span>              :   Color profileBorderColor;</span>
<span id="L38"><span class="lineNum">      38</span>              :   Color dropdownColor;</span>
<span id="L39"><span class="lineNum">      39</span>              :   Color textFieldBGProfile;</span>
<span id="L40"><span class="lineNum">      40</span>              :   Color calibrateTabBGColor;</span>
<span id="L41"><span class="lineNum">      41</span>              :   Color iconColor;</span>
<span id="L42"><span class="lineNum">      42</span>              :   Color splashColor;</span>
<span id="L43"><span class="lineNum">      43</span>              : </span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaGNC">           1 :   CustomThemeData(</span></span>
<span id="L45"><span class="lineNum">      45</span>              :       {required this.bgColor,</span>
<span id="L46"><span class="lineNum">      46</span>              :       required this.loginTitleColor,</span>
<span id="L47"><span class="lineNum">      47</span>              :       required this.signInColor,</span>
<span id="L48"><span class="lineNum">      48</span>              :       required this.tableText,</span>
<span id="L49"><span class="lineNum">      49</span>              :       required this.gridHeadingColor,</span>
<span id="L50"><span class="lineNum">      50</span>              :       required this.popupcolor,</span>
<span id="L51"><span class="lineNum">      51</span>              :       required this.noEntriesColor,</span>
<span id="L52"><span class="lineNum">      52</span>              :       required this.splashColor,</span>
<span id="L53"><span class="lineNum">      53</span>              :       required this.dropDownColor,</span>
<span id="L54"><span class="lineNum">      54</span>              :       required this.numberWheelSelectedBG,</span>
<span id="L55"><span class="lineNum">      55</span>              :       required this.toggleColor,</span>
<span id="L56"><span class="lineNum">      56</span>              :       required this.dialogBG,</span>
<span id="L57"><span class="lineNum">      57</span>              :       required this.dropdownColor,</span>
<span id="L58"><span class="lineNum">      58</span>              :       required this.bottomNavColor,</span>
<span id="L59"><span class="lineNum">      59</span>              :       required this.pleaseSignInColor,</span>
<span id="L60"><span class="lineNum">      60</span>              :       required this.iconColor,</span>
<span id="L61"><span class="lineNum">      61</span>              :       required this.profileChamferColor,</span>
<span id="L62"><span class="lineNum">      62</span>              :       required this.textFieldBGProfile,</span>
<span id="L63"><span class="lineNum">      63</span>              :       required this.editIconColor,</span>
<span id="L64"><span class="lineNum">      64</span>              :       required this.calibrateTabBGColor,</span>
<span id="L65"><span class="lineNum">      65</span>              :       required this.editIconBG,</span>
<span id="L66"><span class="lineNum">      66</span>              :       required this.textFieldFillColor,</span>
<span id="L67"><span class="lineNum">      67</span>              :       required this.onSecondaryContainer,</span>
<span id="L68"><span class="lineNum">      68</span>              :       required this.inactiveBottomNavbarIconColor,</span>
<span id="L69"><span class="lineNum">      69</span>              :       required this.primaryContainer,</span>
<span id="L70"><span class="lineNum">      70</span>              :       required this.drawerHeadingColor,</span>
<span id="L71"><span class="lineNum">      71</span>              :       required this.gridLineColor,</span>
<span id="L72"><span class="lineNum">      72</span>              :       required this.basicAdvanceTextColor,</span>
<span id="L73"><span class="lineNum">      73</span>              :       required this.textfieldTextColor,</span>
<span id="L74"><span class="lineNum">      74</span>              :       required this.profileBorderColor,</span>
<span id="L75"><span class="lineNum">      75</span>              :       required this.textfieldHintColor,</span>
<span id="L76"><span class="lineNum">      76</span>              :       required this.headingColor,</span>
<span id="L77"><span class="lineNum">      77</span>              :       required this.textfieldCursorColor});</span>
<span id="L78"><span class="lineNum">      78</span>              : }</span>
<span id="L79"><span class="lineNum">      79</span>              : </span>
<span id="L80"><span class="lineNum">      80</span>              : class CommonColors {</span>
<span id="L81"><span class="lineNum">      81</span>              :   // const Color(0xff45b4d9),</span>
<span id="L82"><span class="lineNum">      82</span>              :   // const Color(0xff00bc8a),</span>
<span id="L83"><span class="lineNum">      83</span>              :   // const Color(0xffe3b039),</span>
<span id="L84"><span class="lineNum">      84</span>              : </span>
<span id="L85"><span class="lineNum">      85</span>              :   static const Color blue = Color(0xFF145166);</span>
<span id="L86"><span class="lineNum">      86</span>              :   static const Color red = Color(0xFFFF5353);</span>
<span id="L87"><span class="lineNum">      87</span>              :   static const Color green = Color(0xFF00bc8a);</span>
<span id="L88"><span class="lineNum">      88</span>              :   static const Color yellow = Color(0xFFDFAC46);</span>
<span id="L89"><span class="lineNum">      89</span>              :   static const Color blue2 = const Color(0xff45b4d9);</span>
<span id="L90"><span class="lineNum">      90</span>              : }</span>
<span id="L91"><span class="lineNum">      91</span>              : </span>
<span id="L92"><span class="lineNum">      92</span>              : class ThemeNotifier with ChangeNotifier {</span>
<span id="L93"><span class="lineNum">      93</span>              :   CustomThemeData _currentTheme = _lightTheme;</span>
<span id="L94"><span class="lineNum">      94</span>              :   bool isDark = false;</span>
<span id="L95"><span class="lineNum">      95</span>              :   static double extrasmall = 14;</span>
<span id="L96"><span class="lineNum">      96</span>              :   static double small = 16;</span>
<span id="L97"><span class="lineNum">      97</span>              :   static double medium = 18;</span>
<span id="L98"><span class="lineNum">      98</span>              :   static double large = 20;</span>
<span id="L99"><span class="lineNum">      99</span>              : </span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaUNC">           0 :   static final CustomThemeData _lightTheme = CustomThemeData(</span></span>
<span id="L101"><span class="lineNum">     101</span>              :       bgColor: const Color(0XFFe5ebf0),</span>
<span id="L102"><span class="lineNum">     102</span>              :       bottomNavColor: const Color.fromRGBO(250, 250, 250, 1),</span>
<span id="L103"><span class="lineNum">     103</span>              :       textFieldFillColor: Colors.white,</span>
<span id="L104"><span class="lineNum">     104</span>              :       gridLineColor: const Color(0xff8C8C8C),</span>
<span id="L105"><span class="lineNum">     105</span>              :       loginTitleColor: const Color(0xff40434F),</span>
<span id="L106"><span class="lineNum">     106</span>              :       editIconColor: const Color(0xFF515151),</span>
<span id="L107"><span class="lineNum">     107</span>              :       editIconBG: const Color(0xFFCECECE),</span>
<span id="L108"><span class="lineNum">     108</span>              :       numberWheelSelectedBG: const Color(0xFFCECECE),</span>
<span id="L109"><span class="lineNum">     109</span>              :       tableText: const Color(0xff515151),</span>
<span id="L110"><span class="lineNum">     110</span>              :       dropdownColor: const Color(0xFFF5F5F5),</span>
<span id="L111"><span class="lineNum">     111</span>              :       primaryContainer: Colors.white,</span>
<span id="L112"><span class="lineNum">     112</span>              :       noEntriesColor: const Color(0xFFB0B0B0),</span>
<span id="L113"><span class="lineNum">     113</span>              :       dropDownColor: const Color(0xFFF5F5F5),</span>
<span id="L114"><span class="lineNum">     114</span>              :       onSecondaryContainer: const Color(0xffF2F2F2),</span>
<span id="L115"><span class="lineNum">     115</span>              :       dialogBG: const Color(0xFFF0F0F0),</span>
<span id="L116"><span class="lineNum">     116</span>              :       textFieldBGProfile: const Color(0xFFF2F2F2),</span>
<span id="L117"><span class="lineNum">     117</span>              :       popupcolor: const Color(0xFF2D2D2D),</span>
<span id="L118"><span class="lineNum">     118</span>              :       signInColor: const Color(0xFF515151),</span>
<span id="L119"><span class="lineNum">     119</span>              :       profileChamferColor: const Color(0XFFD4D4D4),</span>
<span id="L120"><span class="lineNum">     120</span>              :       calibrateTabBGColor: Colors.white,</span>
<span id="L121"><span class="lineNum">     121</span>              :       headingColor: const Color(0xFF525252),</span>
<span id="L122"><span class="lineNum">     122</span>              :       iconColor: const Color(0xFF5A5A5A),</span>
<span id="L123"><span class="lineNum">     123</span>              :       gridHeadingColor: const Color(0xFF383838),</span>
<span id="L124"><span class="lineNum">     124</span>              :       drawerHeadingColor: const Color(0xFF2D2D2D),</span>
<span id="L125"><span class="lineNum">     125</span>              :       profileBorderColor: const Color(0xFF747474),</span>
<span id="L126"><span class="lineNum">     126</span>              :       basicAdvanceTextColor: const Color(0xFF2D2D2D),</span>
<span id="L127"><span class="lineNum">     127</span>              :       inactiveBottomNavbarIconColor: const Color(0xFF4F4F4F),</span>
<span id="L128"><span class="lineNum">     128</span>              :       pleaseSignInColor: const Color(0xFF515151),</span>
<span id="L129"><span class="lineNum">     129</span>              :       toggleColor: const Color(0xFFCECACA),</span>
<span id="L130"><span class="lineNum">     130</span>              :       textfieldCursorColor: const Color(0xFFA9A9A9),</span>
<span id="L131"><span class="lineNum">     131</span> <span class="tlaUNC">           0 :       splashColor: const Color(0x66c8c8c8).withOpacity(1),</span></span>
<span id="L132"><span class="lineNum">     132</span>              :       textfieldHintColor: const Color(0xFFAEAEAE),</span>
<span id="L133"><span class="lineNum">     133</span>              :       textfieldTextColor: const Color(0xFF646464));</span>
<span id="L134"><span class="lineNum">     134</span>              : </span>
<span id="L135"><span class="lineNum">     135</span> <span class="tlaUNC">           0 :   static final CustomThemeData _darkTheme = CustomThemeData(</span></span>
<span id="L136"><span class="lineNum">     136</span>              :       bottomNavColor: const Color(0xFF353535),</span>
<span id="L137"><span class="lineNum">     137</span>              :       tableText: const Color(0xFFEEE0CB),</span>
<span id="L138"><span class="lineNum">     138</span>              :       textFieldFillColor: const Color(0xff333742),</span>
<span id="L139"><span class="lineNum">     139</span>              :       bgColor: const Color(0xFF252525),</span>
<span id="L140"><span class="lineNum">     140</span>              :       headingColor: const Color(0xFFCFCFCF),</span>
<span id="L141"><span class="lineNum">     141</span>              :       dropdownColor: const Color(0xFF393939),</span>
<span id="L142"><span class="lineNum">     142</span>              :       editIconColor: const Color(0xFFEEE0CB),</span>
<span id="L143"><span class="lineNum">     143</span>              :       profileBorderColor: const Color(0xFF747474),</span>
<span id="L144"><span class="lineNum">     144</span>              :       textFieldBGProfile: const Color(0xFF373737),</span>
<span id="L145"><span class="lineNum">     145</span>              :       noEntriesColor: const Color(0XFF6B6B6B),</span>
<span id="L146"><span class="lineNum">     146</span>              :       popupcolor: const Color(0xFFB8B8B8),</span>
<span id="L147"><span class="lineNum">     147</span> <span class="tlaUNC">           0 :       splashColor: const Color(0x66c8c8c8).withOpacity(0.1),</span></span>
<span id="L148"><span class="lineNum">     148</span>              :       editIconBG: const Color(0xFF595959),</span>
<span id="L149"><span class="lineNum">     149</span>              :       gridHeadingColor: const Color(0xFFDBDBDB),</span>
<span id="L150"><span class="lineNum">     150</span>              :       toggleColor: const Color(0xFF434343),</span>
<span id="L151"><span class="lineNum">     151</span> <span class="tlaUNC">           0 :       dialogBG: Color(0xFF0A0A0A),</span></span>
<span id="L152"><span class="lineNum">     152</span>              :       loginTitleColor: const Color(0xffCCCDCD),</span>
<span id="L153"><span class="lineNum">     153</span>              :       inactiveBottomNavbarIconColor: const Color(0xFFDCDCDC),</span>
<span id="L154"><span class="lineNum">     154</span>              :       iconColor: const Color(0xFFA9A9A9),</span>
<span id="L155"><span class="lineNum">     155</span>              :       primaryContainer: const Color(0xFF2B2C2E),</span>
<span id="L156"><span class="lineNum">     156</span> <span class="tlaUNC">           0 :       onSecondaryContainer: const Color(0xff3E4044).withOpacity(0.69),</span></span>
<span id="L157"><span class="lineNum">     157</span>              :       signInColor: const Color(0xFFEDEDED),</span>
<span id="L158"><span class="lineNum">     158</span>              :       profileChamferColor: const Color(0xFF3A3B3C),</span>
<span id="L159"><span class="lineNum">     159</span>              :       basicAdvanceTextColor: Colors.white,</span>
<span id="L160"><span class="lineNum">     160</span>              :       gridLineColor: const Color(0xFFB8B8B8),</span>
<span id="L161"><span class="lineNum">     161</span>              :       pleaseSignInColor: const Color(0xFFE7E7E7),</span>
<span id="L162"><span class="lineNum">     162</span>              :       calibrateTabBGColor: const Color(0xFF434343),</span>
<span id="L163"><span class="lineNum">     163</span>              :       drawerHeadingColor: const Color(0xFFCDCDCD),</span>
<span id="L164"><span class="lineNum">     164</span>              :       numberWheelSelectedBG: const Color(0xFF4F4F4F),</span>
<span id="L165"><span class="lineNum">     165</span>              :       textfieldCursorColor: const Color(0xFFA9A9A9),</span>
<span id="L166"><span class="lineNum">     166</span>              :       textfieldHintColor: const Color(0xFFAEAEAE),</span>
<span id="L167"><span class="lineNum">     167</span>              :       dropDownColor: const Color(0xFF393939),</span>
<span id="L168"><span class="lineNum">     168</span>              :       textfieldTextColor: Colors.white);</span>
<span id="L169"><span class="lineNum">     169</span>              : </span>
<span id="L170"><span class="lineNum">     170</span> <span class="tlaUNC">           0 :   storeThemeMode() {</span></span>
<span id="L171"><span class="lineNum">     171</span>              :     try {</span>
<span id="L172"><span class="lineNum">     172</span>              :       const FlutterSecureStorage()</span>
<span id="L173"><span class="lineNum">     173</span> <span class="tlaUNC">           0 :           .write(key: 'themeMode', value: isDark.toString());</span></span>
<span id="L174"><span class="lineNum">     174</span>              :     } catch (e) {</span>
<span id="L175"><span class="lineNum">     175</span> <span class="tlaUNC">           0 :       print(e);</span></span>
<span id="L176"><span class="lineNum">     176</span>              :     }</span>
<span id="L177"><span class="lineNum">     177</span>              :   }</span>
<span id="L178"><span class="lineNum">     178</span>              : </span>
<span id="L179"><span class="lineNum">     179</span> <span class="tlaUNC">           0 :   readThemeMode() async {</span></span>
<span id="L180"><span class="lineNum">     180</span>              :     // print(&quot;App Signature: &quot;);</span>
<span id="L181"><span class="lineNum">     181</span>              :     // print(await SmsAutoFill().getAppSignature);</span>
<span id="L182"><span class="lineNum">     182</span>              : </span>
<span id="L183"><span class="lineNum">     183</span>              :     try {</span>
<span id="L184"><span class="lineNum">     184</span>              :       String? themeModeString =</span>
<span id="L185"><span class="lineNum">     185</span> <span class="tlaUNC">           0 :           await const FlutterSecureStorage().read(key: 'themeMode');</span></span>
<span id="L186"><span class="lineNum">     186</span>              :       if (themeModeString != null) {</span>
<span id="L187"><span class="lineNum">     187</span> <span class="tlaUNC">           0 :         isDark = themeModeString == 'true';</span></span>
<span id="L188"><span class="lineNum">     188</span>              :       } else {</span>
<span id="L189"><span class="lineNum">     189</span> <span class="tlaUNC">           0 :         if (ThemeMode.system == ThemeMode.dark) {</span></span>
<span id="L190"><span class="lineNum">     190</span> <span class="tlaUNC">           0 :           isDark = true;</span></span>
<span id="L191"><span class="lineNum">     191</span>              :         }</span>
<span id="L192"><span class="lineNum">     192</span>              :       }</span>
<span id="L193"><span class="lineNum">     193</span>              :     } catch (e) {</span>
<span id="L194"><span class="lineNum">     194</span> <span class="tlaUNC">           0 :       print(e);</span></span>
<span id="L195"><span class="lineNum">     195</span>              :     }</span>
<span id="L196"><span class="lineNum">     196</span>              : </span>
<span id="L197"><span class="lineNum">     197</span> <span class="tlaUNC">           0 :     setThemeMode();</span></span>
<span id="L198"><span class="lineNum">     198</span>              :   }</span>
<span id="L199"><span class="lineNum">     199</span>              : </span>
<span id="L200"><span class="lineNum">     200</span> <span class="tlaUNC">           0 :   setThemeMode() {</span></span>
<span id="L201"><span class="lineNum">     201</span> <span class="tlaUNC">           0 :     if (isDark) {</span></span>
<span id="L202"><span class="lineNum">     202</span> <span class="tlaUNC">           0 :       _currentTheme = _darkTheme;</span></span>
<span id="L203"><span class="lineNum">     203</span>              :     } else {</span>
<span id="L204"><span class="lineNum">     204</span> <span class="tlaUNC">           0 :       _currentTheme = _lightTheme;</span></span>
<span id="L205"><span class="lineNum">     205</span>              :     }</span>
<span id="L206"><span class="lineNum">     206</span> <span class="tlaUNC">           0 :     notifyListeners();</span></span>
<span id="L207"><span class="lineNum">     207</span>              :   }</span>
<span id="L208"><span class="lineNum">     208</span>              : </span>
<span id="L209"><span class="lineNum">     209</span> <span class="tlaUNC">           0 :   CustomThemeData get currentTheme =&gt; _currentTheme;</span></span>
<span id="L210"><span class="lineNum">     210</span>              : </span>
<span id="L211"><span class="lineNum">     211</span> <span class="tlaUNC">           0 :   void toggleTheme() {</span></span>
<span id="L212"><span class="lineNum">     212</span> <span class="tlaUNC">           0 :     isDark = !isDark;</span></span>
<span id="L213"><span class="lineNum">     213</span> <span class="tlaUNC">           0 :     storeThemeMode();</span></span>
<span id="L214"><span class="lineNum">     214</span> <span class="tlaUNC">           0 :     setThemeMode();</span></span>
<span id="L215"><span class="lineNum">     215</span>              :   }</span>
<span id="L216"><span class="lineNum">     216</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
