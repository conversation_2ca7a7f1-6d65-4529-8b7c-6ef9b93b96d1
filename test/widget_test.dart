import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mocktail/mocktail.dart';
import 'package:provider/provider.dart';
import 'package:get_it/get_it.dart';
import 'package:water_metering/bloc/auth/auth_bloc.dart';
import 'package:water_metering/bloc/auth/auth_state.dart';
import 'package:water_metering/main.dart';
import 'package:water_metering/theme/theme2.dart';
import 'package:water_metering/domain/services/biometric_service.dart';

// Mock classes
class MockAuthBloc extends Mock implements AuthBloc {}

class MockThemeNotifier extends Mock implements ThemeNotifier {}

class MockBiometricService extends Mock implements BiometricService {}

final sl = GetIt.instance;

void main() {
  group('Water Metering App Tests', () {
    late MockAuthBloc mockAuthBloc;
    late MockThemeNotifier mockThemeNotifier;
    late MockBiometricService mockBiometricService;

    setUp(() {
      mockAuthBloc = MockAuthBloc();
      mockThemeNotifier = MockThemeNotifier();
      mockBiometricService = MockBiometricService();

      // Register GetIt dependencies
      sl.registerLazySingleton<BiometricService>(() => mockBiometricService);

      // Setup biometric service mocks
      when(() => mockBiometricService.isAvailable())
          .thenAnswer((_) async => true);
      when(() => mockBiometricService.isEnrolled())
          .thenAnswer((_) async => true);

      // Setup default theme mock
      when(() => mockThemeNotifier.currentTheme).thenReturn(
        CustomThemeData(
          bgColor: Colors.white,
          gridLineColor: Colors.grey,
          onSecondaryContainer: Colors.grey,
          primaryContainer: Colors.white,
          profileChamferColor: Colors.grey,
          loginTitleColor: Colors.black,
          dropDownColor: Colors.white,
          signInColor: Colors.black,
          pleaseSignInColor: Colors.black,
          gridHeadingColor: Colors.black,
          textFieldFillColor: Colors.white,
          textfieldTextColor: Colors.black,
          textfieldHintColor: Colors.grey,
          textfieldCursorColor: Colors.blue,
          bottomNavColor: Colors.white,
          headingColor: Colors.black,
          basicAdvanceTextColor: Colors.black,
          drawerHeadingColor: Colors.black,
          tableText: Colors.black,
          editIconColor: Colors.black,
          noEntriesColor: Colors.grey,
          numberWheelSelectedBG: Colors.grey,
          editIconBG: Colors.grey,
          dialogBG: Colors.white,
          inactiveBottomNavbarIconColor: Colors.grey,
          toggleColor: Colors.grey,
          popupcolor: Colors.black,
          profileBorderColor: Colors.grey,
          dropdownColor: Colors.white,
          textFieldBGProfile: Colors.white,
          calibrateTabBGColor: Colors.white,
          iconColor: Colors.black,
          splashColor: Colors.grey,
          errorColor: Colors.red,
          primaryColor: Colors.blue,
        ),
      );
      when(() => mockThemeNotifier.isDark).thenReturn(false);
    });

    tearDown(() {
      sl.reset();
    });

    testWidgets('should render app with loading state initially',
        (WidgetTester tester) async {
      // Setup AuthBloc to emit initial state
      when(() => mockAuthBloc.state).thenReturn(AuthInitial());
      when(() => mockAuthBloc.stream)
          .thenAnswer((_) => Stream.value(AuthInitial()));

      // Build the app with mocked dependencies
      await tester.pumpWidget(
        ChangeNotifierProvider<ThemeNotifier>.value(
          value: mockThemeNotifier,
          child: BlocProvider<AuthBloc>.value(
            value: mockAuthBloc,
            child: const MyApp(),
          ),
        ),
      );

      // Verify that the app renders without crashing
      expect(find.byType(MaterialApp), findsAtLeastNWidgets(1));

      // Should show loading indicator for initial state
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should handle authentication state changes',
        (WidgetTester tester) async {
      // Setup AuthBloc to emit unauthenticated state
      when(() => mockAuthBloc.state).thenReturn(AuthUnauthenticated());
      when(() => mockAuthBloc.stream)
          .thenAnswer((_) => Stream.value(AuthUnauthenticated()));

      // Build the app with mocked dependencies
      await tester.pumpWidget(
        ChangeNotifierProvider<ThemeNotifier>.value(
          value: mockThemeNotifier,
          child: BlocProvider<AuthBloc>.value(
            value: mockAuthBloc,
            child: const MyApp(),
          ),
        ),
      );

      // Should render without crashing
      expect(find.byType(MaterialApp), findsAtLeastNWidgets(1));
    });
  });
}
