import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:water_metering/core/error/auth_failures.dart';
import 'package:water_metering/view_model/custom_exception.dart';
import 'package:water_metering/data/datasources/auth_local_datasource.dart';
import 'package:water_metering/data/datasources/auth_remote_datasource.dart';
// import 'package:water_metering/data/models/login_result.dart'; // Not needed
import 'package:water_metering/data/repositories/auth_repository_impl.dart';

import '../../helpers/test_fixtures.dart';

/// Mock classes for dependencies
class MockAuthRemoteDataSource extends Mock implements AuthRemoteDataSource {}

class MockAuthLocalDataSource extends Mock implements AuthLocalDataSource {}

/// Comprehensive unit tests for AuthRepositoryImpl
///
/// These tests verify the repository implementation's integration with
/// remote and local data sources, error handling, and data transformation.
void main() {
  group('AuthRepositoryImpl', () {
    late AuthRepositoryImpl repository;
    late MockAuthRemoteDataSource mockRemoteDataSource;
    late MockAuthLocalDataSource mockLocalDataSource;

    setUp(() {
      mockRemoteDataSource = MockAuthRemoteDataSource();
      mockLocalDataSource = MockAuthLocalDataSource();

      repository = AuthRepositoryImpl(
        remoteDataSource: mockRemoteDataSource,
        localDataSource: mockLocalDataSource,
      );

      // Register fallback values for mocktail
      registerFallbackValue(TestFixtures.createTestAuthUserModel());
      registerFallbackValue(TestFixtures.createTestAuthSessionModel());
    });

    group('login', () {
      test('should return success result when login succeeds', () async {
        // Arrange
        final loginResult = LoginResult(
          success: true,
          tokens: {
            'access_token': TestFixtures.testAccessToken,
            'refresh_token': TestFixtures.testRefreshToken,
          },
        );
        final userModel = TestFixtures.createTestAuthUserModel();

        when(() => mockRemoteDataSource.login(any(), any()))
            .thenAnswer((_) async => loginResult);
        when(() => mockLocalDataSource.saveTokens(any(), any()))
            .thenAnswer((_) async => {});
        when(() => mockLocalDataSource.isBiometricAvailable())
            .thenAnswer((_) async => true);
        when(() => mockLocalDataSource.saveCredentials(any(), any()))
            .thenAnswer((_) async => {});
        when(() => mockRemoteDataSource.getUserInfo())
            .thenAnswer((_) async => userModel);
        when(() => mockLocalDataSource.saveUser(any()))
            .thenAnswer((_) async => {});
        when(() => mockLocalDataSource.isBiometricEnabled())
            .thenAnswer((_) async => false);
        when(() => mockLocalDataSource.saveSession(any()))
            .thenAnswer((_) async => {});

        // Act
        final result = await repository.login(
          TestFixtures.testEmail,
          TestFixtures.testPassword,
        );

        // Assert
        expect(result.success, isTrue);
        expect(result.user, isNotNull);
        expect(result.user!.email, equals(TestFixtures.testEmail));
        expect(result.accessToken, equals(TestFixtures.testAccessToken));
        expect(result.refreshToken, equals(TestFixtures.testRefreshToken));

        // Verify interactions
        verify(() => mockRemoteDataSource.login(
              TestFixtures.testEmail,
              TestFixtures.testPassword,
            )).called(1);
        verify(() => mockLocalDataSource.saveTokens(
              TestFixtures.testAccessToken,
              TestFixtures.testRefreshToken,
            )).called(1);
        verify(() => mockLocalDataSource.saveCredentials(
              TestFixtures.testEmail,
              TestFixtures.testPassword,
            )).called(1);
        verify(() => mockRemoteDataSource.getUserInfo()).called(1);
        verify(() => mockLocalDataSource.saveUser(userModel)).called(1);
        verify(() => mockLocalDataSource.saveSession(any())).called(1);
      });

      test('should return two-factor required when 2FA is needed', () async {
        // Arrange
        final loginResult = LoginResult(
          success: false,
          twoFactorRefCode: TestFixtures.testTwoFactorRefCode,
        );

        when(() => mockRemoteDataSource.login(any(), any()))
            .thenAnswer((_) async => loginResult);

        // Act
        final result = await repository.login(
          TestFixtures.testEmail,
          TestFixtures.testPassword,
        );

        // Assert
        expect(result.success, isFalse);
        expect(result.requiresTwoFactor, isTrue);
        expect(
            result.twoFactorRefCode, equals(TestFixtures.testTwoFactorRefCode));

        // Verify interactions
        verify(() => mockRemoteDataSource.login(
              TestFixtures.testEmail,
              TestFixtures.testPassword,
            )).called(1);
        verifyNever(() => mockLocalDataSource.saveTokens(any(), any()));
        verifyNever(() => mockRemoteDataSource.getUserInfo());
      });

      test('should return failure when login fails', () async {
        // Arrange
        final loginResult = LoginResult(
          success: false,
          error: 'Invalid credentials',
        );

        when(() => mockRemoteDataSource.login(any(), any()))
            .thenAnswer((_) async => loginResult);

        // Act
        final result = await repository.login(
          TestFixtures.testEmail,
          'wrong_password',
        );

        // Assert
        expect(result.success, isFalse);
        expect(result.failure, isA<AuthFailure>());

        // Verify interactions
        verify(() => mockRemoteDataSource.login(
              TestFixtures.testEmail,
              'wrong_password',
            )).called(1);
        verifyNever(() => mockLocalDataSource.saveTokens(any(), any()));
      });

      test('should not save credentials when biometric is not available',
          () async {
        // Arrange
        final loginResult = LoginResult(
          success: true,
          tokens: {
            'access_token': TestFixtures.testAccessToken,
            'refresh_token': TestFixtures.testRefreshToken,
          },
        );
        final userModel = TestFixtures.createTestAuthUserModel();

        when(() => mockRemoteDataSource.login(any(), any()))
            .thenAnswer((_) async => loginResult);
        when(() => mockLocalDataSource.saveTokens(any(), any()))
            .thenAnswer((_) async => {});
        when(() => mockLocalDataSource.isBiometricAvailable())
            .thenAnswer((_) async => false);
        when(() => mockRemoteDataSource.getUserInfo())
            .thenAnswer((_) async => userModel);
        when(() => mockLocalDataSource.saveUser(any()))
            .thenAnswer((_) async => {});
        when(() => mockLocalDataSource.isBiometricEnabled())
            .thenAnswer((_) async => false);
        when(() => mockLocalDataSource.saveSession(any()))
            .thenAnswer((_) async => {});

        // Act
        await repository.login(
            TestFixtures.testEmail, TestFixtures.testPassword);

        // Assert
        verifyNever(() => mockLocalDataSource.saveCredentials(any(), any()));
      });

      test('should handle exceptions during login', () async {
        // Arrange
        when(() => mockRemoteDataSource.login(any(), any()))
            .thenThrow(CustomException('Network error'));

        // Act
        final result = await repository.login(
          TestFixtures.testEmail,
          TestFixtures.testPassword,
        );

        // Assert
        expect(result.success, isFalse);
        expect(result.failure, isA<AuthFailure>());
      });

      test('should handle exceptions during token saving', () async {
        // Arrange
        final loginResult = LoginResult(
          success: true,
          tokens: {
            'access_token': TestFixtures.testAccessToken,
            'refresh_token': TestFixtures.testRefreshToken,
          },
        );

        when(() => mockRemoteDataSource.login(any(), any()))
            .thenAnswer((_) async => loginResult);
        when(() => mockLocalDataSource.saveTokens(any(), any()))
            .thenThrow(Exception('Storage error'));

        // Act
        final result = await repository.login(
          TestFixtures.testEmail,
          TestFixtures.testPassword,
        );

        // Assert
        expect(result.success, isFalse);
        expect(result.failure, isA<AuthFailure>());
      });

      test('should handle exceptions during user info retrieval', () async {
        // Arrange
        final loginResult = LoginResult(
          success: true,
          tokens: {
            'access_token': TestFixtures.testAccessToken,
            'refresh_token': TestFixtures.testRefreshToken,
          },
        );

        when(() => mockRemoteDataSource.login(any(), any()))
            .thenAnswer((_) async => loginResult);
        when(() => mockLocalDataSource.saveTokens(any(), any()))
            .thenAnswer((_) async => {});
        when(() => mockLocalDataSource.isBiometricAvailable())
            .thenAnswer((_) async => false);
        when(() => mockRemoteDataSource.getUserInfo())
            .thenThrow(CustomException('User info error'));

        // Act
        final result = await repository.login(
          TestFixtures.testEmail,
          TestFixtures.testPassword,
        );

        // Assert
        expect(result.success, isFalse);
        expect(result.failure, isA<AuthFailure>());
      });
    });

    group('loginWithBiometric', () {
      test('should return success when biometric login succeeds', () async {
        // Arrange
        final credentials = {
          'email': TestFixtures.testEmail,
          'password': TestFixtures.testPassword,
        };
        final loginResult = LoginResult(
          success: true,
          tokens: {
            'access_token': TestFixtures.testAccessToken,
            'refresh_token': TestFixtures.testRefreshToken,
          },
        );
        final userModel = TestFixtures.createTestAuthUserModel();

        when(() => mockLocalDataSource.isBiometricAvailable())
            .thenAnswer((_) async => true);
        when(() => mockLocalDataSource.isBiometricEnabled())
            .thenAnswer((_) async => true);
        when(() => mockLocalDataSource.getCredentials())
            .thenAnswer((_) async => credentials);
        when(() => mockLocalDataSource.authenticateWithBiometric(
            reason: any(named: 'reason'))).thenAnswer((_) async => true);
        when(() => mockRemoteDataSource.login(any(), any()))
            .thenAnswer((_) async => loginResult);
        when(() => mockLocalDataSource.saveTokens(any(), any()))
            .thenAnswer((_) async => {});
        when(() => mockRemoteDataSource.getUserInfo())
            .thenAnswer((_) async => userModel);
        when(() => mockLocalDataSource.saveUser(any()))
            .thenAnswer((_) async => {});
        when(() => mockLocalDataSource.saveSession(any()))
            .thenAnswer((_) async => {});
        when(() => mockLocalDataSource.saveCredentials(any(), any()))
            .thenAnswer((_) async => {});

        // Act
        final result =
            await repository.loginWithBiometric(TestFixtures.testEmail);

        // Assert
        expect(result.success, isTrue);
        expect(result.user, isNotNull);

        // Verify interactions
        verify(() => mockLocalDataSource.isBiometricAvailable()).called(2);
        verify(() => mockLocalDataSource.isBiometricEnabled()).called(2);
        verify(() => mockLocalDataSource.getCredentials()).called(1);
        verify(() => mockLocalDataSource.authenticateWithBiometric(
              reason: 'Please authenticate to login',
            )).called(1);
        verify(() => mockRemoteDataSource.login(
              TestFixtures.testEmail,
              TestFixtures.testPassword,
            )).called(1);
      });

      test('should return failure when biometric is not available', () async {
        // Arrange
        when(() => mockLocalDataSource.isBiometricAvailable())
            .thenAnswer((_) async => false);

        // Act
        final result =
            await repository.loginWithBiometric(TestFixtures.testEmail);

        // Assert
        expect(result.success, isFalse);
        expect(result.failure, isA<BiometricNotAvailableFailure>());

        // Verify interactions
        verify(() => mockLocalDataSource.isBiometricAvailable()).called(1);
        verifyNever(() => mockLocalDataSource.isBiometricEnabled());
        verifyNever(() => mockLocalDataSource.getCredentials());
      });

      test('should return failure when biometric is not enabled', () async {
        // Arrange
        when(() => mockLocalDataSource.isBiometricAvailable())
            .thenAnswer((_) async => true);
        when(() => mockLocalDataSource.isBiometricEnabled())
            .thenAnswer((_) async => false);

        // Act
        final result =
            await repository.loginWithBiometric(TestFixtures.testEmail);

        // Assert
        expect(result.success, isFalse);
        expect(result.failure, isA<BiometricNotEnrolledFailure>());

        // Verify interactions
        verify(() => mockLocalDataSource.isBiometricAvailable()).called(1);
        verify(() => mockLocalDataSource.isBiometricEnabled()).called(1);
        verifyNever(() => mockLocalDataSource.getCredentials());
      });

      test('should return failure when no stored credentials', () async {
        // Arrange
        when(() => mockLocalDataSource.isBiometricAvailable())
            .thenAnswer((_) async => true);
        when(() => mockLocalDataSource.isBiometricEnabled())
            .thenAnswer((_) async => true);
        when(() => mockLocalDataSource.getCredentials())
            .thenAnswer((_) async => null);

        // Act
        final result =
            await repository.loginWithBiometric(TestFixtures.testEmail);

        // Assert
        expect(result.success, isFalse);
        expect(result.failure, isA<NoStoredCredentialsFailure>());

        // Verify interactions
        verify(() => mockLocalDataSource.getCredentials()).called(1);
        verifyNever(() => mockLocalDataSource.authenticateWithBiometric(
            reason: any(named: 'reason')));
      });

      test('should return failure when stored email does not match', () async {
        // Arrange
        final credentials = {
          'email': '<EMAIL>',
          'password': TestFixtures.testPassword,
        };

        when(() => mockLocalDataSource.isBiometricAvailable())
            .thenAnswer((_) async => true);
        when(() => mockLocalDataSource.isBiometricEnabled())
            .thenAnswer((_) async => true);
        when(() => mockLocalDataSource.getCredentials())
            .thenAnswer((_) async => credentials);

        // Act
        final result =
            await repository.loginWithBiometric(TestFixtures.testEmail);

        // Assert
        expect(result.success, isFalse);
        expect(result.failure, isA<NoStoredCredentialsFailure>());

        // Verify interactions
        verifyNever(() => mockLocalDataSource.authenticateWithBiometric(
            reason: any(named: 'reason')));
      });

      test('should return failure when biometric authentication fails',
          () async {
        // Arrange
        final credentials = {
          'email': TestFixtures.testEmail,
          'password': TestFixtures.testPassword,
        };

        when(() => mockLocalDataSource.isBiometricAvailable())
            .thenAnswer((_) async => true);
        when(() => mockLocalDataSource.isBiometricEnabled())
            .thenAnswer((_) async => true);
        when(() => mockLocalDataSource.getCredentials())
            .thenAnswer((_) async => credentials);
        when(() => mockLocalDataSource.authenticateWithBiometric(
            reason: any(named: 'reason'))).thenAnswer((_) async => false);

        // Act
        final result =
            await repository.loginWithBiometric(TestFixtures.testEmail);

        // Assert
        expect(result.success, isFalse);
        expect(result.failure, isA<BiometricAuthenticationFailure>());

        // Verify interactions
        verify(() => mockLocalDataSource.authenticateWithBiometric(
              reason: 'Please authenticate to login',
            )).called(1);
        verifyNever(() => mockRemoteDataSource.login(any(), any()));
      });

      test('should handle exceptions during biometric login', () async {
        // Arrange
        when(() => mockLocalDataSource.isBiometricAvailable())
            .thenThrow(Exception('Biometric check failed'));

        // Act
        final result =
            await repository.loginWithBiometric(TestFixtures.testEmail);

        // Assert
        expect(result.success, isFalse);
        expect(result.failure, isA<AuthFailure>());
      });
    });

    group('logout', () {
      test('should clear all local data on successful logout', () async {
        // Arrange
        when(() => mockRemoteDataSource.logout()).thenAnswer((_) async => {});
        when(() => mockLocalDataSource.clearAllData())
            .thenAnswer((_) async => {});

        // Act
        await repository.logout();

        // Assert
        verify(() => mockRemoteDataSource.logout()).called(1);
        verify(() => mockLocalDataSource.clearAllData()).called(1);
      });

      test('should clear local data even when remote logout fails', () async {
        // Arrange
        when(() => mockRemoteDataSource.logout())
            .thenThrow(Exception('Network error'));
        when(() => mockLocalDataSource.clearAllData())
            .thenAnswer((_) async => {});

        // Act
        await repository.logout();

        // Assert
        verify(() => mockRemoteDataSource.logout()).called(1);
        verify(() => mockLocalDataSource.clearAllData()).called(1);
      });

      test('should not throw exception when local data clearing fails',
          () async {
        // Arrange
        when(() => mockRemoteDataSource.logout()).thenAnswer((_) async => {});
        when(() => mockLocalDataSource.clearAllData())
            .thenThrow(Exception('Storage error'));

        // Act & Assert
        expect(() => repository.logout(), returnsNormally);
      });
    });

    group('globalLogout', () {
      test('should clear all local data on successful global logout', () async {
        // Arrange
        when(() => mockRemoteDataSource.logout()).thenAnswer((_) async => {});
        when(() => mockLocalDataSource.clearAllData())
            .thenAnswer((_) async => {});

        // Act
        await repository.globalLogout();

        // Assert
        verify(() => mockRemoteDataSource.logout()).called(1);
        verify(() => mockLocalDataSource.clearAllData()).called(1);
      });

      test('should throw exception when remote global logout fails', () async {
        // Arrange
        when(() => mockRemoteDataSource.logout())
            .thenThrow(Exception('Network error'));
        when(() => mockLocalDataSource.clearAllData())
            .thenAnswer((_) async => {});

        // Act & Assert
        expect(() => repository.globalLogout(), throwsException);
        verify(() => mockLocalDataSource.clearAllData()).called(1);
      });
    });

    group('verifyTwoFactor', () {
      test('should return success when two-factor verification succeeds',
          () async {
        // Arrange
        final tokens = {
          'access_token': TestFixtures.testAccessToken,
          'refresh_token': TestFixtures.testRefreshToken,
        };
        final userModel = TestFixtures.createTestAuthUserModel();

        when(() => mockRemoteDataSource.verifyTwoFactor(any(), any()))
            .thenAnswer((_) async => tokens);
        when(() => mockLocalDataSource.saveTokens(any(), any()))
            .thenAnswer((_) async => {});
        when(() => mockRemoteDataSource.getUserInfo())
            .thenAnswer((_) async => userModel);
        when(() => mockLocalDataSource.saveUser(any()))
            .thenAnswer((_) async => {});
        when(() => mockLocalDataSource.isBiometricEnabled())
            .thenAnswer((_) async => false);
        when(() => mockLocalDataSource.saveSession(any()))
            .thenAnswer((_) async => {});
        when(() => mockLocalDataSource.setTwoFactorEnabled(any()))
            .thenAnswer((_) async => {});

        // Act
        final result = await repository.verifyTwoFactor(
          TestFixtures.testTwoFactorRefCode,
          TestFixtures.testTwoFactorCode,
        );

        // Assert
        expect(result.success, isTrue);
        expect(result.user, isNotNull);
        expect(result.accessToken, equals(TestFixtures.testAccessToken));
        expect(result.refreshToken, equals(TestFixtures.testRefreshToken));

        // Verify interactions
        verify(() => mockRemoteDataSource.verifyTwoFactor(
              TestFixtures.testTwoFactorRefCode,
              TestFixtures.testTwoFactorCode,
            )).called(1);
        verify(() => mockLocalDataSource.saveTokens(
              TestFixtures.testAccessToken,
              TestFixtures.testRefreshToken,
            )).called(1);
        verify(() => mockRemoteDataSource.getUserInfo()).called(1);
        verify(() => mockLocalDataSource.saveUser(userModel)).called(1);
        verify(() => mockLocalDataSource.saveSession(any())).called(1);
      });

      test('should handle exceptions during two-factor verification', () async {
        // Arrange
        when(() => mockRemoteDataSource.verifyTwoFactor(any(), any()))
            .thenThrow(CustomException('Invalid code'));

        // Act
        final result = await repository.verifyTwoFactor(
          TestFixtures.testTwoFactorRefCode,
          TestFixtures.invalidTwoFactorCode,
        );

        // Assert
        expect(result.success, isFalse);
        expect(result.failure, isA<AuthFailure>());

        // Verify interactions
        verify(() => mockRemoteDataSource.verifyTwoFactor(
              TestFixtures.testTwoFactorRefCode,
              TestFixtures.invalidTwoFactorCode,
            )).called(1);
        verifyNever(() => mockLocalDataSource.saveTokens(any(), any()));
      });
    });

    group('refreshToken', () {
      test('should return new access token when refresh succeeds', () async {
        // Arrange
        final tokens = {
          'access_token': 'new_access_token',
          'refresh_token': 'new_refresh_token',
        };

        when(() => mockLocalDataSource.getRefreshToken())
            .thenAnswer((_) async => TestFixtures.testRefreshToken);
        when(() => mockRemoteDataSource.refreshToken(any()))
            .thenAnswer((_) async => tokens);
        when(() => mockLocalDataSource.saveTokens(any(), any()))
            .thenAnswer((_) async => {});
        when(() => mockLocalDataSource.getSession())
            .thenAnswer((_) async => TestFixtures.createTestAuthSessionModel());
        when(() => mockLocalDataSource.saveSession(any()))
            .thenAnswer((_) async => {});

        // Act
        final result = await repository.refreshToken();

        // Assert
        expect(result, equals('new_access_token'));

        // Verify interactions
        verify(() => mockLocalDataSource.getRefreshToken()).called(1);
        verify(() => mockRemoteDataSource
            .refreshToken(TestFixtures.testRefreshToken)).called(1);
        verify(() => mockLocalDataSource.saveTokens(
            'new_access_token', 'new_refresh_token')).called(1);
      });

      test('should throw exception when no refresh token available', () async {
        // Arrange
        when(() => mockLocalDataSource.getRefreshToken())
            .thenAnswer((_) async => null);

        // Act & Assert
        expect(() => repository.refreshToken(), throwsException);

        // Verify interactions
        verify(() => mockLocalDataSource.getRefreshToken()).called(1);
        verifyNever(() => mockRemoteDataSource.refreshToken(any()));
      });

      test('should throw exception when remote refresh fails', () async {
        // Arrange
        when(() => mockLocalDataSource.getRefreshToken())
            .thenAnswer((_) async => TestFixtures.testRefreshToken);
        when(() => mockRemoteDataSource.refreshToken(any()))
            .thenThrow(CustomException('Token expired'));

        // Act & Assert
        expect(() => repository.refreshToken(), throwsException);

        // Verify interactions
        verify(() => mockLocalDataSource.getRefreshToken()).called(1);
        verifyNever(() => mockRemoteDataSource.refreshToken(any()));
        verifyNever(() => mockLocalDataSource.saveTokens(any(), any()));
      });
    });

    group('isAuthenticated', () {
      test('should return true when session is valid', () async {
        // Arrange
        final session = TestFixtures.createTestAuthSessionModel(
          isValid: true,
          isTokenExpiring: false,
        );

        when(() => mockLocalDataSource.getSession())
            .thenAnswer((_) async => session);

        // Act
        final result = await repository.isAuthenticated();

        // Assert
        expect(result, isTrue);

        // Verify interactions
        verify(() => mockLocalDataSource.getSession()).called(1);
      });

      test('should return false when no session exists', () async {
        // Arrange
        when(() => mockLocalDataSource.getSession())
            .thenAnswer((_) async => null);

        // Act
        final result = await repository.isAuthenticated();

        // Assert
        expect(result, isFalse);

        // Verify interactions
        verify(() => mockLocalDataSource.getSession()).called(1);
      });

      test('should return false when session is invalid', () async {
        // Arrange
        final session = TestFixtures.createTestAuthSessionModel(
          isValid: false,
        );

        when(() => mockLocalDataSource.getSession())
            .thenAnswer((_) async => session);

        // Act
        final result = await repository.isAuthenticated();

        // Assert
        expect(result, isFalse);

        // Verify interactions
        verify(() => mockLocalDataSource.getSession()).called(1);
      });

      test('should return false when session is expiring (invalid)', () async {
        // Arrange
        final session = TestFixtures.createTestAuthSessionModel(
          isTokenExpiring: true,
        );

        when(() => mockLocalDataSource.getSession())
            .thenAnswer((_) async => session);

        // Act
        final result = await repository.isAuthenticated();

        // Assert
        expect(result, isFalse);

        // Verify interactions
        verify(() => mockLocalDataSource.getSession()).called(1);
        verifyNever(() => mockLocalDataSource.getRefreshToken());
        verifyNever(() => mockRemoteDataSource.refreshToken(any()));
      });

      test('should return false when token refresh fails', () async {
        // Arrange
        final session = TestFixtures.createTestAuthSessionModel(
          isValid: true,
          isTokenExpiring: true,
        );

        when(() => mockLocalDataSource.getSession())
            .thenAnswer((_) async => session);
        when(() => mockLocalDataSource.getRefreshToken())
            .thenAnswer((_) async => TestFixtures.testRefreshToken);
        when(() => mockRemoteDataSource.refreshToken(any()))
            .thenThrow(Exception('Token refresh failed'));

        // Act
        final result = await repository.isAuthenticated();

        // Assert
        expect(result, isFalse);
      });

      test('should return false when exception occurs', () async {
        // Arrange
        when(() => mockLocalDataSource.getSession())
            .thenThrow(Exception('Storage error'));

        // Act
        final result = await repository.isAuthenticated();

        // Assert
        expect(result, isFalse);
      });
    });

    group('getCurrentUser', () {
      test('should return user when authenticated and user exists locally',
          () async {
        // Arrange
        final userModel = TestFixtures.createTestAuthUserModel();

        when(() => mockLocalDataSource.getSession()).thenAnswer((_) async =>
            TestFixtures.createTestAuthSessionModel(isValid: true));
        when(() => mockLocalDataSource.getUser())
            .thenAnswer((_) async => userModel);

        // Act
        final result = await repository.getCurrentUser();

        // Assert
        expect(result, isNotNull);
        expect(result!.email, equals(TestFixtures.testEmail));

        // Verify interactions
        verify(() => mockLocalDataSource.getSession()).called(1);
        verify(() => mockLocalDataSource.getUser()).called(1);
        verifyNever(() => mockRemoteDataSource.getUserInfo());
      });

      test('should fetch user from remote when not available locally',
          () async {
        // Arrange
        final userModel = TestFixtures.createTestAuthUserModel();

        when(() => mockLocalDataSource.getSession()).thenAnswer((_) async =>
            TestFixtures.createTestAuthSessionModel(isValid: true));
        when(() => mockLocalDataSource.getUser()).thenAnswer((_) async => null);
        when(() => mockRemoteDataSource.getUserInfo())
            .thenAnswer((_) async => userModel);
        when(() => mockLocalDataSource.saveUser(any()))
            .thenAnswer((_) async => {});

        // Act
        final result = await repository.getCurrentUser();

        // Assert
        expect(result, isNotNull);
        expect(result!.email, equals(TestFixtures.testEmail));

        // Verify interactions
        verify(() => mockLocalDataSource.getSession()).called(1);
        verify(() => mockLocalDataSource.getUser()).called(1);
        verify(() => mockRemoteDataSource.getUserInfo()).called(1);
        verify(() => mockLocalDataSource.saveUser(userModel)).called(1);
      });

      test('should return null when not authenticated', () async {
        // Arrange
        when(() => mockLocalDataSource.getSession())
            .thenAnswer((_) async => null);

        // Act
        final result = await repository.getCurrentUser();

        // Assert
        expect(result, isNull);

        // Verify interactions
        verify(() => mockLocalDataSource.getSession()).called(1);
        verifyNever(() => mockLocalDataSource.getUser());
        verifyNever(() => mockRemoteDataSource.getUserInfo());
      });

      test('should return null when session is invalid', () async {
        // Arrange
        when(() => mockLocalDataSource.getSession()).thenAnswer((_) async =>
            TestFixtures.createTestAuthSessionModel(isValid: false));

        // Act
        final result = await repository.getCurrentUser();

        // Assert
        expect(result, isNull);

        // Verify interactions
        verify(() => mockLocalDataSource.getSession()).called(1);
        verifyNever(() => mockLocalDataSource.getUser());
        verifyNever(() => mockRemoteDataSource.getUserInfo());
      });

      test('should handle exceptions during user retrieval', () async {
        // Arrange
        when(() => mockLocalDataSource.getSession()).thenAnswer((_) async =>
            TestFixtures.createTestAuthSessionModel(isValid: true));
        when(() => mockLocalDataSource.getUser())
            .thenThrow(Exception('Storage error'));

        // Act
        final result = await repository.getCurrentUser();

        // Assert
        expect(result, isNull);

        // Verify interactions
        verify(() => mockLocalDataSource.getSession()).called(1);
        verify(() => mockLocalDataSource.getUser()).called(1);
        verifyNever(() => mockRemoteDataSource.getUserInfo());
      });

      test('should handle exceptions during remote user fetch', () async {
        // Arrange
        when(() => mockLocalDataSource.getSession()).thenAnswer((_) async =>
            TestFixtures.createTestAuthSessionModel(isValid: true));
        when(() => mockLocalDataSource.getUser()).thenAnswer((_) async => null);
        when(() => mockRemoteDataSource.getUserInfo())
            .thenThrow(Exception('Network error'));

        // Act
        final result = await repository.getCurrentUser();

        // Assert
        expect(result, isNull);

        // Verify interactions
        verify(() => mockLocalDataSource.getSession()).called(1);
        verify(() => mockLocalDataSource.getUser()).called(1);
        verify(() => mockRemoteDataSource.getUserInfo()).called(1);
        verifyNever(() => mockLocalDataSource.saveUser(any()));
      });

      test('should return null when session is expiring (invalid)', () async {
        // Arrange
        when(() => mockLocalDataSource.getSession())
            .thenAnswer((_) async => TestFixtures.createTestAuthSessionModel(
                  isTokenExpiring: true,
                ));

        // Act
        final result = await repository.getCurrentUser();

        // Assert
        expect(result, isNull);

        // Verify interactions
        verify(() => mockLocalDataSource.getSession()).called(1);
        verifyNever(() => mockLocalDataSource.getRefreshToken());
        verifyNever(() => mockRemoteDataSource.refreshToken(any()));
        verifyNever(() => mockLocalDataSource.getUser());
      });

      test('should return null when session is invalid', () async {
        // Arrange
        when(() => mockLocalDataSource.getSession())
            .thenAnswer((_) async => TestFixtures.createTestAuthSessionModel(
                  isTokenExpiring: true,
                ));

        // Act
        final result = await repository.getCurrentUser();

        // Assert
        expect(result, isNull);

        // Verify interactions
        verify(() => mockLocalDataSource.getSession()).called(1);
        verifyNever(() => mockLocalDataSource.getRefreshToken());
        verifyNever(() => mockRemoteDataSource.refreshToken(any()));
        verifyNever(() => mockLocalDataSource.getUser());
      });
    });
  });
}
