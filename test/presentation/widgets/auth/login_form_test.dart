import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mocktail/mocktail.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import 'package:water_metering/presentation/widgets/auth/login_form.dart';
import 'package:water_metering/bloc/auth/auth_bloc.dart';
import 'package:water_metering/bloc/auth/auth_state.dart';
import 'package:water_metering/bloc/auth/auth_event.dart';
import 'package:water_metering/views/widgets/containers/PasswordController.dart';
import 'package:water_metering/views/widgets/containers/customTextField.dart';
import 'package:water_metering/theme/theme2.dart';

// Mock classes
class MockAuthBloc extends Mock implements AuthBloc {}

class MockThemeNotifier extends Mock implements ThemeNotifier {}

void main() {
  group('LoginForm Widget Tests', () {
    late MockAuthBloc mockAuthBloc;
    late MockThemeNotifier mockThemeNotifier;
    late TextEditingController emailController;
    late ObscuringTextEditingController passwordController;

    setUpAll(() {
      registerFallbackValue(AuthInitial());
      registerFallbackValue(AuthLoginRequested('', ''));
    });

    setUp(() {
      mockAuthBloc = MockAuthBloc();
      mockThemeNotifier = MockThemeNotifier();
      emailController = TextEditingController();
      passwordController = ObscuringTextEditingController();

      // Mock the AuthBloc stream and close method
      when(() => mockAuthBloc.stream).thenAnswer((_) => const Stream.empty());
      when(() => mockAuthBloc.state).thenReturn(AuthInitial());
      when(() => mockAuthBloc.close()).thenAnswer((_) async {});

      // Setup default theme mock
      when(() => mockThemeNotifier.currentTheme).thenReturn(
        CustomThemeData(
          bgColor: const Color(0XFFe5ebf0),
          bottomNavColor: const Color.fromRGBO(250, 250, 250, 1),
          textFieldFillColor: Colors.white,
          gridLineColor: const Color(0xff8C8C8C),
          loginTitleColor: const Color(0xff40434F),
          editIconColor: const Color(0xFF515151),
          editIconBG: const Color(0xFFCECECE),
          numberWheelSelectedBG: const Color(0xFFCECECE),
          tableText: const Color(0xff515151),
          dropdownColor: const Color(0xFFF5F5F5),
          primaryContainer: Colors.white,
          noEntriesColor: const Color(0xFFB0B0B0),
          dropDownColor: const Color(0xFFF5F5F5),
          onSecondaryContainer: const Color(0xffF2F2F2),
          dialogBG: const Color(0xFFF0F0F0),
          textFieldBGProfile: const Color(0xFFF2F2F2),
          popupcolor: const Color(0xFF2D2D2D),
          signInColor: const Color(0xFF515151),
          profileChamferColor: const Color(0XFFD4D4D4),
          calibrateTabBGColor: Colors.white,
          headingColor: const Color(0xFF525252),
          iconColor: const Color(0xFF5A5A5A),
          gridHeadingColor: const Color(0xFF383838),
          drawerHeadingColor: const Color(0xFF2D2D2D),
          profileBorderColor: const Color(0xFF747474),
          basicAdvanceTextColor: const Color(0xFF2D2D2D),
          inactiveBottomNavbarIconColor: const Color(0xFF4F4F4F),
          pleaseSignInColor: const Color(0xFF515151),
          toggleColor: const Color(0xFFCECACA),
          textfieldCursorColor: const Color(0xFFA9A9A9),
          splashColor: const Color(0x66c8c8c8),
          textfieldHintColor: const Color(0xFFAEAEAE),
          textfieldTextColor: const Color(0xFF646464),
          errorColor: Colors.red,
          primaryColor: Colors.blue,
        ),
      );
    });

    tearDown(() {
      emailController.dispose();
      passwordController.dispose();
    });

    Widget createTestWidget({
      TextEditingController? emailCtrl,
      ObscuringTextEditingController? passwordCtrl,
      Function(String, String)? onLoginRequested,
      bool showValidationErrors = false,
      bool enabled = true,
      String emailHint = 'Enter Email',
      String passwordHint = 'Enter Password',
      bool autoFocus = false,
    }) {
      return ScreenUtilInit(
        designSize: const Size(375, 812),
        builder: (context, child) => MultiProvider(
          providers: [
            BlocProvider<AuthBloc>(create: (_) => mockAuthBloc),
            ChangeNotifierProvider<ThemeNotifier>.value(
                value: mockThemeNotifier),
          ],
          child: MaterialApp(
            home: Scaffold(
              body: LoginForm(
                emailController: emailCtrl ?? emailController,
                passwordController: passwordCtrl ?? passwordController,
                onLoginRequested: onLoginRequested,
                showValidationErrors: showValidationErrors,
                enabled: enabled,
                emailHint: emailHint,
                passwordHint: passwordHint,
                autoFocus: autoFocus,
              ),
            ),
          ),
        ),
      );
    }

    // Helper function to find email field by hint text
    Finder findEmailField(String emailHint) {
      return find.widgetWithText(CustomTextField, emailHint);
    }

    group('Widget Rendering', () {
      testWidgets('should render email and password fields', (tester) async {
        await tester.pumpWidget(createTestWidget());

        // Find email field by hint text
        expect(find.text('Enter Email'), findsOneWidget);

        // Find password field
        expect(find.byType(PasswordTextField), findsOneWidget);
        expect(find.text('Enter Password'), findsOneWidget);
      });

      testWidgets('should use custom hint texts when provided', (tester) async {
        await tester.pumpWidget(createTestWidget(
          emailHint: 'Custom Email Hint',
          passwordHint: 'Custom Password Hint',
        ));

        expect(find.text('Custom Email Hint'), findsOneWidget);
        expect(find.text('Custom Password Hint'), findsOneWidget);
      });

      testWidgets('should show validation errors when enabled', (tester) async {
        await tester.pumpWidget(createTestWidget(
          showValidationErrors: true,
        ));

        // Enter invalid email and trigger validation
        await tester.enterText(findEmailField('Enter Email'), 'invalid-email');
        await tester.pump();

        // Access the form state to trigger validation
        final loginFormState =
            tester.state<LoginFormState>(find.byType(LoginForm));
        loginFormState.validate();
        await tester.pump();

        // Should show email validation error
        expect(find.text('Please enter a valid email address'), findsOneWidget);
      });

      testWidgets('should not show validation errors when disabled',
          (tester) async {
        await tester.pumpWidget(createTestWidget(
          showValidationErrors: false,
        ));

        // Enter invalid email and trigger validation
        await tester.enterText(findEmailField('Enter Email'), 'invalid-email');
        await tester.pump();

        // Access the form state to trigger validation
        final loginFormState =
            tester.state<LoginFormState>(find.byType(LoginForm));
        loginFormState.validate();
        await tester.pump();

        // Should not show validation errors
        expect(find.text('Please enter a valid email address'), findsNothing);
      });
    });

    group('Form Validation', () {
      testWidgets('should validate email format', (tester) async {
        await tester.pumpWidget(createTestWidget());

        final loginFormState =
            tester.state<LoginFormState>(find.byType(LoginForm));

        // Test invalid email
        await tester.enterText(findEmailField('Enter Email'), 'invalid-email');
        await tester.pump();
        expect(loginFormState.validate(), isFalse);

        // Test valid email
        await tester.enterText(
            findEmailField('Enter Email'), '<EMAIL>');
        await tester.pump();
        expect(loginFormState.validate(),
            isFalse); // Still false because password is empty

        // Add password
        passwordController.text = 'password123';
        await tester.pump();
        expect(loginFormState.validate(), isTrue);
      });

      testWidgets('should validate password is not empty', (tester) async {
        await tester.pumpWidget(createTestWidget());

        final loginFormState =
            tester.state<LoginFormState>(find.byType(LoginForm));

        // Test with valid email but empty password
        await tester.enterText(
            findEmailField('Enter Email'), '<EMAIL>');
        await tester.pump();
        expect(loginFormState.validate(), isFalse);

        // Add password
        passwordController.text = 'password123';
        await tester.pump();
        expect(loginFormState.validate(), isTrue);
      });
    });

    group('Form Submission', () {
      testWidgets('should call onLoginRequested when provided', (tester) async {
        String? capturedEmail;
        String? capturedPassword;

        await tester.pumpWidget(createTestWidget(
          onLoginRequested: (email, password) {
            capturedEmail = email;
            capturedPassword = password;
          },
        ));

        // Enter valid credentials
        await tester.enterText(
            findEmailField('Enter Email'), '<EMAIL>');
        passwordController.text = 'password123';
        await tester.pump();

        // Submit form
        final loginFormState =
            tester.state<LoginFormState>(find.byType(LoginForm));
        loginFormState.submit();
        await tester.pump();

        // Verify callback was called with correct values
        expect(capturedEmail, equals('<EMAIL>'));
        expect(capturedPassword, equals('password123'));
      });

      testWidgets('should trigger AuthBloc when no callback provided',
          (tester) async {
        await tester.pumpWidget(createTestWidget());

        // Enter valid credentials
        await tester.enterText(
            findEmailField('Enter Email'), '<EMAIL>');
        passwordController.text = 'password123';
        await tester.pump();

        // Submit form
        final loginFormState =
            tester.state<LoginFormState>(find.byType(LoginForm));
        loginFormState.submit();
        await tester.pump();

        // Verify AuthBloc.add was called
        verify(() => mockAuthBloc.add(any(that: isA<AuthLoginRequested>())))
            .called(1);
      });

      testWidgets('should not submit when form is invalid', (tester) async {
        bool callbackCalled = false;

        await tester.pumpWidget(createTestWidget(
          onLoginRequested: (email, password) {
            callbackCalled = true;
          },
        ));

        // Enter invalid email
        await tester.enterText(findEmailField('Enter Email'), 'invalid-email');
        await tester.pump();

        // Submit form
        final loginFormState =
            tester.state<LoginFormState>(find.byType(LoginForm));
        loginFormState.submit();
        await tester.pump();

        // Verify callback was not called
        expect(callbackCalled, isFalse);
        verifyNever(() => mockAuthBloc.add(any()));
      });

      testWidgets('should not submit when disabled', (tester) async {
        bool callbackCalled = false;

        await tester.pumpWidget(createTestWidget(
          enabled: false,
          onLoginRequested: (email, password) {
            callbackCalled = true;
          },
        ));

        // Enter valid credentials
        await tester.enterText(
            findEmailField('Enter Email'), '<EMAIL>');
        passwordController.text = 'password123';
        await tester.pump();

        // Submit form
        final loginFormState =
            tester.state<LoginFormState>(find.byType(LoginForm));
        loginFormState.submit();
        await tester.pump();

        // Verify callback was not called
        expect(callbackCalled, isFalse);
        verifyNever(() => mockAuthBloc.add(any()));
      });
    });

    group('User Interaction', () {
      testWidgets('should clear errors when user types', (tester) async {
        await tester.pumpWidget(createTestWidget(
          showValidationErrors: true,
        ));

        // Enter invalid email to trigger error
        await tester.enterText(findEmailField('Enter Email'), 'invalid');
        await tester.pump();

        final loginFormState =
            tester.state<LoginFormState>(find.byType(LoginForm));
        loginFormState.validate();
        await tester.pump();

        // Should show error
        expect(find.text('Please enter a valid email address'), findsOneWidget);

        // Manually clear errors to test the clearErrors functionality
        loginFormState.clearErrors();
        await tester.pump();

        // Error should be cleared
        expect(find.text('Please enter a valid email address'), findsNothing);
      });

      testWidgets('should handle text input correctly', (tester) async {
        await tester.pumpWidget(createTestWidget());

        // Test email input
        await tester.enterText(
            findEmailField('Enter Email'), '<EMAIL>');
        await tester.pump();
        expect(emailController.text, equals('<EMAIL>'));

        // Test password input
        passwordController.text = 'mypassword';
        await tester.pump();
        expect(passwordController.getText(), equals('mypassword'));
      });
    });

    group('Edge Cases', () {
      testWidgets('should handle null controllers gracefully', (tester) async {
        await tester.pumpWidget(createTestWidget(
          emailCtrl: null,
          passwordCtrl: null,
        ));

        // Widget should render without errors
        expect(find.byType(LoginForm), findsOneWidget);
        expect(find.text('Enter Email'), findsOneWidget);
        expect(find.byType(PasswordTextField), findsOneWidget);
      });

      testWidgets('should trim email input', (tester) async {
        String? capturedEmail;

        await tester.pumpWidget(createTestWidget(
          onLoginRequested: (email, password) {
            capturedEmail = email;
          },
        ));

        // Enter email with spaces
        await tester.enterText(
            findEmailField('Enter Email'), '  <EMAIL>  ');
        passwordController.text = 'password123';
        await tester.pump();

        // Submit form
        final loginFormState =
            tester.state<LoginFormState>(find.byType(LoginForm));
        loginFormState.submit();
        await tester.pump();

        // Verify email was trimmed
        expect(capturedEmail, equals('<EMAIL>'));
      });
    });
  });
}
