import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:water_metering/bloc/auth/auth_bloc.dart';
import 'package:water_metering/bloc/auth/auth_event.dart';
import 'package:water_metering/bloc/auth/auth_state.dart';
import 'package:water_metering/core/error/auth_failures.dart';
import 'package:water_metering/core/services/auth_state_service.dart';

import '../../helpers/test_fixtures.dart';
import '../../helpers/test_helper.dart';

// Mock for AuthStateService
class MockAuthStateService extends Mock implements AuthStateService {}

/// Comprehensive unit tests for AuthBloc
///
/// These tests verify all authentication state transitions, event handling,
/// and integration with domain layer use cases using bloc_test package.
void main() {
  group('AuthBloc', () {
    late AuthBloc authBloc;
    late MockLoginUseCase mockLoginUseCase;
    late MockBiometricLoginUseCase mockBiometricLoginUseCase;
    late MockLogoutUseCase mockLogoutUseCase;
    late MockVerifyTwoFactorUseCase mockVerifyTwoFactorUseCase;
    late MockAuthRepository mockAuthRepository;
    late MockAuthStateService mockAuthStateService;

    setUp(() {
      mockLoginUseCase = MockLoginUseCase();
      mockBiometricLoginUseCase = MockBiometricLoginUseCase();
      mockLogoutUseCase = MockLogoutUseCase();
      mockVerifyTwoFactorUseCase = MockVerifyTwoFactorUseCase();
      mockAuthRepository = MockAuthRepository();
      mockAuthStateService = MockAuthStateService();

      authBloc = AuthBloc(
        loginUseCase: mockLoginUseCase,
        biometricLoginUseCase: mockBiometricLoginUseCase,
        logoutUseCase: mockLogoutUseCase,
        verifyTwoFactorUseCase: mockVerifyTwoFactorUseCase,
        authRepository: mockAuthRepository,
        authStateService: mockAuthStateService,
      );
    });

    tearDown(() {
      authBloc.close();
    });

    test('initial state should be AuthInitial', () {
      expect(authBloc.state, equals(AuthInitial()));
    });

    group('AuthLoginRequested', () {
      blocTest<AuthBloc, AuthState>(
        'should emit [AuthLoading, AuthAuthenticated] when login succeeds',
        build: () {
          when(() => mockLoginUseCase.call(any(), any()))
              .thenAnswer((_) async => TestFixtures.createSuccessAuthResult());
          return authBloc;
        },
        act: (bloc) => bloc.add(AuthLoginRequested(
          TestFixtures.testEmail,
          TestFixtures.testPassword,
        )),
        expect: () => [
          AuthLoading(),
          AuthAuthenticated(TestFixtures.createTestAuthUser()),
        ],
        verify: (_) {
          verify(() => mockLoginUseCase.call(
                TestFixtures.testEmail,
                TestFixtures.testPassword,
              )).called(1);
        },
      );

      blocTest<AuthBloc, AuthState>(
        'should emit [AuthLoading, AuthTwoFactorRequired] when 2FA is required',
        build: () {
          when(() => mockLoginUseCase.call(any(), any())).thenAnswer(
              (_) async => TestFixtures.createTwoFactorRequiredAuthResult());
          return authBloc;
        },
        act: (bloc) => bloc.add(AuthLoginRequested(
          TestFixtures.testEmail,
          TestFixtures.testPassword,
        )),
        expect: () => [
          AuthLoading(),
          AuthTwoFactorRequired(TestFixtures.testTwoFactorRefCode),
        ],
        verify: (_) {
          verify(() => mockLoginUseCase.call(
                TestFixtures.testEmail,
                TestFixtures.testPassword,
              )).called(1);
        },
      );

      blocTest<AuthBloc, AuthState>(
        'should emit [AuthLoading, AuthError] when login fails',
        build: () {
          final failure = TestFixtures.createInvalidCredentialsFailure();
          when(() => mockLoginUseCase.call(any(), any())).thenAnswer(
              (_) async =>
                  TestFixtures.createFailureAuthResult(failure: failure));
          return authBloc;
        },
        act: (bloc) => bloc.add(AuthLoginRequested(
          TestFixtures.testEmail,
          'wrong_password',
        )),
        expect: () => [
          AuthLoading(),
          AuthError(TestFixtures.createInvalidCredentialsFailure()),
        ],
        verify: (_) {
          verify(() => mockLoginUseCase.call(
                TestFixtures.testEmail,
                'wrong_password',
              )).called(1);
        },
      );

      blocTest<AuthBloc, AuthState>(
        'should emit [AuthLoading, AuthError] when use case throws exception',
        build: () {
          when(() => mockLoginUseCase.call(any(), any()))
              .thenThrow(Exception('Network error'));
          return authBloc;
        },
        act: (bloc) => bloc.add(AuthLoginRequested(
          TestFixtures.testEmail,
          TestFixtures.testPassword,
        )),
        expect: () => [
          AuthLoading(),
          isA<AuthError>(),
        ],
        verify: (_) {
          verify(() => mockLoginUseCase.call(
                TestFixtures.testEmail,
                TestFixtures.testPassword,
              )).called(1);
        },
      );
    });

    group('AuthBiometricLoginRequested', () {
      blocTest<AuthBloc, AuthState>(
        'should emit [AuthLoading, AuthAuthenticated] when biometric login succeeds',
        build: () {
          when(() => mockBiometricLoginUseCase.call(any()))
              .thenAnswer((_) async => TestFixtures.createSuccessAuthResult());
          return authBloc;
        },
        act: (bloc) =>
            bloc.add(AuthBiometricLoginRequested(TestFixtures.testEmail)),
        expect: () => [
          AuthLoading(),
          AuthAuthenticated(TestFixtures.createTestAuthUser()),
        ],
        verify: (_) {
          verify(() => mockBiometricLoginUseCase.call(TestFixtures.testEmail))
              .called(1);
        },
      );

      blocTest<AuthBloc, AuthState>(
        'should emit [AuthLoading, AuthError] when biometric login fails',
        build: () {
          final failure = TestFixtures.createBiometricFailure();
          when(() => mockBiometricLoginUseCase.call(any())).thenAnswer(
              (_) async =>
                  TestFixtures.createFailureAuthResult(failure: failure));
          return authBloc;
        },
        act: (bloc) =>
            bloc.add(AuthBiometricLoginRequested(TestFixtures.testEmail)),
        expect: () => [
          AuthLoading(),
          AuthError(TestFixtures.createBiometricFailure()),
        ],
        verify: (_) {
          verify(() => mockBiometricLoginUseCase.call(TestFixtures.testEmail))
              .called(1);
        },
      );

      blocTest<AuthBloc, AuthState>(
        'should emit [AuthLoading, AuthError] when biometric use case throws exception',
        build: () {
          when(() => mockBiometricLoginUseCase.call(any()))
              .thenThrow(Exception('Biometric not available'));
          return authBloc;
        },
        act: (bloc) =>
            bloc.add(AuthBiometricLoginRequested(TestFixtures.testEmail)),
        expect: () => [
          AuthLoading(),
          isA<AuthError>(),
        ],
        verify: (_) {
          verify(() => mockBiometricLoginUseCase.call(TestFixtures.testEmail))
              .called(1);
        },
      );
    });

    group('AuthTwoFactorSubmitted', () {
      blocTest<AuthBloc, AuthState>(
        'should emit [AuthLoading, AuthAuthenticated] when 2FA verification succeeds',
        build: () {
          when(() => mockVerifyTwoFactorUseCase.call(any(), any()))
              .thenAnswer((_) async => TestFixtures.createSuccessAuthResult());
          return authBloc;
        },
        act: (bloc) => bloc.add(AuthTwoFactorSubmitted(
          TestFixtures.testTwoFactorRefCode,
          TestFixtures.testTwoFactorCode,
        )),
        expect: () => [
          AuthLoading(),
          AuthAuthenticated(TestFixtures.createTestAuthUser()),
        ],
        verify: (_) {
          verify(() => mockVerifyTwoFactorUseCase.call(
                TestFixtures.testTwoFactorRefCode,
                TestFixtures.testTwoFactorCode,
              )).called(1);
        },
      );

      blocTest<AuthBloc, AuthState>(
        'should emit [AuthLoading, AuthError] when 2FA verification fails',
        build: () {
          const failure = InvalidTwoFactorCodeFailure();
          when(() => mockVerifyTwoFactorUseCase.call(any(), any())).thenAnswer(
              (_) async =>
                  TestFixtures.createFailureAuthResult(failure: failure));
          return authBloc;
        },
        act: (bloc) => bloc.add(AuthTwoFactorSubmitted(
          TestFixtures.testTwoFactorRefCode,
          TestFixtures.invalidTwoFactorCode,
        )),
        expect: () => [
          AuthLoading(),
          AuthError(const InvalidTwoFactorCodeFailure()),
        ],
        verify: (_) {
          verify(() => mockVerifyTwoFactorUseCase.call(
                TestFixtures.testTwoFactorRefCode,
                TestFixtures.invalidTwoFactorCode,
              )).called(1);
        },
      );

      blocTest<AuthBloc, AuthState>(
        'should emit [AuthLoading, AuthError] when 2FA use case throws exception',
        build: () {
          when(() => mockVerifyTwoFactorUseCase.call(any(), any()))
              .thenThrow(Exception('2FA verification failed'));
          return authBloc;
        },
        act: (bloc) => bloc.add(AuthTwoFactorSubmitted(
          TestFixtures.testTwoFactorRefCode,
          TestFixtures.testTwoFactorCode,
        )),
        expect: () => [
          AuthLoading(),
          isA<AuthError>(),
        ],
        verify: (_) {
          verify(() => mockVerifyTwoFactorUseCase.call(
                TestFixtures.testTwoFactorRefCode,
                TestFixtures.testTwoFactorCode,
              )).called(1);
        },
      );
    });

    group('AuthLogoutRequested', () {
      blocTest<AuthBloc, AuthState>(
        'should emit [AuthLoading, AuthUnauthenticated] when logout succeeds',
        build: () {
          when(() => mockLogoutUseCase.call()).thenAnswer((_) async => {});
          return authBloc;
        },
        act: (bloc) => bloc.add(AuthLogoutRequested()),
        expect: () => [
          AuthLoading(),
          AuthUnauthenticated(),
        ],
        verify: (_) {
          verify(() => mockLogoutUseCase.call()).called(1);
        },
      );

      blocTest<AuthBloc, AuthState>(
        'should emit [AuthLoading, AuthUnauthenticated] even when logout fails',
        build: () {
          when(() => mockLogoutUseCase.call())
              .thenThrow(Exception('Logout failed'));
          return authBloc;
        },
        act: (bloc) => bloc.add(AuthLogoutRequested()),
        expect: () => [
          AuthLoading(),
          AuthUnauthenticated(),
        ],
        verify: (_) {
          verify(() => mockLogoutUseCase.call()).called(1);
        },
      );
    });

    group('AuthGlobalLogoutRequested', () {
      blocTest<AuthBloc, AuthState>(
        'should emit [AuthLoading, AuthUnauthenticated] when global logout succeeds',
        build: () {
          when(() => mockAuthRepository.globalLogout())
              .thenAnswer((_) async => {});
          return authBloc;
        },
        act: (bloc) => bloc.add(AuthGlobalLogoutRequested()),
        expect: () => [
          AuthLoading(),
          AuthUnauthenticated(),
        ],
        verify: (_) {
          verify(() => mockAuthRepository.globalLogout()).called(1);
        },
      );

      blocTest<AuthBloc, AuthState>(
        'should emit [AuthLoading, AuthUnauthenticated] even when global logout fails',
        build: () {
          when(() => mockAuthRepository.globalLogout())
              .thenThrow(Exception('Global logout failed'));
          return authBloc;
        },
        act: (bloc) => bloc.add(AuthGlobalLogoutRequested()),
        expect: () => [
          AuthLoading(),
          AuthUnauthenticated(),
        ],
        verify: (_) {
          verify(() => mockAuthRepository.globalLogout()).called(1);
        },
      );
    });

    group('AuthCheckRequested', () {
      blocTest<AuthBloc, AuthState>(
        'should emit [AuthAuthenticated] when user is authenticated',
        build: () {
          when(() => mockAuthRepository.isAuthenticated())
              .thenAnswer((_) async => true);
          when(() => mockAuthRepository.getCurrentUser())
              .thenAnswer((_) async => TestFixtures.createTestAuthUser());
          return authBloc;
        },
        act: (bloc) => bloc.add(AuthCheckRequested()),
        expect: () => [
          AuthAuthenticated(TestFixtures.createTestAuthUser()),
        ],
        verify: (_) {
          verify(() => mockAuthRepository.isAuthenticated()).called(1);
          verify(() => mockAuthRepository.getCurrentUser()).called(1);
        },
      );

      blocTest<AuthBloc, AuthState>(
        'should emit [AuthUnauthenticated] when user is not authenticated',
        build: () {
          when(() => mockAuthRepository.isAuthenticated())
              .thenAnswer((_) async => false);
          return authBloc;
        },
        act: (bloc) => bloc.add(AuthCheckRequested()),
        expect: () => [
          AuthUnauthenticated(),
        ],
        verify: (_) {
          verify(() => mockAuthRepository.isAuthenticated()).called(1);
          verifyNever(() => mockAuthRepository.getCurrentUser());
        },
      );

      blocTest<AuthBloc, AuthState>(
        'should emit [AuthUnauthenticated] when authenticated but no user found',
        build: () {
          when(() => mockAuthRepository.isAuthenticated())
              .thenAnswer((_) async => true);
          when(() => mockAuthRepository.getCurrentUser())
              .thenAnswer((_) async => null);
          return authBloc;
        },
        act: (bloc) => bloc.add(AuthCheckRequested()),
        expect: () => [
          AuthUnauthenticated(),
        ],
        verify: (_) {
          verify(() => mockAuthRepository.isAuthenticated()).called(1);
          verify(() => mockAuthRepository.getCurrentUser()).called(1);
        },
      );

      blocTest<AuthBloc, AuthState>(
        'should emit [AuthUnauthenticated] when repository throws exception',
        build: () {
          when(() => mockAuthRepository.isAuthenticated())
              .thenThrow(Exception('Auth check failed'));
          return authBloc;
        },
        act: (bloc) => bloc.add(AuthCheckRequested()),
        expect: () => [
          AuthUnauthenticated(),
        ],
        verify: (_) {
          verify(() => mockAuthRepository.isAuthenticated()).called(1);
        },
      );
    });

    group('AuthTokenRefreshRequested', () {
      blocTest<AuthBloc, AuthState>(
        'should not emit any state when token refresh succeeds',
        build: () {
          when(() => mockAuthRepository.refreshToken())
              .thenAnswer((_) async => 'new_token');
          return authBloc;
        },
        act: (bloc) => bloc.add(AuthTokenRefreshRequested()),
        expect: () => [],
        verify: (_) {
          verify(() => mockAuthRepository.refreshToken()).called(1);
        },
      );

      blocTest<AuthBloc, AuthState>(
        'should emit [AuthUnauthenticated] when token refresh fails',
        build: () {
          when(() => mockAuthRepository.refreshToken())
              .thenThrow(Exception('Token refresh failed'));
          return authBloc;
        },
        act: (bloc) => bloc.add(AuthTokenRefreshRequested()),
        expect: () => [
          AuthUnauthenticated(),
        ],
        verify: (_) {
          verify(() => mockAuthRepository.refreshToken()).called(1);
        },
      );
    });

    group('convenience methods', () {
      test('isAuthenticated should return true when state is AuthAuthenticated',
          () {
        // Arrange
        final user = TestFixtures.createTestAuthUser();
        authBloc.emit(AuthAuthenticated(user));

        // Act & Assert
        expect(authBloc.isAuthenticated, isTrue);
      });

      test(
          'isAuthenticated should return false when state is not AuthAuthenticated',
          () {
        // Arrange
        authBloc.emit(AuthUnauthenticated());

        // Act & Assert
        expect(authBloc.isAuthenticated, isFalse);
      });

      test('currentUser should return user when state is AuthAuthenticated',
          () {
        // Arrange
        final user = TestFixtures.createTestAuthUser();
        authBloc.emit(AuthAuthenticated(user));

        // Act & Assert
        expect(authBloc.currentUser, equals(user));
      });

      test('currentUser should return null when state is not AuthAuthenticated',
          () {
        // Arrange
        authBloc.emit(AuthUnauthenticated());

        // Act & Assert
        expect(authBloc.currentUser, isNull);
      });
    });

    group('state transitions', () {
      blocTest<AuthBloc, AuthState>(
        'should handle multiple consecutive login attempts',
        build: () {
          when(() => mockLoginUseCase.call(any(), any()))
              .thenAnswer((_) async => TestFixtures.createSuccessAuthResult());
          return authBloc;
        },
        act: (bloc) {
          bloc.add(AuthLoginRequested(
              TestFixtures.testEmail, TestFixtures.testPassword));
          bloc.add(AuthLoginRequested(
              TestFixtures.testEmail, TestFixtures.testPassword));
        },
        expect: () => [
          AuthLoading(),
          AuthAuthenticated(TestFixtures.createTestAuthUser()),
          AuthLoading(),
          AuthAuthenticated(TestFixtures.createTestAuthUser()),
        ],
        verify: (_) {
          verify(() => mockLoginUseCase.call(
                TestFixtures.testEmail,
                TestFixtures.testPassword,
              )).called(2);
        },
      );

      blocTest<AuthBloc, AuthState>(
        'should handle login followed by logout',
        build: () {
          when(() => mockLoginUseCase.call(any(), any()))
              .thenAnswer((_) async => TestFixtures.createSuccessAuthResult());
          when(() => mockLogoutUseCase.call()).thenAnswer((_) async => {});
          return authBloc;
        },
        act: (bloc) {
          bloc.add(AuthLoginRequested(
              TestFixtures.testEmail, TestFixtures.testPassword));
          bloc.add(AuthLogoutRequested());
        },
        expect: () => [
          AuthLoading(),
          AuthAuthenticated(TestFixtures.createTestAuthUser()),
          AuthLoading(),
          AuthUnauthenticated(),
        ],
        verify: (_) {
          verify(() => mockLoginUseCase.call(
                TestFixtures.testEmail,
                TestFixtures.testPassword,
              )).called(1);
          verify(() => mockLogoutUseCase.call()).called(1);
        },
      );

      blocTest<AuthBloc, AuthState>(
        'should handle 2FA flow from login to verification',
        build: () {
          when(() => mockLoginUseCase.call(any(), any())).thenAnswer(
              (_) async => TestFixtures.createTwoFactorRequiredAuthResult());
          when(() => mockVerifyTwoFactorUseCase.call(any(), any()))
              .thenAnswer((_) async => TestFixtures.createSuccessAuthResult());
          return authBloc;
        },
        act: (bloc) {
          bloc.add(AuthLoginRequested(
              TestFixtures.testEmail, TestFixtures.testPassword));
          bloc.add(AuthTwoFactorSubmitted(
            TestFixtures.testTwoFactorRefCode,
            TestFixtures.testTwoFactorCode,
          ));
        },
        expect: () => [
          AuthLoading(),
          AuthTwoFactorRequired(TestFixtures.testTwoFactorRefCode),
          AuthLoading(),
          AuthAuthenticated(TestFixtures.createTestAuthUser()),
        ],
        verify: (_) {
          verify(() => mockLoginUseCase.call(
                TestFixtures.testEmail,
                TestFixtures.testPassword,
              )).called(1);
          verify(() => mockVerifyTwoFactorUseCase.call(
                TestFixtures.testTwoFactorRefCode,
                TestFixtures.testTwoFactorCode,
              )).called(1);
        },
      );
    });

    group('error handling', () {
      blocTest<AuthBloc, AuthState>(
        'should handle mixed success and failure scenarios',
        build: () {
          when(() => mockLoginUseCase.call(
                  TestFixtures.testEmail, TestFixtures.testPassword))
              .thenAnswer((_) async => TestFixtures.createSuccessAuthResult());
          when(() => mockLoginUseCase.call(
                  TestFixtures.testEmail, 'wrong_password'))
              .thenAnswer((_) async => TestFixtures.createFailureAuthResult(
                    failure: TestFixtures.createInvalidCredentialsFailure(),
                  ));
          return authBloc;
        },
        act: (bloc) {
          bloc.add(AuthLoginRequested(
              TestFixtures.testEmail, TestFixtures.testPassword));
          bloc.add(
              AuthLoginRequested(TestFixtures.testEmail, 'wrong_password'));
        },
        expect: () => [
          AuthLoading(),
          AuthAuthenticated(TestFixtures.createTestAuthUser()),
          AuthLoading(),
          AuthError(TestFixtures.createInvalidCredentialsFailure()),
        ],
      );

      blocTest<AuthBloc, AuthState>(
        'should recover from error state on successful login',
        build: () {
          when(() => mockLoginUseCase.call(
                  TestFixtures.testEmail, 'wrong_password'))
              .thenAnswer((_) async => TestFixtures.createFailureAuthResult(
                    failure: TestFixtures.createInvalidCredentialsFailure(),
                  ));
          when(() => mockLoginUseCase.call(
                  TestFixtures.testEmail, TestFixtures.testPassword))
              .thenAnswer((_) async => TestFixtures.createSuccessAuthResult());
          return authBloc;
        },
        act: (bloc) {
          bloc.add(
              AuthLoginRequested(TestFixtures.testEmail, 'wrong_password'));
          bloc.add(AuthLoginRequested(
              TestFixtures.testEmail, TestFixtures.testPassword));
        },
        expect: () => [
          AuthLoading(),
          AuthError(TestFixtures.createInvalidCredentialsFailure()),
          AuthLoading(),
          AuthAuthenticated(TestFixtures.createTestAuthUser()),
        ],
      );
    });
  });
}
