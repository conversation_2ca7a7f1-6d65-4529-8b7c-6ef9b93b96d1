import 'package:flutter_test/flutter_test.dart';
import 'package:water_metering/bloc/auth/auth_event.dart';

import '../../helpers/test_fixtures.dart';

/// Unit tests for AuthEvent classes
/// 
/// These tests verify the equality, props, and toString implementations
/// of all authentication event classes.
void main() {
  group('AuthEvent', () {
    group('AuthLoginRequested', () {
      test('should support equality comparison', () {
        // Arrange
        final event1 = AuthLoginRequested(TestFixtures.testEmail, TestFixtures.testPassword);
        final event2 = AuthLoginRequested(TestFixtures.testEmail, TestFixtures.testPassword);

        // Assert
        expect(event1, equals(event2));
        expect(event1.hashCode, equals(event2.hashCode));
      });

      test('should not be equal with different email', () {
        // Arrange
        final event1 = AuthLoginRequested(TestFixtures.testEmail, TestFixtures.testPassword);
        final event2 = AuthLoginRequested('<EMAIL>', TestFixtures.testPassword);

        // Assert
        expect(event1, isNot(equals(event2)));
        expect(event1.hashCode, isNot(equals(event2.hashCode)));
      });

      test('should not be equal with different password', () {
        // Arrange
        final event1 = AuthLoginRequested(TestFixtures.testEmail, TestFixtures.testPassword);
        final event2 = AuthLoginRequested(TestFixtures.testEmail, 'different_password');

        // Assert
        expect(event1, isNot(equals(event2)));
        expect(event1.hashCode, isNot(equals(event2.hashCode)));
      });

      test('should have correct props', () {
        // Arrange
        final event = AuthLoginRequested(TestFixtures.testEmail, TestFixtures.testPassword);

        // Assert
        expect(event.props, equals([TestFixtures.testEmail, TestFixtures.testPassword]));
      });

      test('should have correct toString', () {
        // Arrange
        final event = AuthLoginRequested(TestFixtures.testEmail, TestFixtures.testPassword);

        // Assert
        expect(event.toString(), equals('AuthLoginRequested(email: ${TestFixtures.testEmail})'));
      });

      test('should store email and password correctly', () {
        // Arrange & Act
        final event = AuthLoginRequested(TestFixtures.testEmail, TestFixtures.testPassword);

        // Assert
        expect(event.email, equals(TestFixtures.testEmail));
        expect(event.password, equals(TestFixtures.testPassword));
      });
    });

    group('AuthBiometricLoginRequested', () {
      test('should support equality comparison', () {
        // Arrange
        final event1 = AuthBiometricLoginRequested(TestFixtures.testEmail);
        final event2 = AuthBiometricLoginRequested(TestFixtures.testEmail);

        // Assert
        expect(event1, equals(event2));
        expect(event1.hashCode, equals(event2.hashCode));
      });

      test('should not be equal with different email', () {
        // Arrange
        final event1 = AuthBiometricLoginRequested(TestFixtures.testEmail);
        final event2 = AuthBiometricLoginRequested('<EMAIL>');

        // Assert
        expect(event1, isNot(equals(event2)));
        expect(event1.hashCode, isNot(equals(event2.hashCode)));
      });

      test('should have correct props', () {
        // Arrange
        final event = AuthBiometricLoginRequested(TestFixtures.testEmail);

        // Assert
        expect(event.props, equals([TestFixtures.testEmail]));
      });

      test('should have correct toString', () {
        // Arrange
        final event = AuthBiometricLoginRequested(TestFixtures.testEmail);

        // Assert
        expect(event.toString(), equals('AuthBiometricLoginRequested(email: ${TestFixtures.testEmail})'));
      });

      test('should store email correctly', () {
        // Arrange & Act
        final event = AuthBiometricLoginRequested(TestFixtures.testEmail);

        // Assert
        expect(event.email, equals(TestFixtures.testEmail));
      });
    });

    group('AuthTwoFactorSubmitted', () {
      test('should support equality comparison', () {
        // Arrange
        final event1 = AuthTwoFactorSubmitted(
          TestFixtures.testTwoFactorRefCode,
          TestFixtures.testTwoFactorCode,
        );
        final event2 = AuthTwoFactorSubmitted(
          TestFixtures.testTwoFactorRefCode,
          TestFixtures.testTwoFactorCode,
        );

        // Assert
        expect(event1, equals(event2));
        expect(event1.hashCode, equals(event2.hashCode));
      });

      test('should not be equal with different refCode', () {
        // Arrange
        final event1 = AuthTwoFactorSubmitted(
          TestFixtures.testTwoFactorRefCode,
          TestFixtures.testTwoFactorCode,
        );
        final event2 = AuthTwoFactorSubmitted(
          'different_ref_code',
          TestFixtures.testTwoFactorCode,
        );

        // Assert
        expect(event1, isNot(equals(event2)));
        expect(event1.hashCode, isNot(equals(event2.hashCode)));
      });

      test('should not be equal with different code', () {
        // Arrange
        final event1 = AuthTwoFactorSubmitted(
          TestFixtures.testTwoFactorRefCode,
          TestFixtures.testTwoFactorCode,
        );
        final event2 = AuthTwoFactorSubmitted(
          TestFixtures.testTwoFactorRefCode,
          '654321',
        );

        // Assert
        expect(event1, isNot(equals(event2)));
        expect(event1.hashCode, isNot(equals(event2.hashCode)));
      });

      test('should have correct props', () {
        // Arrange
        final event = AuthTwoFactorSubmitted(
          TestFixtures.testTwoFactorRefCode,
          TestFixtures.testTwoFactorCode,
        );

        // Assert
        expect(event.props, equals([
          TestFixtures.testTwoFactorRefCode,
          TestFixtures.testTwoFactorCode,
        ]));
      });

      test('should have correct toString', () {
        // Arrange
        final event = AuthTwoFactorSubmitted(
          TestFixtures.testTwoFactorRefCode,
          TestFixtures.testTwoFactorCode,
        );

        // Assert
        expect(event.toString(), equals(
          'AuthTwoFactorSubmitted(refCode: ${TestFixtures.testTwoFactorRefCode}, code: ${TestFixtures.testTwoFactorCode})'
        ));
      });

      test('should store refCode and code correctly', () {
        // Arrange & Act
        final event = AuthTwoFactorSubmitted(
          TestFixtures.testTwoFactorRefCode,
          TestFixtures.testTwoFactorCode,
        );

        // Assert
        expect(event.refCode, equals(TestFixtures.testTwoFactorRefCode));
        expect(event.code, equals(TestFixtures.testTwoFactorCode));
      });
    });

    group('AuthLogoutRequested', () {
      test('should support equality comparison', () {
        // Arrange
        final event1 = AuthLogoutRequested();
        final event2 = AuthLogoutRequested();

        // Assert
        expect(event1, equals(event2));
        expect(event1.hashCode, equals(event2.hashCode));
      });

      test('should have empty props', () {
        // Arrange
        final event = AuthLogoutRequested();

        // Assert
        expect(event.props, equals([]));
      });

      test('should have correct toString', () {
        // Arrange
        final event = AuthLogoutRequested();

        // Assert
        expect(event.toString(), equals('AuthLogoutRequested()'));
      });
    });

    group('AuthCheckRequested', () {
      test('should support equality comparison', () {
        // Arrange
        final event1 = AuthCheckRequested();
        final event2 = AuthCheckRequested();

        // Assert
        expect(event1, equals(event2));
        expect(event1.hashCode, equals(event2.hashCode));
      });

      test('should have empty props', () {
        // Arrange
        final event = AuthCheckRequested();

        // Assert
        expect(event.props, equals([]));
      });

      test('should have correct toString', () {
        // Arrange
        final event = AuthCheckRequested();

        // Assert
        expect(event.toString(), equals('AuthCheckRequested()'));
      });
    });

    group('AuthGlobalLogoutRequested', () {
      test('should support equality comparison', () {
        // Arrange
        final event1 = AuthGlobalLogoutRequested();
        final event2 = AuthGlobalLogoutRequested();

        // Assert
        expect(event1, equals(event2));
        expect(event1.hashCode, equals(event2.hashCode));
      });

      test('should have empty props', () {
        // Arrange
        final event = AuthGlobalLogoutRequested();

        // Assert
        expect(event.props, equals([]));
      });

      test('should have correct toString', () {
        // Arrange
        final event = AuthGlobalLogoutRequested();

        // Assert
        expect(event.toString(), equals('AuthGlobalLogoutRequested()'));
      });
    });

    group('AuthTokenRefreshRequested', () {
      test('should support equality comparison', () {
        // Arrange
        final event1 = AuthTokenRefreshRequested();
        final event2 = AuthTokenRefreshRequested();

        // Assert
        expect(event1, equals(event2));
        expect(event1.hashCode, equals(event2.hashCode));
      });

      test('should have empty props', () {
        // Arrange
        final event = AuthTokenRefreshRequested();

        // Assert
        expect(event.props, equals([]));
      });

      test('should have correct toString', () {
        // Arrange
        final event = AuthTokenRefreshRequested();

        // Assert
        expect(event.toString(), equals('AuthTokenRefreshRequested()'));
      });
    });

    group('inheritance', () {
      test('all events should extend AuthEvent', () {
        // Arrange & Act
        final loginEvent = AuthLoginRequested(TestFixtures.testEmail, TestFixtures.testPassword);
        final biometricEvent = AuthBiometricLoginRequested(TestFixtures.testEmail);
        final twoFactorEvent = AuthTwoFactorSubmitted(
          TestFixtures.testTwoFactorRefCode,
          TestFixtures.testTwoFactorCode,
        );
        final logoutEvent = AuthLogoutRequested();
        final checkEvent = AuthCheckRequested();
        final globalLogoutEvent = AuthGlobalLogoutRequested();
        final tokenRefreshEvent = AuthTokenRefreshRequested();

        // Assert
        expect(loginEvent, isA<AuthEvent>());
        expect(biometricEvent, isA<AuthEvent>());
        expect(twoFactorEvent, isA<AuthEvent>());
        expect(logoutEvent, isA<AuthEvent>());
        expect(checkEvent, isA<AuthEvent>());
        expect(globalLogoutEvent, isA<AuthEvent>());
        expect(tokenRefreshEvent, isA<AuthEvent>());
      });
    });

    group('edge cases', () {
      test('should handle empty strings in AuthLoginRequested', () {
        // Arrange & Act
        final event = AuthLoginRequested('', '');

        // Assert
        expect(event.email, equals(''));
        expect(event.password, equals(''));
        expect(event.props, equals(['', '']));
      });

      test('should handle empty string in AuthBiometricLoginRequested', () {
        // Arrange & Act
        final event = AuthBiometricLoginRequested('');

        // Assert
        expect(event.email, equals(''));
        expect(event.props, equals(['']));
      });

      test('should handle empty strings in AuthTwoFactorSubmitted', () {
        // Arrange & Act
        final event = AuthTwoFactorSubmitted('', '');

        // Assert
        expect(event.refCode, equals(''));
        expect(event.code, equals(''));
        expect(event.props, equals(['', '']));
      });

      test('should handle special characters in event data', () {
        // Arrange
        const specialEmail = '<EMAIL>';
        const specialPassword = 'P@ssw0rd!@#\$%^&*()';
        const specialRefCode = 'ref-code-with-special-chars-123!@#';
        const specialCode = '123456';

        // Act
        final loginEvent = AuthLoginRequested(specialEmail, specialPassword);
        final biometricEvent = AuthBiometricLoginRequested(specialEmail);
        final twoFactorEvent = AuthTwoFactorSubmitted(specialRefCode, specialCode);

        // Assert
        expect(loginEvent.email, equals(specialEmail));
        expect(loginEvent.password, equals(specialPassword));
        expect(biometricEvent.email, equals(specialEmail));
        expect(twoFactorEvent.refCode, equals(specialRefCode));
        expect(twoFactorEvent.code, equals(specialCode));
      });
    });
  });
}
