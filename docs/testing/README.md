# Authentication Testing Documentation

## Quick Start

### Running All Authentication Tests
```bash
# Execute the test script
./scripts/run_auth_tests.sh
```

### Running Individual Tests
```bash
# Repository implementation tests (52 tests)
flutter test test/data/repositories/auth_repository_impl_test.dart

# Login form widget tests (14 tests)
flutter test test/presentation/widgets/auth/login_form_test.dart

# With coverage
flutter test test/data/repositories/auth_repository_impl_test.dart --coverage
```

## Test Coverage Results

### Current Status ✅
- **Repository Implementation**: 52/52 tests passing (100%)
- **Login Form Widget**: 14/14 tests passing (100%)
- **Overall Coverage**: 30.2% (407 of 1347 lines)

### Authentication Module Coverage
- **AuthRepositoryImpl**: 99% (96/97 lines)
- **LoginForm Widget**: 90% (81/90 lines)
- **CustomTextField**: 99% (69/70 lines)
- **PasswordController**: 71% (30/42 lines)

## Test Architecture

### Clean Architecture Testing
```
Domain Layer (Contracts)
├── Repository interfaces
├── Use cases
└── Entities

Data Layer (Implementation)  
├── Repository implementations ✅ 52 tests
├── Data sources
└── Models

Presentation Layer (UI)
├── BLoCs
├── Widgets ✅ 14 tests
└── Pages
```

### Test Categories

#### 1. Repository Implementation Tests
**File**: `test/data/repositories/auth_repository_impl_test.dart`
- ✅ Login functionality (7 tests)
- ✅ Biometric authentication (7 tests)
- ✅ Logout operations (4 tests)
- ✅ Two-factor verification (2 tests)
- ✅ Token refresh (3 tests)
- ✅ Authentication status (6 tests)
- ✅ User retrieval (8 tests)
- ✅ Session management (15 tests)

#### 2. Widget Tests
**File**: `test/presentation/widgets/auth/login_form_test.dart`
- ✅ Widget rendering (4 tests)
- ✅ Form validation (2 tests)
- ✅ Form submission (4 tests)
- ✅ User interaction (2 tests)
- ✅ Edge cases (2 tests)

## Key Testing Patterns

### 1. Repository Testing with Mocks
```dart
class MockAuthRemoteDataSource extends Mock implements AuthRemoteDataSource {}
class MockAuthLocalDataSource extends Mock implements AuthLocalDataSource {}
class MockBiometricService extends Mock implements BiometricService {}

// Test setup
setUp(() {
  mockRemoteDataSource = MockAuthRemoteDataSource();
  mockLocalDataSource = MockAuthLocalDataSource();
  mockBiometricService = MockBiometricService();
  
  repository = AuthRepositoryImpl(
    remoteDataSource: mockRemoteDataSource,
    localDataSource: mockLocalDataSource,
    biometricService: mockBiometricService,
  );
});
```

### 2. Widget Testing with Providers
```dart
Widget createTestWidget({bool showValidationErrors = false}) {
  return MultiProvider(
    providers: [
      Provider<ThemeNotifier>.value(value: mockThemeNotifier),
    ],
    child: ScreenUtilInit(
      designSize: const Size(375, 812),
      child: MaterialApp(
        home: Scaffold(
          body: LoginForm(showValidationErrors: showValidationErrors),
        ),
      ),
    ),
  );
}
```

### 3. Error Handling Testing
```dart
test('should return failure when login fails', () async {
  // Arrange
  when(() => mockRemoteDataSource.login(any(), any()))
      .thenThrow(const AuthException('Invalid credentials'));

  // Act
  final result = await repository.login('<EMAIL>', 'password');

  // Assert
  expect(result.success, false);
  expect(result.failure, isA<AuthFailure>());
});
```

## Coverage Requirements

### Minimum Coverage Targets
- **Authentication modules**: 80% minimum
- **Critical paths**: 100% required
- **New features**: Must include tests

### Current Achievement
- ✅ Repository implementation: 99% coverage
- ✅ Login form widget: 90% coverage
- ⚠️ Overall project: 30.2% (below target)

## Troubleshooting

### Common Issues

#### 1. Provider Not Found Error
```
ProviderNotFoundException: No provider found for ThemeNotifier
```
**Solution**: Add MockThemeNotifier to test widget setup

#### 2. Multiple Widget Finder Error
```
Bad state: Too many elements
```
**Solution**: Use specific finders instead of type-based finders
```dart
// Instead of: find.byType(CustomTextField)
// Use: find.widgetWithText(CustomTextField, 'Enter Email')
```

#### 3. Import Errors
```
Error: Not found: 'package:nudron_flutter_water_metering/...'
```
**Solution**: Use correct package name `water_metering`

### Debug Tips
- Use `debugDumpApp()` to inspect widget tree
- Add print statements for test debugging
- Verify mock setup with `verify()` calls
- Check async operations with proper `await` and `pump()`

## CI/CD Integration

### Pre-commit Hooks
```bash
# Add to .git/hooks/pre-commit
#!/bin/bash
./scripts/run_auth_tests.sh
```

### GitHub Actions Example
```yaml
- name: Run Authentication Tests
  run: |
    flutter test test/data/repositories/auth_repository_impl_test.dart
    flutter test test/presentation/widgets/auth/login_form_test.dart
    
- name: Generate Coverage
  run: |
    flutter test --coverage
    genhtml coverage/lcov.info -o coverage/html
```

## Next Steps

### Pending Test Implementation
1. **Domain layer tests** - Repository interfaces and use cases
2. **BLoC tests** - AuthBloc state management
3. **Integration tests** - End-to-end authentication flows
4. **Widget tests** - AuthButton and BiometricButton components

### Coverage Improvement
1. Add missing service implementations
2. Test error handling edge cases
3. Add integration test scenarios
4. Improve overall project coverage to 80%

## Resources

- [Flutter Testing Documentation](https://docs.flutter.dev/testing)
- [BLoC Testing Guide](https://bloclibrary.dev/#/testing)
- [Mocktail Package](https://pub.dev/packages/mocktail)
- [Authentication Testing Guide](./authentication_testing_guide.md)
