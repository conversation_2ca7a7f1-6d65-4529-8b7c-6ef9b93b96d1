# Authentication Testing Guide

## Overview

This document provides comprehensive guidance for testing the authentication system in the Water Metering Flutter application. The authentication system follows clean architecture patterns with domain, data, and presentation layers.

## Test Structure

### Test Organization
```
test/
├── domain/
│   ├── repositories/
│   │   └── auth_repository_test.dart
│   └── usecases/
│       ├── login_usecase_test.dart
│       ├── logout_usecase_test.dart
│       └── verify_two_factor_usecase_test.dart
├── data/
│   └── repositories/
│       └── auth_repository_impl_test.dart
├── presentation/
│   └── widgets/
│       └── auth/
│           ├── login_form_test.dart
│           ├── auth_button_test.dart
│           └── biometric_button_test.dart
└── helpers/
    └── test_helper.dart
```

## Test Categories

### 1. Domain Layer Tests

#### Repository Interface Tests (`auth_repository_test.dart`)
- **Purpose**: Test the abstract repository contract
- **Coverage**: All repository method signatures and return types
- **Pattern**: Interface verification without implementation details

#### Use Case Tests
- **Login Use Case**: Tests business logic for user authentication
- **Logout Use Case**: Tests session termination logic
- **Two-Factor Use Case**: Tests 2FA verification workflow

### 2. Data Layer Tests

#### Repository Implementation Tests (`auth_repository_impl_test.dart`)
- **Status**: ✅ **52 tests passing** (100% success rate)
- **Coverage**: Complete implementation testing with mocked dependencies
- **Key Features**:
  - Login with email/password
  - Biometric authentication
  - Token refresh logic
  - Session management
  - Error handling
  - Two-factor authentication

**Test Results Summary:**
```
✅ Login functionality: 7 tests
✅ Biometric login: 7 tests  
✅ Logout operations: 4 tests
✅ Two-factor verification: 2 tests
✅ Token refresh: 3 tests
✅ Authentication status: 6 tests
✅ User retrieval: 8 tests
✅ Session management: 15 tests
```

### 3. Presentation Layer Tests

#### Widget Tests (`login_form_test.dart`)
- **Status**: ✅ **14 tests passing** (100% success rate)
- **Coverage**: Complete UI component testing
- **Key Features**:
  - Widget rendering
  - Form validation
  - User interactions
  - Error handling
  - Edge cases

**Test Categories:**
```
✅ Widget Rendering: 4 tests
✅ Form Validation: 2 tests
✅ Form Submission: 4 tests
✅ User Interaction: 2 tests
✅ Edge Cases: 2 tests
```

## Running Tests

### Individual Test Files
```bash
# Run repository implementation tests
flutter test test/data/repositories/auth_repository_impl_test.dart

# Run login form widget tests
flutter test test/presentation/widgets/auth/login_form_test.dart

# Run with coverage
flutter test test/data/repositories/auth_repository_impl_test.dart --coverage
```

### Test Coverage
```bash
# Generate coverage report
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
```

**Current Coverage Results:**
- **Overall Coverage**: 30.2% (407 of 1347 lines)
- **Auth Repository Implementation**: 99% (96 of 97 lines)
- **Login Form Widget**: 90% (81 of 90 lines)
- **Custom Text Field**: 99% (69 of 70 lines)
- **Password Controller**: 71% (30 of 42 lines)

## Test Patterns and Best Practices

### 1. Mocking Strategy
- Use `mocktail` for manual mock generation
- Mock external dependencies (APIs, storage, services)
- Verify method calls and interactions

### 2. Widget Testing Patterns
```dart
// Provider setup for theme-dependent widgets
Widget createTestWidget({bool showValidationErrors = false}) {
  return MultiProvider(
    providers: [
      Provider<ThemeNotifier>.value(value: mockThemeNotifier),
    ],
    child: ScreenUtilInit(
      designSize: const Size(375, 812),
      child: MaterialApp(
        home: Scaffold(
          body: LoginForm(
            showValidationErrors: showValidationErrors,
          ),
        ),
      ),
    ),
  );
}
```

### 3. Error Handling Tests
- Test both success and failure scenarios
- Verify proper error mapping from exceptions to failures
- Test user-friendly error messages

### 4. State Management Testing
- Test BLoC state transitions
- Verify event handling
- Test side effects (navigation, storage)

## Known Issues and Solutions

### 1. Multiple Widget Finder Issue
**Problem**: Multiple `CustomTextField` widgets causing "Too many elements" errors
**Solution**: Use hint text-based finders instead of type-based finders
```dart
Finder findEmailField(String emailHint) {
  return find.widgetWithText(CustomTextField, emailHint);
}
```

### 2. ThemeNotifier Provider Issue
**Problem**: `ProviderNotFoundException` in widget tests
**Solution**: Provide `MockThemeNotifier` with complete `CustomThemeData`
```dart
class MockThemeNotifier extends Mock implements ThemeNotifier {
  @override
  CustomThemeData get customTheme => CustomThemeData(
    // All 30+ required theme properties
  );
}
```

## Test Maintenance

### Adding New Tests
1. Follow the established directory structure
2. Use consistent naming conventions
3. Include both positive and negative test cases
4. Mock external dependencies appropriately

### Updating Existing Tests
1. Maintain backward compatibility
2. Update mocks when interfaces change
3. Verify coverage doesn't decrease
4. Update documentation

## Continuous Integration

### Test Execution
- All tests should pass before merging
- Coverage reports should be generated
- Failed tests should block deployment

### Coverage Requirements
- Minimum 80% coverage for authentication modules
- Critical paths must have 100% coverage
- New features require corresponding tests

## Troubleshooting

### Common Test Failures
1. **Import errors**: Verify package names and file paths
2. **Mock setup**: Ensure all required methods are mocked
3. **Widget provider issues**: Check provider setup in test widgets
4. **Async operations**: Use proper `await` and `pump()` calls

### Debug Tips
- Use `debugDumpApp()` to inspect widget tree
- Add print statements for debugging test flow
- Verify mock call counts with `verify()` and `verifyNever()`
- Check test isolation with proper setup/teardown
