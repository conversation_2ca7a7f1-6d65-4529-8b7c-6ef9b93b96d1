# Authentication System Documentation

## Overview

The Nudron Flutter Water Metering application implements a comprehensive authentication system built using **Clean Architecture** principles with **BLoC state management**. The system supports multiple authentication methods including email/password, biometric authentication, and two-factor authentication, with robust state persistence and error handling.

## Key Features

- 🔐 **Multiple Authentication Methods**
  - Email/password login
  - Biometric authentication (Face ID, Touch ID, Fingerprint)
  - Two-factor authentication (SMS and app-based)

- 🏗️ **Clean Architecture**
  - Domain, Data, and Presentation layer separation
  - Repository pattern with abstractions
  - Use case-driven business logic
  - Service abstractions for cross-cutting concerns

- 🔄 **State Management**
  - BLoC pattern for authentication state
  - Persistent authentication state (7-day expiry)
  - Automatic state restoration on app startup

- 🛡️ **Security & Error Handling**
  - Secure token storage with FlutterSecureStorage
  - Comprehensive error handling with typed failures
  - Automatic token refresh
  - Session management with timeout handling

## Architecture Overview

### Layer Structure

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   AuthBloc      │  │  Login Pages    │  │ Auth Widgets │ │
│  │   (State Mgmt)  │  │   (UI Views)    │  │ (Components) │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                     Domain Layer                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Use Cases     │  │  Repositories   │  │   Services   │ │
│  │   (Business     │  │  (Abstractions) │  │ (Abstractions│ │
│  │    Logic)       │  │                 │  │              │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Entities      │  │   Failures      │  │   Results    │ │
│  │  (Core Models)  │  │ (Error Types)   │  │ (Outcomes)   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      Data Layer                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Repository Impl │  │  Data Sources   │  │   Services   │ │
│  │ (Concrete Impl) │  │ (Remote/Local)  │  │ (Concrete    │ │
│  │                 │  │                 │  │  Impl)       │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Data Models   │  │  External APIs  │  │   Storage    │ │
│  │ (JSON Mapping)  │  │ (HTTP Clients)  │  │ (Secure)     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### Core Components

#### 1. AuthBloc (State Management)
- **Location**: `lib/bloc/auth/auth_bloc.dart`
- **Purpose**: Centralized authentication state management
- **Events**: Login, logout, biometric auth, 2FA verification, state restoration
- **States**: Initial, loading, authenticated, unauthenticated, 2FA required, error

#### 2. AuthStateService (Persistence)
- **Location**: `lib/core/services/auth_state_service.dart`
- **Purpose**: Persistent authentication state with 7-day expiry
- **Features**: Secure storage, state validation, automatic cleanup

#### 3. Repository Pattern
- **Interface**: `lib/domain/repositories/auth_repository.dart`
- **Implementation**: `lib/data/repositories/auth_repository_impl.dart`
- **Purpose**: Abstraction layer for authentication operations

#### 4. Use Cases (Business Logic)
- **LoginUseCase**: Email/password authentication
- **BiometricLoginUseCase**: Biometric authentication
- **LogoutUseCase**: Session termination
- **VerifyTwoFactorUseCase**: 2FA verification

#### 5. Service Abstractions
- **BiometricService**: Biometric authentication operations
- **TwoFactorService**: 2FA code sending and verification
- **TokenService**: JWT token management and validation
- **SessionService**: Session lifecycle management

## Authentication Methods

### 1. Email/Password Authentication
```dart
// Trigger login via AuthBloc
authBloc.add(AuthLoginRequested(email, password));

// Handle states
BlocListener<AuthBloc, AuthState>(
  listener: (context, state) {
    if (state is AuthAuthenticated) {
      // Navigate to dashboard
    } else if (state is AuthTwoFactorRequired) {
      // Show 2FA input
    } else if (state is AuthError) {
      // Show error message
    }
  },
)
```

### 2. Biometric Authentication
```dart
// Trigger biometric login
authBloc.add(AuthBiometricLoginRequested(email));

// Check biometric availability
final isAvailable = await biometricService.isAvailable();
final isEnrolled = await biometricService.isEnrolled();
```

### 3. Two-Factor Authentication
```dart
// After initial login returns 2FA required
authBloc.add(AuthVerifyTwoFactorRequested(refCode, code));

// Send 2FA code
final result = await twoFactorService.sendCode(refCode: refCode);
```

## State Persistence

The authentication system maintains user sessions across app restarts using `AuthStateService`:

- **Storage**: FlutterSecureStorage for secure persistence
- **Expiry**: 7-day automatic expiration
- **Validation**: State version checking and corruption handling
- **Restoration**: Automatic state restoration on app startup

### State Restoration Flow
1. App starts → `AuthStateRestoreRequested` event
2. `AuthStateService.loadAuthState()` checks for valid persisted state
3. If valid and not expired → `AuthAuthenticated` state
4. If invalid/expired → `AuthUnauthenticated` state

## Error Handling

### Failure Types
The system uses typed failures for robust error handling:

- **NetworkFailure**: Connectivity issues
- **ServerFailure**: Server-side errors
- **InvalidCredentialsFailure**: Wrong email/password
- **AccountNotVerifiedFailure**: Unverified email/phone
- **BiometricNotAvailableFailure**: Biometric not supported
- **TwoFactorRequiredFailure**: 2FA verification needed

### Error Mapping
```dart
// Exceptions are mapped to domain failures
final failure = ErrorMapper.mapExceptionToFailure(exception);
emit(AuthError(failure));
```

## Dependency Injection

The system uses **GetIt** for dependency injection with proper layer separation:

```dart
// Domain layer (abstractions)
sl.registerLazySingleton<AuthRepository>(() => AuthRepositoryImpl(...));
sl.registerLazySingleton<BiometricService>(() => BiometricServiceImpl(...));

// Use cases
sl.registerFactory(() => LoginUseCase(sl()));
sl.registerFactory(() => BiometricLoginUseCase(sl()));

// BLoCs
sl.registerFactory(() => AuthBloc(sl(), sl(), sl(), sl(), sl(), sl()));
```

## Security Considerations

- **Token Storage**: Secure storage using FlutterSecureStorage
- **Token Refresh**: Automatic refresh before expiration
- **Session Timeout**: 7-day maximum session duration
- **Biometric Security**: Platform-native biometric APIs
- **Input Validation**: Email format and password strength validation
- **Error Sanitization**: User-friendly error messages without technical details

## Getting Started

### 1. Setup Dependencies
Ensure the following dependencies are configured in your `pubspec.yaml`:
```yaml
dependencies:
  flutter_bloc: ^8.1.3
  get_it: ^7.6.4
  flutter_secure_storage: ^9.0.0
  local_auth: ^2.1.6
  equatable: ^2.0.5
```

### 2. Initialize Dependency Injection
```dart
// In main.dart
await initializeDependencies();
```

### 3. Wrap App with BlocProvider
```dart
BlocProvider<AuthBloc>(
  create: (context) => sl<AuthBloc>()..add(AuthStateRestoreRequested()),
  child: MyApp(),
)
```

### 4. Handle Authentication State
```dart
BlocBuilder<AuthBloc, AuthState>(
  builder: (context, state) {
    if (state is AuthAuthenticated) {
      return DashboardPage();
    } else {
      return LoginPage();
    }
  },
)
```

## API Reference

### Core Entities

#### AuthUser
Domain entity representing an authenticated user:

```dart
class AuthUser extends Equatable {
  final String id;              // Unique user identifier
  final String name;            // User's full name
  final String email;           // User's email address
  final bool emailVerified;     // Email verification status
  final String phone;           // User's phone number
  final bool phoneVerified;     // Phone verification status
  final String? lastPassChange; // Last password change timestamp
  final int? lastUpdate;        // Last profile update timestamp
  final String? multiFactor;    // MFA status ('0', 'app', 'sms')

  // Business methods
  bool get hasTwoFactorEnabled => multiFactor == 'app' || multiFactor == 'sms';
  bool get isFullyVerified => emailVerified && phoneVerified;
}
```

#### AuthResult
Result wrapper for authentication operations:

```dart
class AuthResult extends Equatable {
  final bool success;                    // Operation success status
  final AuthUser? user;                  // Authenticated user (if successful)
  final String? twoFactorRefCode;        // 2FA reference code (if required)
  final AuthFailure? failure;           // Failure details (if failed)
  final bool requiresTwoFactor;         // Whether 2FA is required
  final String? accessToken;            // Access token (if successful)
  final String? refreshToken;           // Refresh token (if successful)

  // Factory constructors
  factory AuthResult.success({required AuthUser user, String? accessToken, String? refreshToken});
  factory AuthResult.twoFactorRequired({required String twoFactorRefCode});
  factory AuthResult.failure({String? error, AuthFailure? failure});
  factory AuthResult.withFailure({required AuthFailure failure});

  // Utility methods
  bool get hasTokens => accessToken != null && refreshToken != null;
  bool get isComplete => success && hasTokens && user != null;
  bool get isFailure => !success && !requiresTwoFactor;
}
```

#### AuthStateData
Persistent authentication state data:

```dart
class AuthStateData {
  final bool isAuthenticated;           // Authentication status
  final AuthUser? user;                 // User data (if authenticated)
  final DateTime? lastAuthTime;         // Last authentication timestamp
  final String? sessionId;              // Session identifier
  final Map<String, dynamic>? metadata; // Additional session metadata

  // Factory constructors
  factory AuthStateData.authenticated({required AuthUser user, String? sessionId, Map<String, dynamic>? metadata});
  factory AuthStateData.unauthenticated();

  // JSON serialization
  factory AuthStateData.fromJson(Map<String, dynamic> json);
  Map<String, dynamic> toJson();
}
```

### Repository Interface

#### AuthRepository
Main authentication repository contract:

```dart
abstract class AuthRepository {
  /// Authenticate user with email and password
  Future<AuthResult> login(String email, String password);

  /// Authenticate user with biometric verification
  Future<AuthResult> loginWithBiometric(String email);

  /// Verify two-factor authentication code
  Future<AuthResult> verifyTwoFactor(String refCode, String code);

  /// Logout current user session
  Future<void> logout();

  /// Logout from all devices (global logout)
  Future<void> globalLogout();

  /// Refresh access token using refresh token
  Future<String> refreshToken();

  /// Check if user is currently authenticated
  Future<bool> isAuthenticated();

  /// Get current authenticated user
  Future<AuthUser?> getCurrentUser();
}
```

### Use Cases

#### LoginUseCase
Handles email/password authentication:

```dart
class LoginUseCase {
  final AuthRepository _authRepository;

  const LoginUseCase(this._authRepository);

  /// Execute login with email and password
  ///
  /// Validates input parameters and delegates to repository
  /// Returns AuthResult with success/failure/2FA required status
  Future<AuthResult> call(String email, String password) async {
    // Input validation
    final validationResult = _validateLoginParameters(email, password);
    if (validationResult != null) return validationResult;

    // Normalize and authenticate
    final normalizedEmail = email.trim().toLowerCase();
    return await _authRepository.login(normalizedEmail, password.trim());
  }
}
```

#### BiometricLoginUseCase
Handles biometric authentication:

```dart
class BiometricLoginUseCase {
  final AuthRepository _authRepository;

  const BiometricLoginUseCase(this._authRepository);

  /// Execute biometric login
  ///
  /// Validates email and delegates to repository for biometric auth
  /// Requires prior biometric setup and stored credentials
  Future<AuthResult> call(String email) async {
    final validationResult = _validateEmail(email);
    if (validationResult != null) return validationResult;

    final normalizedEmail = email.trim().toLowerCase();
    return await _authRepository.loginWithBiometric(normalizedEmail);
  }
}
```

#### LogoutUseCase
Handles user logout:

```dart
class LogoutUseCase {
  final AuthRepository _authRepository;

  const LogoutUseCase(this._authRepository);

  /// Execute logout operation
  ///
  /// Clears local session and notifies server
  Future<void> call() async {
    await _authRepository.logout();
  }
}
```

#### VerifyTwoFactorUseCase
Handles 2FA verification:

```dart
class VerifyTwoFactorUseCase {
  final AuthRepository _authRepository;

  const VerifyTwoFactorUseCase(this._authRepository);

  /// Verify two-factor authentication code
  ///
  /// Validates 2FA code format and delegates to repository
  Future<AuthResult> call(String refCode, String code) async {
    final validationResult = _validateTwoFactorParameters(refCode, code);
    if (validationResult != null) return validationResult;

    return await _authRepository.verifyTwoFactor(refCode.trim(), code.trim());
  }
}
```

### Service Abstractions

#### AuthStateService
Manages persistent authentication state:

```dart
abstract class AuthStateService {
  /// Save authentication state to persistent storage
  Future<AuthStateResult> saveAuthState(AuthStateData stateData);

  /// Load authentication state from persistent storage
  /// Returns null if no valid state exists or state is expired
  Future<AuthStateData?> loadAuthState();

  /// Clear all authentication state from storage
  Future<void> clearAuthState();

  /// Check if persisted state exists
  Future<bool> hasPersistedState();

  /// Validate persisted state integrity
  Future<bool> validatePersistedState();

  /// Get last authentication timestamp
  Future<DateTime?> getLastAuthTime();

  /// Update session metadata
  Future<AuthStateResult> updateSessionMetadata(Map<String, dynamic> metadata);

  /// Check if service is ready for operations
  bool isServiceReady();
}
```

#### BiometricService
Manages biometric authentication:

```dart
abstract class BiometricService {
  /// Check if biometric authentication is available on device
  Future<bool> isAvailable();

  /// Check if biometric credentials are enrolled
  Future<bool> isEnrolled();

  /// Authenticate user using biometric verification
  /// Returns BiometricAuthResult with success/failure status
  Future<BiometricAuthResult> authenticate({required String reason});

  /// Get available biometric types on device
  Future<List<BiometricType>> getAvailableBiometrics();
}
```

#### TwoFactorService
Manages two-factor authentication:

```dart
abstract class TwoFactorService {
  /// Send 2FA code to user via configured method
  Future<TwoFactorSendResult> sendCode({required String refCode, TwoFactorMethod? method});

  /// Verify 2FA code
  Future<TwoFactorVerifyResult> verifyCode({required String refCode, required String code});

  /// Check if 2FA is enabled for user
  Future<bool> isTwoFactorEnabled();

  /// Get user's 2FA method preference
  Future<TwoFactorMethod?> getTwoFactorMethod();

  /// Enable 2FA for user account
  Future<TwoFactorSetupResult> enableTwoFactor({required TwoFactorMethod method});

  /// Disable 2FA for user account
  Future<TwoFactorResult> disableTwoFactor();
}
```

#### TokenService
Manages JWT tokens:

```dart
abstract class TokenService {
  /// Validate JWT token and return validation result
  Future<TokenValidationResult> validateToken(String token);

  /// Check if token is expired
  bool isTokenExpired(String token);

  /// Check if token is expiring soon (within threshold)
  bool isTokenExpiring(String token, {Duration threshold = const Duration(minutes: 5)});

  /// Get token expiry time
  DateTime getTokenExpiryTime(String token);

  /// Parse token payload
  Map<String, dynamic> parseTokenPayload(String token);

  /// Refresh access token using refresh token
  Future<String> refreshAccessToken();

  /// Get valid access token (refresh if needed)
  Future<String> getValidAccessToken();
}
```

### BLoC Events and States

#### AuthEvent Hierarchy
```dart
abstract class AuthEvent extends Equatable {}

class AuthLoginRequested extends AuthEvent {
  final String email, password;
  AuthLoginRequested(this.email, this.password);
}

class AuthBiometricLoginRequested extends AuthEvent {
  final String email;
  AuthBiometricLoginRequested(this.email);
}

class AuthVerifyTwoFactorRequested extends AuthEvent {
  final String refCode, code;
  AuthVerifyTwoFactorRequested(this.refCode, this.code);
}

class AuthLogoutRequested extends AuthEvent {}
class AuthGlobalLogoutRequested extends AuthEvent {}
class AuthCheckRequested extends AuthEvent {}
class AuthTokenRefreshRequested extends AuthEvent {}
class AuthStateRestoreRequested extends AuthEvent {}
```

#### AuthState Hierarchy
```dart
abstract class AuthState extends Equatable {}

class AuthInitial extends AuthState {}

class AuthLoading extends AuthState {}

class AuthAuthenticated extends AuthState {
  final AuthUser user;
  AuthAuthenticated(this.user);
}

class AuthUnauthenticated extends AuthState {}

class AuthTwoFactorRequired extends AuthState {
  final String refCode;
  AuthTwoFactorRequired(this.refCode);
}

class AuthError extends AuthState {
  final AuthFailure failure;
  AuthError(this.failure);
}
```

### Error Types

#### AuthFailure Hierarchy
```dart
abstract class AuthFailure extends Equatable {
  final String message;    // User-friendly error message
  final String code;       // Error code for programmatic handling
  final Map<String, dynamic>? context; // Additional error context
}

// Network-related failures
class NetworkFailure extends AuthFailure {}
class ServerFailure extends AuthFailure {}

// Authentication failures
class InvalidCredentialsFailure extends AuthFailure {}
class AccountNotVerifiedFailure extends AuthFailure {
  final String verificationType; // 'email' or 'phone'
}

// Biometric failures
class BiometricNotAvailableFailure extends AuthFailure {}
class BiometricNotEnrolledFailure extends AuthFailure {}
class BiometricAuthenticationFailure extends AuthFailure {}
class BiometricCancelledFailure extends AuthFailure {}

// Two-factor failures
class TwoFactorRequiredFailure extends AuthFailure {}
class InvalidTwoFactorCodeFailure extends AuthFailure {}
class TwoFactorCodeExpiredFailure extends AuthFailure {}

// Token failures
class TokenExpiredFailure extends AuthFailure {}
class TokenRefreshFailure extends AuthFailure {}

// State persistence failures
class StateStorageFailure extends AuthFailure {}
class StateValidationFailure extends AuthFailure {}
```

### Usage Examples

#### Basic Authentication Flow
```dart
// In your widget
class LoginPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthAuthenticated) {
          Navigator.pushReplacementNamed(context, '/dashboard');
        } else if (state is AuthTwoFactorRequired) {
          Navigator.pushNamed(context, '/two-factor', arguments: state.refCode);
        } else if (state is AuthError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.failure.message)),
          );
        }
      },
      child: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, state) {
          if (state is AuthLoading) {
            return Center(child: CircularProgressIndicator());
          }

          return LoginForm(
            onLogin: (email, password) {
              context.read<AuthBloc>().add(
                AuthLoginRequested(email, password),
              );
            },
            onBiometricLogin: (email) {
              context.read<AuthBloc>().add(
                AuthBiometricLoginRequested(email),
              );
            },
          );
        },
      ),
    );
  }
}
```

#### Dependency Injection Setup
```dart
// In injection_container.dart
Future<void> initializeDependencies() async {
  // External dependencies
  sl.registerLazySingleton(() => const FlutterSecureStorage());
  sl.registerLazySingleton(() => http.Client());
  sl.registerLazySingleton(() => LocalAuthentication());

  // Core services
  sl.registerLazySingleton<SecureStorageService>(
    () => SecureStorageServiceImpl(sl()),
  );
  sl.registerLazySingleton<AuthStateService>(
    () => AuthStateServiceImpl(sl()),
  );
  sl.registerLazySingleton<TokenService>(
    () => TokenServiceImpl(sl()),
  );
  sl.registerLazySingleton<BiometricService>(
    () => BiometricServiceImpl(sl()),
  );
  sl.registerLazySingleton<TwoFactorService>(
    () => TwoFactorServiceImpl(sl()),
  );

  // Data sources
  sl.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(sl()),
  );
  sl.registerLazySingleton<AuthLocalDataSource>(
    () => AuthLocalDataSourceImpl(sl()),
  );

  // Repository
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(sl(), sl()),
  );

  // Use cases
  sl.registerLazySingleton(() => LoginUseCase(sl()));
  sl.registerLazySingleton(() => BiometricLoginUseCase(sl()));
  sl.registerLazySingleton(() => LogoutUseCase(sl()));
  sl.registerLazySingleton(() => VerifyTwoFactorUseCase(sl()));

  // BLoC
  sl.registerFactory(() => AuthBloc(sl(), sl(), sl(), sl(), sl(), sl()));
}
```

## Related Documentation

- [Architecture Details](./architecture.md) - Detailed architectural patterns and design decisions
- [Authentication Flows](./flows.md) - Visual flow diagrams for all authentication scenarios
- [Testing Guide](../testing/authentication_testing_guide.md) - Comprehensive testing strategies

## Troubleshooting

### Common Issues

1. **State not persisting**: Check FlutterSecureStorage permissions
2. **Biometric not working**: Verify device enrollment and app permissions
3. **Token refresh failing**: Check network connectivity and server status
4. **2FA codes not received**: Verify phone number and SMS service status

### Debug Mode
Enable debug logging to troubleshoot authentication issues:
```dart
// Set in main.dart for debug builds
if (kDebugMode) {
  developer.log('Authentication debug mode enabled');
}
```
