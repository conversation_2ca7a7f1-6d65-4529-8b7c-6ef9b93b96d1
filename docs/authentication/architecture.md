# Authentication Architecture

## Clean Architecture Implementation

The authentication system follows **Clean Architecture** principles with clear separation of concerns across three main layers:

### Domain Layer (Business Logic)
The domain layer contains the core business logic and is independent of external frameworks.

#### Entities
Core business objects that encapsulate enterprise-wide business rules:

```dart
// lib/domain/entities/auth_user.dart
class AuthUser extends Equatable {
  final String id;
  final String name;
  final String email;
  final bool emailVerified;
  final String phone;
  final bool phoneVerified;
  final String? multiFactor;
  
  // Business methods
  bool get hasTwoFactorEnabled => multiFactor == 'app' || multiFactor == 'sms';
  bool get isFullyVerified => emailVerified && phoneVerified;
}
```

#### Repository Abstractions
Define contracts for data access without implementation details:

```dart
// lib/domain/repositories/auth_repository.dart
abstract class AuthRepository {
  Future<AuthResult> login(String email, String password);
  Future<AuthResult> loginWithBiometric(String email);
  Future<AuthResult> verifyTwoFactor(String refCode, String code);
  Future<void> logout();
  Future<void> globalLogout();
  Future<String> refreshToken();
  Future<bool> isAuthenticated();
  Future<AuthUser?> getCurrentUser();
}
```

#### Use Cases
Encapsulate single business operations with clear inputs and outputs:

```dart
// lib/domain/usecases/login_usecase.dart
class LoginUseCase {
  final AuthRepository _authRepository;
  
  Future<AuthResult> call(String email, String password) async {
    // Input validation
    final validationResult = _validateLoginParameters(email, password);
    if (validationResult != null) return validationResult;
    
    // Business logic
    final normalizedEmail = email.trim().toLowerCase();
    return await _authRepository.login(normalizedEmail, password.trim());
  }
}
```

#### Service Abstractions
Define contracts for cross-cutting concerns:

```dart
// lib/domain/services/biometric_service.dart
abstract class BiometricService {
  Future<bool> isAvailable();
  Future<bool> isEnrolled();
  Future<BiometricAuthResult> authenticate({required String reason});
}
```

### Data Layer (Implementation Details)
The data layer implements domain contracts and handles external data sources.

#### Repository Implementation
Concrete implementation of domain repository contracts:

```dart
// lib/data/repositories/auth_repository_impl.dart
class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource remoteDataSource;
  final AuthLocalDataSource localDataSource;
  
  @override
  Future<AuthResult> login(String email, String password) async {
    try {
      // Remote authentication
      final loginResult = await remoteDataSource.login(email, password);
      
      if (loginResult.success) {
        // Save tokens locally
        await localDataSource.saveTokens(
          loginResult.tokens!['access_token']!,
          loginResult.tokens!['refresh_token']!,
        );
        
        // Get and save user info
        final userModel = await remoteDataSource.getUserInfo();
        await localDataSource.saveUser(userModel);
        
        return AuthResult.success(user: userModel.toDomain());
      }
      
      return AuthResult.failure(error: loginResult.error);
    } catch (e) {
      return AuthResult.withFailure(
        failure: ErrorMapper.mapExceptionToFailure(e),
      );
    }
  }
}
```

#### Data Sources
Handle specific data access patterns:

```dart
// Remote data source for API calls
class AuthRemoteDataSource {
  Future<LoginResponse> login(String email, String password);
  Future<AuthUserModel> getUserInfo();
  Future<Map<String, String>> verifyTwoFactor(String refCode, String code);
}

// Local data source for secure storage
class AuthLocalDataSource {
  Future<void> saveTokens(String accessToken, String refreshToken);
  Future<void> saveUser(AuthUserModel user);
  Future<bool> isBiometricAvailable();
  Future<void> saveCredentials(String email, String password);
}
```

### Presentation Layer (UI & State Management)
The presentation layer handles user interface and state management.

#### BLoC Pattern Implementation
Centralized state management using the BLoC pattern:

```dart
// lib/bloc/auth/auth_bloc.dart
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final LoginUseCase _loginUseCase;
  final BiometricLoginUseCase _biometricLoginUseCase;
  final AuthStateService _authStateService;
  
  AuthBloc(...) : super(AuthInitial()) {
    on<AuthLoginRequested>(_onLoginRequested);
    on<AuthBiometricLoginRequested>(_onBiometricLoginRequested);
    on<AuthStateRestoreRequested>(_onStateRestoreRequested);
  }
  
  Future<void> _onLoginRequested(
    AuthLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());
    
    final result = await _loginUseCase.call(event.email, event.password);
    
    if (result.success && result.user != null) {
      emit(AuthAuthenticated(result.user!));
      await _saveAuthState(result.user!);
    } else if (result.requiresTwoFactor) {
      emit(AuthTwoFactorRequired(result.twoFactorRefCode!));
    } else {
      emit(AuthError(result.failure!));
    }
  }
}
```

## State Management Architecture

### Authentication States
The system defines clear authentication states:

```dart
// lib/bloc/auth/auth_state.dart
abstract class AuthState extends Equatable {}

class AuthInitial extends AuthState {}
class AuthLoading extends AuthState {}
class AuthAuthenticated extends AuthState {
  final AuthUser user;
  AuthAuthenticated(this.user);
}
class AuthUnauthenticated extends AuthState {}
class AuthTwoFactorRequired extends AuthState {
  final String refCode;
  AuthTwoFactorRequired(this.refCode);
}
class AuthError extends AuthState {
  final AuthFailure failure;
  AuthError(this.failure);
}
```

### Authentication Events
Events trigger state transitions:

```dart
// lib/bloc/auth/auth_event.dart
abstract class AuthEvent extends Equatable {}

class AuthLoginRequested extends AuthEvent {
  final String email, password;
  AuthLoginRequested(this.email, this.password);
}

class AuthBiometricLoginRequested extends AuthEvent {
  final String email;
  AuthBiometricLoginRequested(this.email);
}

class AuthVerifyTwoFactorRequested extends AuthEvent {
  final String refCode, code;
  AuthVerifyTwoFactorRequested(this.refCode, this.code);
}

class AuthStateRestoreRequested extends AuthEvent {}
class AuthLogoutRequested extends AuthEvent {}
```

## State Persistence Architecture

### AuthStateService Design
The `AuthStateService` provides persistent authentication state:

```dart
// lib/core/services/auth_state_service.dart
abstract class AuthStateService {
  Future<AuthStateResult> saveAuthState(AuthStateData stateData);
  Future<AuthStateData?> loadAuthState();
  Future<void> clearAuthState();
  Future<bool> hasPersistedState();
  Future<bool> validatePersistedState();
  Future<DateTime?> getLastAuthTime();
  bool isServiceReady();
}

class AuthStateData {
  final bool isAuthenticated;
  final AuthUser? user;
  final DateTime? lastAuthTime;
  final String? sessionId;
  final Map<String, dynamic>? metadata;
  
  // 7-day expiry logic
  static const Duration _stateExpiryDuration = Duration(days: 7);
}
```

### Persistence Implementation
Uses FlutterSecureStorage for secure state persistence:

```dart
class AuthStateServiceImpl implements AuthStateService {
  final SecureStorageService _secureStorage;
  
  static const String _authStateKey = 'auth_state_data';
  static const Duration _stateExpiryDuration = Duration(days: 7);
  
  @override
  Future<AuthStateData?> loadAuthState() async {
    // Load and validate state
    final stateString = await _secureStorage.read(_authStateKey);
    if (stateString == null) return null;
    
    final stateData = AuthStateData.fromJson(jsonDecode(stateString));
    
    // Check expiry
    if (_isStateExpired(stateData)) {
      await clearAuthState();
      return null;
    }
    
    return stateData;
  }
  
  bool _isStateExpired(AuthStateData stateData) {
    if (stateData.lastAuthTime == null) return true;
    
    final now = DateTime.now();
    final expiryTime = stateData.lastAuthTime!.add(_stateExpiryDuration);
    return now.isAfter(expiryTime);
  }
}
```

## Error Handling Architecture

### Failure Hierarchy
Structured error handling with typed failures:

```dart
// lib/core/error/auth_failures.dart
abstract class AuthFailure extends Equatable {
  final String message;
  final String code;
  final Map<String, dynamic>? context;
}

class NetworkFailure extends AuthFailure {
  const NetworkFailure({
    String message = 'Network connection failed',
    Map<String, dynamic>? context,
  }) : super(message: message, code: 'NETWORK_ERROR', context: context);
}

class InvalidCredentialsFailure extends AuthFailure {
  const InvalidCredentialsFailure({
    String message = 'Invalid email or password',
    Map<String, dynamic>? context,
  }) : super(message: message, code: 'INVALID_CREDENTIALS', context: context);
}
```

### Error Mapping
Convert exceptions to domain failures:

```dart
// lib/core/error/error_mapper.dart
class ErrorMapper {
  static AuthFailure mapExceptionToFailure(dynamic exception) {
    if (exception is AuthException) {
      return _mapAuthException(exception);
    } else if (exception is NetworkException) {
      return NetworkFailure(message: exception.message);
    } else if (exception is String) {
      return GenericAuthFailure(message: exception);
    } else {
      return GenericAuthFailure(
        message: 'An unexpected error occurred',
        context: {'originalError': exception.toString()},
      );
    }
  }
}
```

## Service Architecture

### Token Management
Centralized token operations:

```dart
// lib/core/services/token_service.dart
abstract class TokenService {
  Future<TokenValidationResult> validateToken(String token);
  Future<String> refreshAccessToken();
  DateTime getTokenExpiryTime(String token);
  Map<String, dynamic> parseTokenPayload(String token);
  Future<String> getValidAccessToken();
}
```

### Session Management
Session lifecycle operations:

```dart
// lib/core/services/session_service.dart
abstract class SessionService {
  Future<SessionResult> startSession(AuthUser user);
  Future<SessionResult> refreshSession();
  Future<void> endSession();
  Future<bool> validateSession();
  Future<Duration?> getSessionTimeRemaining();
  bool isSessionActive();
}
```

## Dependency Injection Architecture

### Container Setup
Organized dependency registration:

```dart
// lib/core/di/injection_container.dart
final sl = GetIt.instance;

Future<void> initializeDependencies() async {
  // External dependencies
  sl.registerLazySingleton(() => const FlutterSecureStorage());
  sl.registerLazySingleton(() => http.Client());
  sl.registerLazySingleton(() => LocalAuthentication());
  
  // Services
  sl.registerLazySingleton<TokenService>(() => TokenServiceImpl(sl()));
  sl.registerLazySingleton<BiometricService>(() => BiometricServiceImpl(sl()));
  sl.registerLazySingleton<AuthStateService>(() => AuthStateServiceImpl(sl()));
  
  // Data sources
  sl.registerLazySingleton<AuthRemoteDataSource>(() => AuthRemoteDataSourceImpl(sl()));
  sl.registerLazySingleton<AuthLocalDataSource>(() => AuthLocalDataSourceImpl(sl()));
  
  // Repositories
  sl.registerLazySingleton<AuthRepository>(() => AuthRepositoryImpl(sl(), sl()));
  
  // Use cases
  sl.registerLazySingleton(() => LoginUseCase(sl()));
  sl.registerLazySingleton(() => BiometricLoginUseCase(sl()));
  sl.registerLazySingleton(() => LogoutUseCase(sl()));
  
  // BLoCs
  sl.registerFactory(() => AuthBloc(sl(), sl(), sl(), sl(), sl(), sl()));
}
```

## Design Patterns

### Repository Pattern
- **Purpose**: Abstract data access logic
- **Benefits**: Testability, flexibility, separation of concerns
- **Implementation**: Interface in domain, concrete implementation in data layer

### Use Case Pattern
- **Purpose**: Encapsulate single business operations
- **Benefits**: Single responsibility, reusability, testability
- **Implementation**: One use case per business operation

### BLoC Pattern
- **Purpose**: Predictable state management
- **Benefits**: Separation of business logic from UI, testability, reactive programming
- **Implementation**: Events trigger business logic, emit states for UI updates

### Service Locator Pattern
- **Purpose**: Centralized dependency management
- **Benefits**: Loose coupling, easy testing, configuration flexibility
- **Implementation**: GetIt service locator with proper registration order
