# Authentication Flow Diagrams

This document provides visual representations of all authentication flows in the Nudron Flutter Water Metering application using Mermaid diagrams.

## 1. Email/Password Login Flow

```mermaid
sequenceDiagram
    participant U as User
    participant UI as Login UI
    participant AB as AuthBloc
    participant UC as LoginUseCase
    participant AR as AuthRepository
    participant R<PERSON> as RemoteDataSource
    participant LDS as LocalDataSource
    participant ASS as AuthStateService

    U->>UI: Enter email/password
    UI->>AB: AuthLoginRequested(email, password)
    AB->>AB: emit(AuthLoading)
    AB->>UC: call(email, password)
    
    UC->>UC: Validate input
    alt Invalid input
        UC-->>AB: AuthResult.failure()
        AB->>AB: emit(AuthError)
        AB-->>UI: AuthError state
        UI-->>U: Show error message
    else Valid input
        UC->>AR: login(email, password)
        AR->>RDS: login(email, password)
        
        alt Login successful
            RDS-->>AR: LoginResponse(success, tokens)
            AR->>LDS: saveTokens(access, refresh)
            AR->>RDS: getUserInfo()
            RDS-->>AR: AuthUserModel
            AR->>LDS: saveUser(userModel)
            AR->>LDS: saveSession(sessionModel)
            AR-->>UC: AuthResult.success(user)
            UC-->>AB: AuthResult.success(user)
            AB->>ASS: saveAuthState(user)
            AB->>AB: emit(AuthAuthenticated(user))
            AB-->>UI: AuthAuthenticated state
            UI-->>U: Navigate to dashboard
            
        else 2FA required
            RDS-->>AR: LoginResponse(2FA required, refCode)
            AR-->>UC: AuthResult.twoFactorRequired(refCode)
            UC-->>AB: AuthResult.twoFactorRequired(refCode)
            AB->>AB: emit(AuthTwoFactorRequired(refCode))
            AB-->>UI: AuthTwoFactorRequired state
            UI-->>U: Show 2FA input
            
        else Login failed
            RDS-->>AR: LoginResponse(error)
            AR-->>UC: AuthResult.failure(error)
            UC-->>AB: AuthResult.failure(error)
            AB->>AB: emit(AuthError(failure))
            AB-->>UI: AuthError state
            UI-->>U: Show error message
        end
    end
```

## 2. Biometric Authentication Flow

```mermaid
sequenceDiagram
    participant U as User
    participant UI as Login UI
    participant AB as AuthBloc
    participant BUC as BiometricLoginUseCase
    participant AR as AuthRepository
    participant LDS as LocalDataSource
    participant BS as BiometricService
    participant RDS as RemoteDataSource

    U->>UI: Tap biometric login
    UI->>AB: AuthBiometricLoginRequested(email)
    AB->>AB: emit(AuthLoading)
    AB->>BUC: call(email)
    
    BUC->>AR: loginWithBiometric(email)
    AR->>LDS: isBiometricEnabled()
    
    alt Biometric not enabled
        LDS-->>AR: false
        AR-->>BUC: AuthResult.failure(BiometricNotEnabled)
        BUC-->>AB: AuthResult.failure()
        AB->>AB: emit(AuthError)
        AB-->>UI: AuthError state
        UI-->>U: Show "Setup biometric first"
        
    else Biometric enabled
        LDS-->>AR: true
        AR->>BS: authenticate(reason: "Login to app")
        
        alt Biometric auth successful
            BS-->>AR: BiometricAuthResult.success()
            AR->>LDS: getStoredCredentials(email)
            LDS-->>AR: credentials
            AR->>RDS: login(email, password)
            RDS-->>AR: LoginResponse(success, tokens)
            AR->>LDS: saveTokens(access, refresh)
            AR->>RDS: getUserInfo()
            RDS-->>AR: AuthUserModel
            AR-->>BUC: AuthResult.success(user)
            BUC-->>AB: AuthResult.success(user)
            AB->>AB: emit(AuthAuthenticated(user))
            AB-->>UI: AuthAuthenticated state
            UI-->>U: Navigate to dashboard
            
        else Biometric auth failed
            BS-->>AR: BiometricAuthResult.failure()
            AR-->>BUC: AuthResult.failure(BiometricFailed)
            BUC-->>AB: AuthResult.failure()
            AB->>AB: emit(AuthError)
            AB-->>UI: AuthError state
            UI-->>U: Show "Biometric failed, use password"
        end
    end
```

## 3. Two-Factor Authentication Flow

```mermaid
sequenceDiagram
    participant U as User
    participant UI as 2FA UI
    participant AB as AuthBloc
    participant VUC as VerifyTwoFactorUseCase
    participant AR as AuthRepository
    participant RDS as RemoteDataSource
    participant LDS as LocalDataSource
    participant TFS as TwoFactorService

    Note over U,TFS: User has already completed initial login and received refCode
    
    U->>UI: Enter 2FA code
    UI->>AB: AuthVerifyTwoFactorRequested(refCode, code)
    AB->>AB: emit(AuthLoading)
    AB->>VUC: call(refCode, code)
    
    VUC->>AR: verifyTwoFactor(refCode, code)
    AR->>RDS: verifyTwoFactor(refCode, code)
    
    alt 2FA verification successful
        RDS-->>AR: tokens{access, refresh}
        AR->>LDS: saveTokens(access, refresh)
        AR->>RDS: getUserInfo()
        RDS-->>AR: AuthUserModel
        AR->>LDS: saveUser(userModel)
        AR->>LDS: saveSession(sessionModel, 2FA=true)
        AR-->>VUC: AuthResult.success(user)
        VUC-->>AB: AuthResult.success(user)
        AB->>AB: emit(AuthAuthenticated(user))
        AB-->>UI: AuthAuthenticated state
        UI-->>U: Navigate to dashboard
        
    else Invalid 2FA code
        RDS-->>AR: error(Invalid code)
        AR-->>VUC: AuthResult.failure(InvalidTwoFactorCode)
        VUC-->>AB: AuthResult.failure()
        AB->>AB: emit(AuthError)
        AB-->>UI: AuthError state
        UI-->>U: Show "Invalid code, try again"
        
    else 2FA code expired
        RDS-->>AR: error(Code expired)
        AR-->>VUC: AuthResult.failure(TwoFactorCodeExpired)
        VUC-->>AB: AuthResult.failure()
        AB->>AB: emit(AuthError)
        AB-->>UI: AuthError state
        UI-->>U: Show "Code expired, request new one"
    end
    
    Note over U,UI: User can request new code
    U->>UI: Tap "Resend code"
    UI->>TFS: sendCode(refCode)
    TFS->>RDS: sendTwoFactorCode(refCode)
    RDS-->>TFS: success/failure
    TFS-->>UI: result
    UI-->>U: Show "Code sent" or error
```

## 4. App Initialization and State Restoration Flow

```mermaid
flowchart TD
    A[App Starts] --> B[Initialize Dependencies]
    B --> C[Create AuthBloc]
    C --> D[Dispatch AuthStateRestoreRequested]
    D --> E[AuthStateService.loadAuthState]
    
    E --> F{State Exists?}
    F -->|No| G[emit AuthUnauthenticated]
    F -->|Yes| H[Validate State Version]
    
    H --> I{Version Valid?}
    I -->|No| J[Clear State] --> G
    I -->|Yes| K[Check State Expiry]
    
    K --> L{State Expired?}
    L -->|Yes| M[Clear Expired State] --> G
    L -->|No| N[Parse AuthStateData]
    
    N --> O{User Data Valid?}
    O -->|No| P[Clear Invalid State] --> G
    O -->|Yes| Q[Validate Tokens]
    
    Q --> R{Tokens Valid?}
    R -->|No| S[Attempt Token Refresh]
    R -->|Yes| T[emit AuthAuthenticated]
    
    S --> U{Refresh Success?}
    U -->|No| V[Clear State] --> G
    U -->|Yes| W[Update Tokens] --> T
    
    G --> X[Show Login Screen]
    T --> Y[Show Dashboard]
    
    style A fill:#e1f5fe
    style X fill:#ffebee
    style Y fill:#e8f5e8
```

## 5. Logout Flow

```mermaid
sequenceDiagram
    participant U as User
    participant UI as Dashboard UI
    participant AB as AuthBloc
    participant LUC as LogoutUseCase
    participant AR as AuthRepository
    participant RDS as RemoteDataSource
    participant LDS as LocalDataSource
    participant ASS as AuthStateService

    U->>UI: Tap logout
    UI->>AB: AuthLogoutRequested()
    AB->>AB: emit(AuthLoading)
    AB->>LUC: call()
    
    LUC->>AR: logout()
    
    par Server logout
        AR->>RDS: logout()
        RDS-->>AR: success/failure
    and Local cleanup
        AR->>LDS: clearTokens()
        AR->>LDS: clearUser()
        AR->>LDS: clearSession()
        AR->>LDS: clearCredentials()
    end
    
    AR-->>LUC: void (success)
    LUC-->>AB: void (success)
    AB->>ASS: clearAuthState()
    AB->>AB: emit(AuthUnauthenticated)
    AB-->>UI: AuthUnauthenticated state
    UI-->>U: Navigate to login screen
```

## 6. Global Logout Flow

```mermaid
sequenceDiagram
    participant U as User
    participant UI as Settings UI
    participant AB as AuthBloc
    participant AR as AuthRepository
    participant RDS as RemoteDataSource
    participant LDS as LocalDataSource
    participant ASS as AuthStateService

    U->>UI: Tap "Logout from all devices"
    UI->>UI: Show confirmation dialog
    U->>UI: Confirm global logout
    UI->>AB: AuthGlobalLogoutRequested()
    AB->>AB: emit(AuthLoading)
    AB->>AR: globalLogout()
    
    AR->>RDS: globalLogout()
    
    alt Global logout successful
        RDS-->>AR: success
        AR->>LDS: clearAllData()
        AR->>ASS: clearAuthState()
        AR-->>AB: success
        AB->>AB: emit(AuthUnauthenticated)
        AB-->>UI: AuthUnauthenticated state
        UI-->>U: Navigate to login + show "Logged out from all devices"
        
    else Global logout failed
        RDS-->>AR: error
        AR-->>AB: failure
        AB->>AB: emit(AuthError)
        AB-->>UI: AuthError state
        UI-->>U: Show error message
    end
```

## 7. Token Refresh Flow

```mermaid
sequenceDiagram
    participant API as API Request
    participant AR as AuthRepository
    participant TS as TokenService
    participant LDS as LocalDataSource
    participant RDS as RemoteDataSource

    API->>AR: makeAuthenticatedRequest()
    AR->>TS: getValidAccessToken()
    TS->>TS: validateToken(currentToken)
    
    alt Token valid
        TS-->>AR: currentToken
        AR->>RDS: makeRequest(token)
        RDS-->>AR: response
        AR-->>API: response
        
    else Token expired/expiring
        TS->>LDS: getRefreshToken()
        LDS-->>TS: refreshToken
        TS->>RDS: refreshAccessToken(refreshToken)
        
        alt Refresh successful
            RDS-->>TS: newTokens{access, refresh}
            TS->>LDS: saveTokens(newTokens)
            TS-->>AR: newAccessToken
            AR->>RDS: makeRequest(newAccessToken)
            RDS-->>AR: response
            AR-->>API: response
            
        else Refresh failed
            RDS-->>TS: error(invalid refresh token)
            TS->>LDS: clearTokens()
            TS-->>AR: TokenRefreshFailure
            AR-->>API: AuthenticationRequired
            Note over API: Trigger re-authentication
        end
    end
```

## 8. Error Handling Flow

```mermaid
flowchart TD
    A[Exception Occurs] --> B[ErrorMapper.mapExceptionToFailure]
    
    B --> C{Exception Type}
    C -->|NetworkException| D[NetworkFailure]
    C -->|ServerException| E[ServerFailure]
    C -->|AuthException| F[InvalidCredentialsFailure]
    C -->|BiometricException| G[BiometricFailure]
    C -->|ValidationException| H[ValidationFailure]
    C -->|Unknown| I[GenericAuthFailure]
    
    D --> J[emit AuthError]
    E --> J
    F --> J
    G --> J
    H --> J
    I --> J
    
    J --> K[UI receives AuthError state]
    K --> L{Failure Type}
    
    L -->|NetworkFailure| M[Show "Check connection"]
    L -->|InvalidCredentialsFailure| N[Show "Wrong email/password"]
    L -->|BiometricFailure| O[Show "Use password instead"]
    L -->|ValidationFailure| P[Show field-specific error]
    L -->|ServerFailure| Q[Show "Server error, try later"]
    L -->|GenericFailure| R[Show "Something went wrong"]
    
    M --> S[User Action Required]
    N --> S
    O --> S
    P --> S
    Q --> S
    R --> S
    
    style A fill:#ffebee
    style J fill:#fff3e0
    style S fill:#e8f5e8
```

## 9. Session Management Flow

```mermaid
stateDiagram-v2
    [*] --> NoSession
    
    NoSession --> Authenticating : Login attempt
    Authenticating --> Active : Login success
    Authenticating --> NoSession : Login failure
    
    Active --> Refreshing : Token near expiry
    Refreshing --> Active : Refresh success
    Refreshing --> Expired : Refresh failure
    
    Active --> Expired : Token expired
    Active --> NoSession : Manual logout
    Active --> NoSession : Global logout
    
    Expired --> Authenticating : Re-authentication
    Expired --> NoSession : Session timeout
    
    state Active {
        [*] --> Valid
        Valid --> NearExpiry : 5 min before expiry
        NearExpiry --> Valid : Token refreshed
    }
    
    state Expired {
        [*] --> GracePeriod
        GracePeriod --> ForceReauth : Grace period ended
    }
```

## Flow Summary

### Key Flow Characteristics

1. **Email/Password Login**: Standard authentication with validation and 2FA support
2. **Biometric Login**: Requires prior setup, uses stored credentials with biometric verification
3. **Two-Factor Authentication**: Secondary verification step with code validation
4. **State Restoration**: Automatic session recovery with 7-day expiry
5. **Logout**: Clean local and server-side session termination
6. **Token Refresh**: Automatic token renewal with fallback to re-authentication
7. **Error Handling**: Comprehensive error mapping with user-friendly messages
8. **Session Management**: Active session monitoring with automatic refresh

### Security Considerations

- All sensitive operations use secure storage
- Tokens are automatically refreshed before expiry
- Biometric authentication requires device-level security
- State persistence has built-in expiry mechanisms
- Error messages don't expose sensitive information
- Global logout invalidates all device sessions
