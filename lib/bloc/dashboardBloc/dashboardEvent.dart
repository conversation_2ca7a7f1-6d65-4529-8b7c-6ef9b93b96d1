import '../../model/filterAndSummaryForProject.dart';

abstract class DashboardEvent {}

class DashboardPageRequested extends DashboardEvent {
  String? nfcData;

  DashboardPageRequested({this.nfcData});
}

class DashboardLoadInitialDataRequested extends DashboardEvent {}

class DashboardChangeScreenRequested extends DashboardEvent {}

class DashboardRefreshSummaryPageRequested extends DashboardEvent {}

class DashboardRefreshDevicesPageRequested extends DashboardEvent {}

class DashboardChangeDashBoardNavRequested extends DashboardEvent {}

class DashboardUserInfoUpdateRequested extends DashboardEvent {}

class DashboardRefreshDashboardRequested extends DashboardEvent {}

class DashboardSwitchBottomNavRequested extends DashboardEvent {
  final int index;

  DashboardSwitchBottomNavRequested(this.index);
}

class DashboardSelectMonthRequested extends DashboardEvent {
  final int month;

  DashboardSelectMonthRequested(this.month);
}

class DashboardUpdateSelectedFiltersRequested extends DashboardEvent {
  final List<String?> filters;
  final FilterAndSummaryForProject? filterData;

  DashboardUpdateSelectedFiltersRequested(this.filters, this.filterData);
}
