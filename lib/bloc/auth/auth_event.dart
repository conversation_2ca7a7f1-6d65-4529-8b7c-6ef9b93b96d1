import 'package:equatable/equatable.dart';

/// Abstract base class for all authentication events
///
/// This class defines the contract for authentication events that can be
/// dispatched to the AuthBloc. All authentication events extend this class
/// and implement Equatable for efficient event comparison.
abstract class AuthEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

/// Event to request standard email/password authentication
///
/// This event is dispatched when the user attempts to log in using
/// their email address and password. The AuthBloc will validate
/// the credentials and emit appropriate states based on the result.
class AuthLoginRequested extends AuthEvent {
  /// User's email address
  final String email;

  /// User's password
  final String password;

  AuthLoginRequested(this.email, this.password);

  @override
  List<Object?> get props => [email, password];

  @override
  String toString() => 'AuthLoginRequested(email: $email)';
}

/// Event to request biometric authentication
///
/// This event is dispatched when the user attempts to log in using
/// biometric authentication (fingerprint, face recognition, etc.).
/// The email is used to identify the user account for biometric login.
class AuthBiometricLoginRequested extends AuthEvent {
  /// User's email address for biometric authentication
  final String email;

  AuthBiometricLoginRequested(this.email);

  @override
  List<Object?> get props => [email];

  @override
  String toString() => 'AuthBiometricLoginRequested(email: $email)';
}

/// Event to submit two-factor authentication code
///
/// This event is dispatched when the user enters their two-factor
/// authentication code after initial login. The refCode is the
/// reference code received from the initial login attempt.
class AuthTwoFactorSubmitted extends AuthEvent {
  /// Reference code from the initial login attempt
  final String refCode;

  /// Two-factor authentication code entered by the user
  final String code;

  AuthTwoFactorSubmitted(this.refCode, this.code);

  @override
  List<Object?> get props => [refCode, code];

  @override
  String toString() => 'AuthTwoFactorSubmitted(refCode: $refCode, code: $code)';
}

/// Event to request user logout
///
/// This event is dispatched when the user wants to log out of the application.
/// The AuthBloc will clear the authentication session and emit the
/// unauthenticated state.
class AuthLogoutRequested extends AuthEvent {
  @override
  String toString() => 'AuthLogoutRequested()';
}

/// Event to check current authentication status
///
/// This event is dispatched to verify if the user is currently authenticated.
/// This is typically used when the app starts to check if there's an existing
/// valid session. The AuthBloc will check stored tokens and session validity.
class AuthCheckRequested extends AuthEvent {
  @override
  String toString() => 'AuthCheckRequested()';
}

/// Event to request global logout
///
/// This event is dispatched when the user wants to log out from all devices.
/// This will invalidate all sessions and tokens across all devices where
/// the user is logged in.
class AuthGlobalLogoutRequested extends AuthEvent {
  @override
  String toString() => 'AuthGlobalLogoutRequested()';
}

/// Event to request token refresh
///
/// This event is dispatched when the access token needs to be refreshed.
/// This is typically handled automatically by the AuthBloc when it detects
/// that the current token is expired or about to expire.
class AuthTokenRefreshRequested extends AuthEvent {
  @override
  String toString() => 'AuthTokenRefreshRequested()';
}

/// Event to request authentication state restoration
///
/// This event is dispatched during app initialization to restore the
/// authentication state from persistent storage. The AuthBloc will use
/// the AuthStateService to load previously saved authentication state
/// and restore the user session if valid.
class AuthStateRestoreRequested extends AuthEvent {
  @override
  String toString() => 'AuthStateRestoreRequested()';
}
