import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../core/error/error_mapper.dart';
import '../../core/services/auth_state_service.dart';
import '../../domain/entities/auth_user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/usecases/login_usecase.dart';
import '../../domain/usecases/logout_usecase.dart';
import '../../domain/usecases/verify_two_factor_usecase.dart';
import 'auth_event.dart';
import 'auth_state.dart';

/// BLoC for managing authentication state and operations
///
/// This BLoC handles all authentication-related business logic including:
/// - Standard email/password login
/// - Biometric authentication
/// - Two-factor authentication
/// - Logout operations
/// - Authentication status checking
/// - Token refresh
///
/// The BLoC uses domain layer use cases to perform authentication operations
/// and maintains the current authentication state of the application.
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final LoginUseCase _loginUseCase;
  final BiometricLoginUseCase _biometricLoginUseCase;
  final LogoutUseCase _logoutUseCase;
  final VerifyTwoFactorUseCase _verifyTwoFactorUseCase;
  final AuthRepository _authRepository;
  final AuthStateService _authStateService;

  AuthBloc({
    required LoginUseCase loginUseCase,
    required BiometricLoginUseCase biometricLoginUseCase,
    required LogoutUseCase logoutUseCase,
    required VerifyTwoFactorUseCase verifyTwoFactorUseCase,
    required AuthRepository authRepository,
    required AuthStateService authStateService,
  })  : _loginUseCase = loginUseCase,
        _biometricLoginUseCase = biometricLoginUseCase,
        _logoutUseCase = logoutUseCase,
        _verifyTwoFactorUseCase = verifyTwoFactorUseCase,
        _authRepository = authRepository,
        _authStateService = authStateService,
        super(AuthInitial()) {
    // Register event handlers
    on<AuthLoginRequested>(_onLoginRequested);
    on<AuthBiometricLoginRequested>(_onBiometricLoginRequested);
    on<AuthTwoFactorSubmitted>(_onTwoFactorSubmitted);
    on<AuthLogoutRequested>(_onLogoutRequested);
    on<AuthGlobalLogoutRequested>(_onGlobalLogoutRequested);
    on<AuthCheckRequested>(_onAuthCheckRequested);
    on<AuthTokenRefreshRequested>(_onTokenRefreshRequested);
    on<AuthStateRestoreRequested>(_onAuthStateRestoreRequested);
  }

  /// Handles standard email/password login requests
  Future<void> _onLoginRequested(
    AuthLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      final result = await _loginUseCase.call(event.email, event.password);

      if (result.success && result.user != null) {
        emit(AuthAuthenticated(result.user!));
        // Save authentication state for persistence
        await _saveAuthState(result.user!, loginMethod: 'email_password');
      } else if (result.requiresTwoFactor && result.twoFactorRefCode != null) {
        emit(AuthTwoFactorRequired(result.twoFactorRefCode!));
      } else {
        final failure = result.failure ??
            ErrorMapper.mapExceptionToFailure(result.error ?? 'Login failed');
        emit(AuthError(failure));
      }
    } catch (e) {
      final failure = ErrorMapper.mapExceptionToFailure(e);
      emit(AuthError(failure));
    }
  }

  /// Handles biometric login requests
  Future<void> _onBiometricLoginRequested(
    AuthBiometricLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      final result = await _biometricLoginUseCase.call(event.email);

      if (result.success && result.user != null) {
        emit(AuthAuthenticated(result.user!));
        // Save authentication state for persistence
        await _saveAuthState(result.user!, loginMethod: 'biometric');
      } else {
        final failure = result.failure ??
            ErrorMapper.mapExceptionToFailure(
                result.error ?? 'Biometric login failed');
        emit(AuthError(failure));
      }
    } catch (e) {
      final failure = ErrorMapper.mapExceptionToFailure(e);
      emit(AuthError(failure));
    }
  }

  /// Handles two-factor authentication code submission
  Future<void> _onTwoFactorSubmitted(
    AuthTwoFactorSubmitted event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      final result =
          await _verifyTwoFactorUseCase.call(event.refCode, event.code);

      if (result.success && result.user != null) {
        emit(AuthAuthenticated(result.user!));
        // Save authentication state for persistence
        await _saveAuthState(result.user!, loginMethod: 'two_factor');
      } else {
        final failure = result.failure ??
            ErrorMapper.mapExceptionToFailure(
                result.error ?? 'Two-factor authentication failed');
        emit(AuthError(failure));
      }
    } catch (e) {
      final failure = ErrorMapper.mapExceptionToFailure(e);
      emit(AuthError(failure));
    }
  }

  /// Handles logout requests
  Future<void> _onLogoutRequested(
    AuthLogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      await _logoutUseCase.call();
      // Clear persisted authentication state
      await _clearAuthState();
      emit(AuthUnauthenticated());
    } catch (e) {
      // Even if logout fails, we should still consider the user logged out locally
      await _clearAuthState();
      emit(AuthUnauthenticated());
    }
  }

  /// Handles global logout requests
  Future<void> _onGlobalLogoutRequested(
    AuthGlobalLogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      await _authRepository.globalLogout();
      // Clear persisted authentication state
      await _clearAuthState();
      emit(AuthUnauthenticated());
    } catch (e) {
      // Even if global logout fails, we should still consider the user logged out locally
      await _clearAuthState();
      emit(AuthUnauthenticated());
    }
  }

  /// Handles authentication status check requests
  Future<void> _onAuthCheckRequested(
    AuthCheckRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      final isAuthenticated = await _authRepository.isAuthenticated();

      if (isAuthenticated) {
        final user = await _authRepository.getCurrentUser();
        if (user != null) {
          emit(AuthAuthenticated(user));
        } else {
          emit(AuthUnauthenticated());
        }
      } else {
        emit(AuthUnauthenticated());
      }
    } catch (e) {
      emit(AuthUnauthenticated());
    }
  }

  /// Handles token refresh requests
  Future<void> _onTokenRefreshRequested(
    AuthTokenRefreshRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      await _authRepository.refreshToken();
      // Token refresh doesn't change the authentication state
      // The current state should remain the same
    } catch (e) {
      // If token refresh fails, the user should be logged out
      emit(AuthUnauthenticated());
    }
  }

  /// Handles authentication state restoration requests
  Future<void> _onAuthStateRestoreRequested(
    AuthStateRestoreRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      // Check if AuthStateService is ready
      if (!await _authStateService.isServiceReady()) {
        emit(AuthUnauthenticated());
        return;
      }

      // Check if there's persisted state
      if (!await _authStateService.hasPersistedState()) {
        emit(AuthUnauthenticated());
        return;
      }

      // Load persisted state
      final authData = await _authStateService.loadAuthState();
      if (authData != null) {
        // Validate the persisted state
        final isValid = await _authStateService.validatePersistedState();
        if (!isValid) {
          // State is invalid, clear it and emit unauthenticated
          await _authStateService.clearAuthState();
          emit(AuthUnauthenticated());
          return;
        }

        // Restore authentication state
        emit(AuthAuthenticated(authData.user!));

        // Update session metadata with current timestamp
        await _authStateService.updateSessionMetadata({
          'lastAccess': DateTime.now().toIso8601String(),
          'restoredFromPersistence': true,
        });
      } else {
        // Failed to load state, emit unauthenticated
        emit(AuthUnauthenticated());
      }
    } catch (e) {
      // Error during state restoration, clear state and emit unauthenticated
      try {
        await _authStateService.clearAuthState();
      } catch (_) {
        // Ignore errors during cleanup
      }
      emit(AuthUnauthenticated());
    }
  }

  /// Helper method to save authentication state after successful login
  Future<void> _saveAuthState(AuthUser user, {String? loginMethod}) async {
    try {
      final stateData = AuthStateData(
        isAuthenticated: true,
        user: user,
        lastAuthTime: DateTime.now(),
        sessionId: 'session_${DateTime.now().millisecondsSinceEpoch}',
        metadata: {
          'loginMethod': loginMethod ?? 'standard',
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      await _authStateService.saveAuthState(stateData);
    } catch (e) {
      // Log error but don't fail the authentication
      if (kDebugMode) {
        developer.log('Failed to save auth state: $e', name: 'AuthBloc');
      }
    }
  }

  /// Helper method to clear authentication state on logout
  Future<void> _clearAuthState() async {
    try {
      await _authStateService.clearAuthState();
    } catch (e) {
      // Log error but don't fail the logout
      if (kDebugMode) {
        developer.log('Failed to clear auth state: $e', name: 'AuthBloc');
      }
    }
  }

  /// Convenience method to check if user is currently authenticated
  bool get isAuthenticated => state is AuthAuthenticated;

  /// Convenience method to get the current authenticated user
  AuthUser? get currentUser {
    final currentState = state;
    if (currentState is AuthAuthenticated) {
      return currentState.user;
    }
    return null;
  }
}
