import 'dart:async';
import 'dart:convert';
import 'dart:developer' as developer;

import 'package:flutter/foundation.dart';

import 'token_service.dart';
import 'secure_storage_service.dart';
import '../../data/datasources/auth_remote_datasource.dart';
import '../error/auth_exceptions.dart';

/// Concrete implementation of TokenService
///
/// This implementation provides all token management functionality including
/// JWT parsing, validation, automatic refresh, and lifecycle management.
/// It integrates with the secure storage service and remote data source
/// for complete token management.
class TokenServiceImpl implements TokenService {
  final SecureStorageService _secureStorage;
  final AuthRemoteDataSource _remoteDataSource;

  Timer? _refreshTimer;
  bool _isRefreshing = false;

  TokenServiceImpl({
    required SecureStorageService secureStorage,
    required AuthRemoteDataSource remoteDataSource,
  })  : _secureStorage = secureStorage,
        _remoteDataSource = remoteDataSource;

  @override
  TokenValidationResult validateToken(String token) {
    try {
      // Check basic JWT format (3 parts separated by dots)
      final parts = token.split('.');
      if (parts.length != 3) {
        return TokenValidationResult.invalid('Invalid JWT format');
      }

      // Parse the payload
      final payload = parseTokenPayload(token);

      // Check if token has expiry time
      if (!payload.containsKey('exp')) {
        return TokenValidationResult.invalid('Token missing expiry time');
      }

      // Get expiry time
      final expiryTime = getTokenExpiryTime(token);

      // Check if expired
      if (isTokenExpired(token)) {
        return TokenValidationResult.invalid('Token is expired');
      }

      return TokenValidationResult.valid(
        expiryTime: expiryTime,
        payload: payload,
      );
    } catch (e) {
      return TokenValidationResult.invalid('Token validation failed: $e');
    }
  }

  @override
  bool isTokenExpired(String token) {
    try {
      final expiryTime = getTokenExpiryTime(token);
      final now = DateTime.now();
      return now.isAfter(expiryTime);
    } catch (e) {
      // If we can't parse the token, consider it expired
      return true;
    }
  }

  @override
  bool isTokenExpiring(String token,
      {Duration threshold = const Duration(minutes: 30)}) {
    try {
      final expiryTime = getTokenExpiryTime(token);
      final now = DateTime.now();
      return now.add(threshold).isAfter(expiryTime);
    } catch (e) {
      // If we can't parse the token, consider it expiring
      return true;
    }
  }

  @override
  DateTime getTokenExpiryTime(String token) {
    try {
      final payload = parseTokenPayload(token);
      final exp = payload['exp'];

      if (exp == null) {
        throw const FormatException('Token missing expiry time');
      }

      // JWT exp is in seconds since epoch
      return DateTime.fromMillisecondsSinceEpoch(exp * 1000);
    } catch (e) {
      throw FormatException('Failed to parse token expiry: $e');
    }
  }

  @override
  Map<String, dynamic> parseTokenPayload(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) {
        throw const FormatException('Invalid JWT format');
      }

      // Get the payload part (middle part)
      String payloadBase64 = parts[1];

      // Normalize base64 padding
      payloadBase64 = base64.normalize(payloadBase64);

      // Decode base64
      final decodedBytes = base64.decode(payloadBase64);
      final decodedString = utf8.decode(decodedBytes);

      // Parse JSON
      return json.decode(decodedString) as Map<String, dynamic>;
    } catch (e) {
      throw FormatException('Failed to parse token payload: $e');
    }
  }

  @override
  Future<TokenRefreshResult> refreshTokens(String refreshToken) async {
    try {
      if (_isRefreshing) {
        // Wait for ongoing refresh to complete
        await _waitForRefreshCompletion();

        // Try to get the refreshed token
        final accessToken = await _secureStorage.read('access_token');
        final newRefreshToken = await _secureStorage.read('refresh_token');

        if (accessToken != null && newRefreshToken != null) {
          return TokenRefreshResult.success(
            accessToken: accessToken,
            refreshToken: newRefreshToken,
          );
        }
      }

      _isRefreshing = true;

      try {
        // Call the remote data source to refresh tokens
        final result = await _remoteDataSource.refreshToken(refreshToken);

        // Store the new tokens
        await _secureStorage.write('access_token', result['access_token']!);
        await _secureStorage.write('refresh_token', result['refresh_token']!);

        if (kDebugMode) {
          developer.log('Tokens refreshed successfully', name: 'TokenService');
        }

        return TokenRefreshResult.success(
          accessToken: result['access_token']!,
          refreshToken: result['refresh_token']!,
        );
      } catch (e) {
        if (kDebugMode) {
          developer.log('Token refresh failed: $e', name: 'TokenService');
        }

        if (e is AuthException) {
          return TokenRefreshResult.failure(e.message);
        }

        return TokenRefreshResult.failure('Failed to refresh tokens: $e');
      } finally {
        _isRefreshing = false;
      }
    } catch (e) {
      _isRefreshing = false;
      return TokenRefreshResult.failure('Token refresh error: $e');
    }
  }

  @override
  Future<String> getValidAccessToken() async {
    try {
      // Get current access token
      final accessToken = await _secureStorage.read('access_token');

      if (accessToken == null) {
        throw const InvalidTokenException(message: 'No access token available');
      }

      // Check if token is valid and not expiring
      if (!isTokenExpired(accessToken) && !isTokenExpiring(accessToken)) {
        return accessToken;
      }

      if (kDebugMode) {
        developer.log('Access token expired or expiring, refreshing...',
            name: 'TokenService');
      }

      // Token is expired or expiring, try to refresh
      final refreshToken = await _secureStorage.read('refresh_token');
      if (refreshToken == null) {
        throw const RefreshTokenExpiredException(
            message: 'No refresh token available');
      }

      final refreshResult = await refreshTokens(refreshToken);
      if (!refreshResult.success) {
        throw TokenException(
            message: refreshResult.error ?? 'Failed to refresh token');
      }

      return refreshResult.accessToken!;
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to get valid access token: $e',
            name: 'TokenService');
      }
      rethrow;
    }
  }

  @override
  void scheduleTokenRefresh(String token, Function() onRefresh) {
    try {
      clearScheduledRefresh();

      if (isTokenExpiring(token, threshold: const Duration(minutes: 10))) {
        // If token expires in less than 10 minutes, refresh immediately
        onRefresh();
        return;
      }

      final expiryTime = getTokenExpiryTime(token);
      final refreshTime = expiryTime.subtract(const Duration(minutes: 10));
      final delay = refreshTime.difference(DateTime.now());

      if (delay.isNegative) {
        // Should refresh immediately
        onRefresh();
        return;
      }

      _refreshTimer = Timer(delay, () {
        if (kDebugMode) {
          developer.log('Scheduled token refresh triggered',
              name: 'TokenService');
        }
        onRefresh();
      });

      if (kDebugMode) {
        developer.log(
            'Token refresh scheduled for ${refreshTime.toIso8601String()}',
            name: 'TokenService');
      }
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to schedule token refresh: $e',
            name: 'TokenService');
      }
    }
  }

  @override
  void clearScheduledRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  @override
  bool isServiceReady() {
    return _secureStorage.isReady();
  }

  /// Waits for any ongoing refresh operation to complete
  Future<void> _waitForRefreshCompletion() async {
    while (_isRefreshing) {
      await Future.delayed(const Duration(milliseconds: 100));
    }
  }

  /// Dispose method to clean up resources
  void dispose() {
    clearScheduledRefresh();
  }
}
