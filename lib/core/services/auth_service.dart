import 'dart:async';
import 'dart:developer' as developer;

import 'package:flutter/foundation.dart';

import 'token_service.dart';
import 'secure_storage_service.dart';
import '../../view_model/loginPostRequests.dart';

/// Authentication service that provides a clean interface for auth operations
/// 
/// This service acts as a facade over the existing LoginPostRequests functionality
/// while gradually introducing the new token and secure storage services.
/// It provides a cleaner interface and better error handling.
class AuthService {
  final TokenService _tokenService;
  final SecureStorageService _secureStorage;
  
  static bool _isLoggedIn = false;

  AuthService({
    required TokenService tokenService,
    required SecureStorageService secureStorage,
  }) : _tokenService = tokenService,
       _secureStorage = secureStorage;

  /// Gets the current login status
  bool get isLoggedIn => _isLoggedIn;

  /// Checks if user is authenticated by validating stored tokens
  Future<bool> checkAuthentication() async {
    try {
      final accessToken = await _secureStorage.read('access_token');
      if (accessToken == null) {
        _isLoggedIn = false;
        return false;
      }

      final validationResult = _tokenService.validateToken(accessToken);
      _isLoggedIn = validationResult.isValid;
      
      if (_isLoggedIn) {
        await _refreshListeners();
      }
      
      return _isLoggedIn;
    } catch (e) {
      if (kDebugMode) {
        developer.log('Authentication check failed: $e', name: 'AuthService');
      }
      _isLoggedIn = false;
      return false;
    }
  }

  /// Gets a valid access token, refreshing if necessary
  Future<String?> getValidAccessToken() async {
    try {
      return await _tokenService.getValidAccessToken();
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to get valid access token: $e', name: 'AuthService');
      }
      return null;
    }
  }

  /// Validates a token and returns validation result
  TokenValidationResult validateToken(String token) {
    return _tokenService.validateToken(token);
  }

  /// Checks if a token is expired
  bool isTokenExpired(String token) {
    return _tokenService.isTokenExpired(token);
  }

  /// Checks if a token is expiring soon
  bool isTokenExpiring(String token, {Duration threshold = const Duration(minutes: 30)}) {
    return _tokenService.isTokenExpiring(token, threshold: threshold);
  }

  /// Gets token expiry time
  DateTime getTokenExpiryTime(String token) {
    return _tokenService.getTokenExpiryTime(token);
  }

  /// Parses token payload
  Map<String, dynamic> parseTokenPayload(String token) {
    return _tokenService.parseTokenPayload(token);
  }

  /// Refreshes tokens using the refresh token
  Future<TokenRefreshResult> refreshTokens() async {
    try {
      final refreshToken = await _secureStorage.read('refresh_token');
      if (refreshToken == null) {
        return TokenRefreshResult.failure('No refresh token available');
      }

      return await _tokenService.refreshTokens(refreshToken);
    } catch (e) {
      return TokenRefreshResult.failure('Token refresh failed: $e');
    }
  }

  /// Schedules automatic token refresh
  void scheduleTokenRefresh(String token, Function() onRefresh) {
    _tokenService.scheduleTokenRefresh(token, onRefresh);
  }

  /// Clears scheduled token refresh
  void clearScheduledRefresh() {
    _tokenService.clearScheduledRefresh();
  }

  /// Stores tokens securely
  Future<void> storeTokens(String accessToken, String refreshToken) async {
    try {
      await _secureStorage.writeBatch({
        'access_token': accessToken,
        'refresh_token': refreshToken,
      });
      
      if (kDebugMode) {
        developer.log('Tokens stored successfully', name: 'AuthService');
      }
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to store tokens: $e', name: 'AuthService');
      }
      rethrow;
    }
  }

  /// Stores user credentials for biometric login
  Future<void> storeCredentials(String email, String password) async {
    try {
      await _secureStorage.writeBatch({
        'email': email,
        'password': password,
      });
      
      if (kDebugMode) {
        developer.log('Credentials stored successfully', name: 'AuthService');
      }
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to store credentials: $e', name: 'AuthService');
      }
      rethrow;
    }
  }

  /// Gets stored credentials
  Future<Map<String, String>?> getStoredCredentials() async {
    try {
      final result = await _secureStorage.readBatch(['email', 'password']);
      final email = result['email'];
      final password = result['password'];
      
      if (email == null || password == null) return null;
      
      return {'email': email, 'password': password};
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to get stored credentials: $e', name: 'AuthService');
      }
      return null;
    }
  }

  /// Sets two-factor authentication status
  Future<void> setTwoFactorEnabled(bool enabled) async {
    try {
      await _secureStorage.write('two_factor', enabled.toString());
      
      if (kDebugMode) {
        developer.log('Two-factor status updated: $enabled', name: 'AuthService');
      }
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to set two-factor status: $e', name: 'AuthService');
      }
      rethrow;
    }
  }

  /// Gets two-factor authentication status
  Future<bool> isTwoFactorEnabled() async {
    try {
      final value = await _secureStorage.read('two_factor');
      return value == 'true';
    } catch (e) {
      return false;
    }
  }

  /// Sets biometric authentication status
  Future<void> setBiometricEnabled(bool enabled) async {
    try {
      await _secureStorage.write('biometric', enabled.toString());
      
      if (kDebugMode) {
        developer.log('Biometric status updated: $enabled', name: 'AuthService');
      }
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to set biometric status: $e', name: 'AuthService');
      }
      rethrow;
    }
  }

  /// Gets biometric authentication status
  Future<bool> isBiometricEnabled() async {
    try {
      final value = await _secureStorage.read('biometric');
      return value == 'true';
    } catch (e) {
      return false;
    }
  }

  /// Clears all stored authentication data
  Future<void> clearAuthData({bool preserveBiometric = false, bool preserveTheme = false}) async {
    try {
      _isLoggedIn = false;
      
      // Get values to preserve if needed
      String? biometricValue;
      String? themeValue;
      String? email;
      String? password;
      
      if (preserveBiometric) {
        biometricValue = await _secureStorage.read('biometric');
        if (biometricValue == 'true') {
          email = await _secureStorage.read('email');
          password = await _secureStorage.read('password');
        }
      }
      
      if (preserveTheme) {
        themeValue = await _secureStorage.read('themeMode');
      }
      
      // Clear all data
      await _secureStorage.clear();
      
      // Restore preserved values
      if (preserveBiometric && biometricValue == 'true' && email != null && password != null) {
        await _secureStorage.writeBatch({
          'email': email,
          'password': password,
          'biometric': 'true',
        });
      }
      
      if (preserveTheme && themeValue != null) {
        await _secureStorage.write('themeMode', themeValue);
      }
      
      if (kDebugMode) {
        developer.log('Auth data cleared successfully', name: 'AuthService');
      }
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to clear auth data: $e', name: 'AuthService');
      }
      rethrow;
    }
  }

  /// Performs health check on the auth service
  Future<bool> performHealthCheck() async {
    try {
      final storageHealth = await _secureStorage.checkHealth();
      final tokenServiceReady = _tokenService.isServiceReady();
      
      return storageHealth.isHealthy && tokenServiceReady;
    } catch (e) {
      if (kDebugMode) {
        developer.log('Health check failed: $e', name: 'AuthService');
      }
      return false;
    }
  }

  /// Refreshes listeners (internal method)
  Future<void> _refreshListeners() async {
    try {
      // This method updates global state variables that other parts of the app depend on
      // For now, we'll delegate to the existing implementation
      await LoginPostRequests.refreshListeners();
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to refresh listeners: $e', name: 'AuthService');
      }
    }
  }

  /// Disposes the service and cleans up resources
  void dispose() {
    clearScheduledRefresh();
  }
}
