import 'dart:convert';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

import '../../domain/entities/auth_user.dart';
import '../../core/error/auth_failures.dart';
import 'secure_storage_service.dart';

/// Result of authentication state operations
class AuthStateResult {
  final bool success;
  final AuthFailure? failure;
  final String? message;

  const AuthStateResult({
    required this.success,
    this.failure,
    this.message,
  });

  /// Creates a successful result
  factory AuthStateResult.success({String? message}) {
    return AuthStateResult(
      success: true,
      message: message,
    );
  }

  /// Creates a failed result
  factory AuthStateResult.failure({
    required AuthFailure failure,
  }) {
    return AuthStateResult(
      success: false,
      failure: failure,
      message: failure.message,
    );
  }
}

/// Authentication state data for persistence
class AuthStateData {
  final bool isAuthenticated;
  final AuthUser? user;
  final DateTime? lastAuthTime;
  final String? sessionId;
  final Map<String, dynamic>? metadata;

  const AuthStateData({
    required this.isAuthenticated,
    this.user,
    this.lastAuthTime,
    this.sessionId,
    this.metadata,
  });

  /// Creates authenticated state data
  factory AuthStateData.authenticated({
    required AuthUser user,
    String? sessionId,
    Map<String, dynamic>? metadata,
  }) {
    return AuthStateData(
      isAuthenticated: true,
      user: user,
      lastAuthTime: DateTime.now(),
      sessionId: sessionId,
      metadata: metadata,
    );
  }

  /// Creates unauthenticated state data
  factory AuthStateData.unauthenticated() {
    return const AuthStateData(
      isAuthenticated: false,
    );
  }

  /// Converts to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'isAuthenticated': isAuthenticated,
      'user': user?.toJson(),
      'lastAuthTime': lastAuthTime?.toIso8601String(),
      'sessionId': sessionId,
      'metadata': metadata,
    };
  }

  /// Creates from JSON storage
  factory AuthStateData.fromJson(Map<String, dynamic> json) {
    return AuthStateData(
      isAuthenticated: json['isAuthenticated'] ?? false,
      user: json['user'] != null ? AuthUser.fromJson(json['user']) : null,
      lastAuthTime: json['lastAuthTime'] != null
          ? DateTime.parse(json['lastAuthTime'])
          : null,
      sessionId: json['sessionId'],
      metadata: json['metadata'] != null
          ? Map<String, dynamic>.from(json['metadata'])
          : null,
    );
  }
}

/// Abstract interface for authentication state persistence operations
///
/// This service provides a clean abstraction for all authentication state
/// persistence operations including saving, loading, and clearing auth state.
/// It follows clean architecture principles by separating state persistence
/// business logic from implementation details.
abstract class AuthStateService {
  /// Saves the current authentication state
  ///
  /// This method persists the authentication state securely including:
  /// - Authentication status
  /// - User information
  /// - Session metadata
  /// - Timestamp information
  ///
  /// Parameters:
  /// - [stateData]: The authentication state data to persist
  ///
  /// Returns: [AuthStateResult] indicating success or failure
  Future<AuthStateResult> saveAuthState(AuthStateData stateData);

  /// Loads the persisted authentication state
  ///
  /// This method retrieves the previously saved authentication state
  /// and validates its integrity and expiry.
  ///
  /// Returns: [AuthStateData] if state exists and is valid, null otherwise
  Future<AuthStateData?> loadAuthState();

  /// Clears all persisted authentication state
  ///
  /// This method removes all stored authentication state data
  /// while optionally preserving certain user preferences.
  ///
  /// Parameters:
  /// - [preserveUserPreferences]: Whether to keep user preferences
  ///
  /// Returns: [AuthStateResult] indicating success or failure
  Future<AuthStateResult> clearAuthState(
      {bool preserveUserPreferences = false});

  /// Checks if authentication state exists
  ///
  /// Returns: true if persisted state exists, false otherwise
  Future<bool> hasPersistedState();

  /// Validates the integrity of persisted state
  ///
  /// This method checks if the stored state is valid and not corrupted.
  ///
  /// Returns: true if state is valid, false otherwise
  Future<bool> validatePersistedState();

  /// Gets the last authentication timestamp
  ///
  /// Returns: [DateTime] of last authentication, null if not available
  Future<DateTime?> getLastAuthTime();

  /// Updates session metadata without changing authentication status
  ///
  /// Parameters:
  /// - [metadata]: New metadata to store
  ///
  /// Returns: [AuthStateResult] indicating success or failure
  Future<AuthStateResult> updateSessionMetadata(Map<String, dynamic> metadata);

  /// Checks if the service is ready for operations
  ///
  /// Returns: true if service is ready, false otherwise
  bool isServiceReady();
}

/// Storage failure when state persistence operations fail
class StateStorageFailure extends AuthFailure {
  const StateStorageFailure({
    String message = 'Failed to store authentication state. Please try again',
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'STATE_STORAGE_FAILURE',
          context: context,
        );
}

/// Failure when state data is corrupted or invalid
class StateCorruptionFailure extends AuthFailure {
  const StateCorruptionFailure({
    String message =
        'Authentication state data is corrupted. Please sign in again',
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'STATE_CORRUPTION_FAILURE',
          context: context,
        );
}

/// Failure when state has expired
class StateExpiredFailure extends AuthFailure {
  const StateExpiredFailure({
    String message = 'Authentication state has expired. Please sign in again',
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'STATE_EXPIRED_FAILURE',
          context: context,
        );
}

/// Concrete implementation of AuthStateService using secure storage
///
/// This implementation provides authentication state persistence using
/// FlutterSecureStorage through the SecureStorageService abstraction.
/// It includes comprehensive error handling, data validation, and
/// automatic state expiry management.
class AuthStateServiceImpl implements AuthStateService {
  final SecureStorageService _secureStorage;

  // Storage keys
  static const String _authStateKey = 'auth_state_data';
  static const String _stateVersionKey = 'auth_state_version';
  static const String _currentStateVersion = '1.0';

  // State expiry duration (7 days)
  static const Duration _stateExpiryDuration = Duration(days: 7);

  bool _isReady = true;

  AuthStateServiceImpl({
    required SecureStorageService secureStorage,
  }) : _secureStorage = secureStorage;

  @override
  Future<AuthStateResult> saveAuthState(AuthStateData stateData) async {
    try {
      if (kDebugMode) {
        developer.log('Saving authentication state', name: 'AuthStateService');
      }

      // Prepare data for storage
      final stateJson = stateData.toJson();
      final stateString = jsonEncode(stateJson);

      // Save state data and version
      await _secureStorage.writeBatch({
        _authStateKey: stateString,
        _stateVersionKey: _currentStateVersion,
      });

      if (kDebugMode) {
        developer.log('Authentication state saved successfully',
            name: 'AuthStateService');
      }

      return AuthStateResult.success(
        message: 'Authentication state saved successfully',
      );
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to save authentication state: $e',
            name: 'AuthStateService');
      }

      _isReady = false;
      return AuthStateResult.failure(
        failure: StateStorageFailure(
          message: 'Failed to save authentication state',
          context: {'error': e.toString()},
        ),
      );
    }
  }

  @override
  Future<AuthStateData?> loadAuthState() async {
    try {
      if (kDebugMode) {
        developer.log('Loading authentication state', name: 'AuthStateService');
      }

      // Check if state exists
      if (!await hasPersistedState()) {
        if (kDebugMode) {
          developer.log('No persisted state found', name: 'AuthStateService');
        }
        return null;
      }

      // Validate state version
      final version = await _secureStorage.read(_stateVersionKey);
      if (version != _currentStateVersion) {
        if (kDebugMode) {
          developer.log(
              'State version mismatch: $version != $_currentStateVersion',
              name: 'AuthStateService');
        }
        await clearAuthState();
        return null;
      }

      // Load and parse state data
      final stateString = await _secureStorage.read(_authStateKey);
      if (stateString == null) {
        return null;
      }

      final stateJson = jsonDecode(stateString) as Map<String, dynamic>;
      final stateData = AuthStateData.fromJson(stateJson);

      // Check if state has expired
      if (_isStateExpired(stateData)) {
        if (kDebugMode) {
          developer.log('Authentication state has expired',
              name: 'AuthStateService');
        }
        await clearAuthState();
        return null;
      }

      if (kDebugMode) {
        developer.log('Authentication state loaded successfully',
            name: 'AuthStateService');
      }

      return stateData;
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to load authentication state: $e',
            name: 'AuthStateService');
      }

      // Clear corrupted state
      await clearAuthState();
      return null;
    }
  }

  @override
  Future<AuthStateResult> clearAuthState(
      {bool preserveUserPreferences = false}) async {
    try {
      if (kDebugMode) {
        developer.log('Clearing authentication state',
            name: 'AuthStateService');
      }

      // Get user preferences if we need to preserve them
      Map<String, String>? userPreferences;
      if (preserveUserPreferences) {
        userPreferences = await _getUserPreferences();
      }

      // Clear auth state data
      await _secureStorage.delete(_authStateKey);
      await _secureStorage.delete(_stateVersionKey);

      // Restore user preferences if needed
      if (preserveUserPreferences && userPreferences != null) {
        await _secureStorage.writeBatch(userPreferences);
      }

      if (kDebugMode) {
        developer.log('Authentication state cleared successfully',
            name: 'AuthStateService');
      }

      return AuthStateResult.success(
        message: 'Authentication state cleared successfully',
      );
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to clear authentication state: $e',
            name: 'AuthStateService');
      }

      return AuthStateResult.failure(
        failure: StateStorageFailure(
          message: 'Failed to clear authentication state',
          context: {'error': e.toString()},
        ),
      );
    }
  }

  @override
  Future<bool> hasPersistedState() async {
    try {
      final stateData = await _secureStorage.read(_authStateKey);
      return stateData != null;
    } catch (e) {
      if (kDebugMode) {
        developer.log('Error checking persisted state: $e',
            name: 'AuthStateService');
      }
      return false;
    }
  }

  @override
  Future<bool> validatePersistedState() async {
    try {
      final stateData = await loadAuthState();
      return stateData != null;
    } catch (e) {
      if (kDebugMode) {
        developer.log('State validation failed: $e', name: 'AuthStateService');
      }
      return false;
    }
  }

  @override
  Future<DateTime?> getLastAuthTime() async {
    try {
      final stateData = await loadAuthState();
      return stateData?.lastAuthTime;
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to get last auth time: $e',
            name: 'AuthStateService');
      }
      return null;
    }
  }

  @override
  Future<AuthStateResult> updateSessionMetadata(
      Map<String, dynamic> metadata) async {
    try {
      final currentState = await loadAuthState();
      if (currentState == null) {
        return AuthStateResult.failure(
          failure: const StateCorruptionFailure(
            message: 'No authentication state found to update',
          ),
        );
      }

      final updatedState = AuthStateData(
        isAuthenticated: currentState.isAuthenticated,
        user: currentState.user,
        lastAuthTime: currentState.lastAuthTime,
        sessionId: currentState.sessionId,
        metadata: {...?currentState.metadata, ...metadata},
      );

      return await saveAuthState(updatedState);
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to update session metadata: $e',
            name: 'AuthStateService');
      }

      return AuthStateResult.failure(
        failure: StateStorageFailure(
          message: 'Failed to update session metadata',
          context: {'error': e.toString()},
        ),
      );
    }
  }

  @override
  bool isServiceReady() {
    return _isReady && _secureStorage.isReady();
  }

  /// Checks if the authentication state has expired
  bool _isStateExpired(AuthStateData stateData) {
    if (stateData.lastAuthTime == null) {
      return true;
    }

    final now = DateTime.now();
    final expiryTime = stateData.lastAuthTime!.add(_stateExpiryDuration);
    return now.isAfter(expiryTime);
  }

  /// Gets user preferences that should be preserved during state clearing
  Future<Map<String, String>?> _getUserPreferences() async {
    try {
      final preferences = <String, String>{};

      // List of keys to preserve
      const preserveKeys = [
        'themeMode',
        'language',
        'notification_settings',
        'app_preferences',
      ];

      for (final key in preserveKeys) {
        final value = await _secureStorage.read(key);
        if (value != null) {
          preferences[key] = value;
        }
      }

      return preferences.isNotEmpty ? preferences : null;
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to get user preferences: $e',
            name: 'AuthStateService');
      }
      return null;
    }
  }
}
