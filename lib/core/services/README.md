# Authentication Services

This directory contains the new authentication services that provide a clean, testable, and maintainable approach to token management and secure storage.

## Services Overview

### 1. TokenService (`token_service.dart`)
Abstract interface for token management operations including:
- JWT token validation and parsing
- Token expiration checking
- Automatic token refresh logic
- Token lifecycle management

**Implementations:**
- `TokenServiceImpl` - Full implementation using remote data sources
- `LegacyTokenService` - Bridge implementation using existing LoginPostRequests

### 2. SecureStorageService (`secure_storage_service.dart`)
Wrapper around FlutterSecureStorage providing:
- Enhanced error handling
- Batch operations
- Health checks
- Object serialization/deserialization
- Import/export functionality

### 3. AuthService (`auth_service.dart`)
High-level authentication service that combines token and storage services:
- Authentication status management
- Token validation and refresh
- Credential storage
- Two-factor and biometric settings
- Clean data management

## Usage Examples

### Basic Token Operations
```dart
// Get the token service from dependency injection
final tokenService = sl<TokenService>();

// Validate a token
final result = tokenService.validateToken(token);
if (result.isValid) {
  print('Token expires at: ${result.expiryTime}');
} else {
  print('Token invalid: ${result.reason}');
}

// Check if token is expiring
if (tokenService.isTokenExpiring(token)) {
  // Refresh the token
  final refreshResult = await tokenService.refreshTokens(refreshToken);
  if (refreshResult.success) {
    // Use new tokens
    final newAccessToken = refreshResult.accessToken;
    final newRefreshToken = refreshResult.refreshToken;
  }
}

// Get a valid access token (auto-refresh if needed)
final validToken = await tokenService.getValidAccessToken();
```

### Secure Storage Operations
```dart
// Get the secure storage service
final secureStorage = sl<SecureStorageService>();

// Store simple values
await secureStorage.write('key', 'value');
final value = await secureStorage.read('key');

// Store objects
await secureStorage.writeObject('user', {'id': '123', 'name': 'John'});
final user = await secureStorage.readObject('user');

// Batch operations
await secureStorage.writeBatch({
  'token1': 'value1',
  'token2': 'value2',
});

final values = await secureStorage.readBatch(['token1', 'token2']);

// Health check
final health = await secureStorage.checkHealth();
if (!health.isHealthy) {
  print('Storage issue: ${health.issue}');
}
```

### High-Level Auth Operations
```dart
// Get the auth service
final authService = sl<AuthService>();

// Check authentication status
final isAuthenticated = await authService.checkAuthentication();

// Get valid access token
final token = await authService.getValidAccessToken();

// Store authentication data
await authService.storeTokens(accessToken, refreshToken);
await authService.storeCredentials(email, password);

// Manage settings
await authService.setTwoFactorEnabled(true);
await authService.setBiometricEnabled(true);

// Clear auth data (with options to preserve certain data)
await authService.clearAuthData(
  preserveBiometric: true,
  preserveTheme: true,
);

// Health check
final isHealthy = await authService.performHealthCheck();
```

## Migration Strategy

The services are designed to work alongside the existing `LoginPostRequests` implementation:

1. **Phase 1** (Current): Services are available but `LoginPostRequests` is still used
2. **Phase 2**: Gradually replace `LoginPostRequests` calls with service calls
3. **Phase 3**: Remove `LoginPostRequests` and use `TokenServiceImpl` instead of `LegacyTokenService`

### Dependency Injection

All services are registered in `lib/core/di/injection_container.dart`:

```dart
// Core services
sl.registerLazySingleton<SecureStorageService>(() => SecureStorageServiceImpl(storage: sl()));
sl.registerLazySingleton<TokenService>(() => LegacyTokenService(secureStorage: sl()));
sl.registerLazySingleton<AuthService>(() => AuthService(tokenService: sl(), secureStorage: sl()));
```

## Benefits

1. **Testability**: All services use dependency injection and interfaces
2. **Maintainability**: Clear separation of concerns
3. **Reusability**: Services can be used across the application
4. **Error Handling**: Consistent error handling patterns
5. **Performance**: Batch operations and caching where appropriate
6. **Security**: Enhanced secure storage with health checks

## Testing

Each service can be easily mocked for testing:

```dart
// Mock the token service
class MockTokenService extends Mock implements TokenService {}

// Use in tests
final mockTokenService = MockTokenService();
when(mockTokenService.validateToken(any)).thenReturn(TokenValidationResult.valid(...));
```

## Future Enhancements

- Add encryption/decryption capabilities to SecureStorageService
- Implement token caching in TokenService
- Add metrics and analytics to AuthService
- Create specialized services for biometric and two-factor authentication
