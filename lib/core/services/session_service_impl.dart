import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

import 'session_service.dart';
import 'token_service.dart';
import 'secure_storage_service.dart';
import '../../model/userInfo.dart';
import '../../view_model/loginPostRequests.dart';

/// Concrete implementation of SessionService
///
/// This implementation provides complete session management functionality including
/// lifecycle management, automatic refresh, event notifications, and safe concurrent
/// operations. It integrates with TokenService and SecureStorageService following
/// clean architecture principles.
class SessionServiceImpl implements SessionService {
  final TokenService _tokenService;
  final SecureStorageService _secureStorage;

  // Session state management
  SessionState _currentState = SessionState.none;
  List<Session> _currentSessions = [];
  Duration _sessionTimeout = const Duration(hours: 24);

  // Event management
  final StreamController<SessionEvent> _sessionEventController =
      StreamController<SessionEvent>.broadcast();

  // Refresh management
  Timer? _autoRefreshTimer;
  bool _isRefreshing = false;
  VoidCallback? _refreshCallback;

  // Concurrency control
  final Map<String, Completer<SessionRefreshResult>> _refreshCompleters = {};

  SessionServiceImpl({
    required TokenService tokenService,
    required SecureStorageService secureStorage,
  })  : _tokenService = tokenService,
        _secureStorage = secureStorage;

  @override
  Stream<SessionEvent> get sessionEvents => _sessionEventController.stream;

  @override
  SessionState get currentState => _currentState;

  @override
  List<Session> get currentSessions => List.unmodifiable(_currentSessions);

  @override
  Future<SessionValidationResult> startSession() async {
    try {
      if (kDebugMode) {
        developer.log('Starting new session', name: 'SessionService');
      }

      // Check if we're already authenticated
      final accessToken = await _secureStorage.read('access_token');
      if (accessToken == null) {
        _updateState(SessionState.invalid);
        _emitEvent(SessionEventType.sessionInvalid,
            message: 'No access token available');
        return SessionValidationResult.invalid(
          state: SessionState.invalid,
          reason: 'No access token available',
        );
      }

      // Validate the token
      final tokenValidation = _tokenService.validateToken(accessToken);
      if (!tokenValidation.isValid) {
        _updateState(SessionState.expired);
        _emitEvent(SessionEventType.sessionExpired,
            message: tokenValidation.reason);
        return SessionValidationResult.invalid(
          state: SessionState.expired,
          reason: tokenValidation.reason ?? 'Token validation failed',
        );
      }

      // Fetch session data from server
      final refreshResult = await _fetchSessionData();
      if (!refreshResult.success) {
        _updateState(SessionState.invalid);
        _emitEvent(SessionEventType.sessionInvalid,
            message: refreshResult.errorMessage);
        return SessionValidationResult.invalid(
          state: SessionState.invalid,
          reason: refreshResult.errorMessage ?? 'Failed to fetch session data',
        );
      }

      // Update state and schedule auto refresh
      _currentSessions = refreshResult.sessions ?? [];
      _updateState(SessionState.active);
      scheduleAutoRefresh();

      _emitEvent(SessionEventType.sessionStarted, sessions: _currentSessions);

      if (kDebugMode) {
        developer.log(
            'Session started successfully with ${_currentSessions.length} sessions',
            name: 'SessionService');
      }

      return SessionValidationResult.valid(
        sessions: _currentSessions,
        expiryTime: tokenValidation.expiryTime,
      );
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to start session: $e', name: 'SessionService');
      }

      _updateState(SessionState.invalid);
      _emitEvent(SessionEventType.sessionInvalid,
          message: 'Session start failed: $e');

      return SessionValidationResult.invalid(
        state: SessionState.invalid,
        reason: 'Session start failed: $e',
      );
    }
  }

  @override
  Future<SessionRefreshResult> refreshSession({bool force = false}) async {
    // Handle concurrent refresh requests
    const refreshKey = 'session_refresh';
    if (_refreshCompleters.containsKey(refreshKey)) {
      return await _refreshCompleters[refreshKey]!.future;
    }

    final completer = Completer<SessionRefreshResult>();
    _refreshCompleters[refreshKey] = completer;

    try {
      if (_isRefreshing && !force) {
        if (kDebugMode) {
          developer.log('Session refresh already in progress',
              name: 'SessionService');
        }
        final result = SessionRefreshResult.success(sessions: _currentSessions);
        completer.complete(result);
        return result;
      }

      _isRefreshing = true;
      _updateState(SessionState.refreshing);

      if (kDebugMode) {
        developer.log('Refreshing session data', name: 'SessionService');
      }

      // Check if we need to refresh tokens first
      final accessToken = await _secureStorage.read('access_token');
      if (accessToken == null) {
        final result =
            SessionRefreshResult.failure('No access token available');
        completer.complete(result);
        return result;
      }

      // If token is expired or expiring, try to refresh it
      if (_tokenService.isTokenExpired(accessToken) ||
          _tokenService.isTokenExpiring(accessToken)) {
        try {
          await _tokenService.getValidAccessToken();
          if (kDebugMode) {
            developer.log('Token refreshed during session refresh',
                name: 'SessionService');
          }
        } catch (e) {
          if (kDebugMode) {
            developer.log('Token refresh failed during session refresh: $e',
                name: 'SessionService');
          }
          _updateState(SessionState.expired);
          final result =
              SessionRefreshResult.failure('Token refresh failed: $e');
          completer.complete(result);
          return result;
        }
      }

      // Fetch updated session data
      final refreshResult = await _fetchSessionData();
      if (!refreshResult.success) {
        _updateState(SessionState.invalid);
        _emitEvent(SessionEventType.refreshFailed,
            message: refreshResult.errorMessage);
        completer.complete(refreshResult);
        return refreshResult;
      }

      // Update session data
      _currentSessions = refreshResult.sessions ?? [];
      _updateState(SessionState.active);

      // Reschedule auto refresh
      scheduleAutoRefresh();

      _emitEvent(SessionEventType.sessionRefreshed, sessions: _currentSessions);

      if (kDebugMode) {
        developer.log(
            'Session refreshed successfully with ${_currentSessions.length} sessions',
            name: 'SessionService');
      }

      // Execute refresh callback if provided
      _refreshCallback?.call();

      final result = SessionRefreshResult.success(
        sessions: _currentSessions,
        nextRefreshTime: _calculateNextRefreshTime(),
      );
      completer.complete(result);
      return result;
    } catch (e) {
      if (kDebugMode) {
        developer.log('Session refresh failed: $e', name: 'SessionService');
      }

      _updateState(SessionState.invalid);
      _emitEvent(SessionEventType.refreshFailed,
          message: 'Session refresh failed: $e');

      final result = SessionRefreshResult.failure('Session refresh failed: $e');
      completer.complete(result);
      return result;
    } finally {
      _isRefreshing = false;
      _refreshCompleters.remove(refreshKey);
    }
  }

  @override
  Future<void> endSession({
    bool clearLocal = true,
    bool notifyServer = false,
  }) async {
    try {
      if (kDebugMode) {
        developer.log(
            'Ending session (clearLocal: $clearLocal, notifyServer: $notifyServer)',
            name: 'SessionService');
      }

      // Clear auto refresh timer
      clearAutoRefresh();

      // Clear local session data
      if (clearLocal) {
        _currentSessions.clear();
        await _clearStoredSessionData();
      }

      // Notify server if requested (this would typically be a logout call)
      if (notifyServer) {
        try {
          // This would be implemented based on your API requirements
          // await _notifyServerSessionEnd();
          if (kDebugMode) {
            developer.log(
                'Server notification for session end would be called here',
                name: 'SessionService');
          }
        } catch (e) {
          if (kDebugMode) {
            developer.log('Failed to notify server of session end: $e',
                name: 'SessionService');
          }
          // Don't throw here as local cleanup should still proceed
        }
      }

      _updateState(SessionState.none);
      _emitEvent(SessionEventType.sessionEnded);

      if (kDebugMode) {
        developer.log('Session ended successfully', name: 'SessionService');
      }
    } catch (e) {
      if (kDebugMode) {
        developer.log('Error ending session: $e', name: 'SessionService');
      }
      // Still update state even if there were errors
      _updateState(SessionState.none);
      _emitEvent(SessionEventType.sessionEnded,
          message: 'Session ended with errors: $e');
    }
  }

  /// Fetches session data from the server
  ///
  /// This method calls the existing LoginPostRequests.tokenCheck() to get
  /// session information and parses it into Session objects.
  Future<SessionRefreshResult> _fetchSessionData() async {
    try {
      // Use existing token check method to get session data
      final json = await LoginPostRequests.tokenCheck();

      final List<Session> sessions = [];
      final sessionsData = json["profile"]?["sessions"];

      if (sessionsData != null) {
        for (var sessionData in sessionsData) {
          sessions.add(Session.fromJson(sessionData));
        }
      }

      return SessionRefreshResult.success(sessions: sessions);
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to fetch session data: $e',
            name: 'SessionService');
      }
      return SessionRefreshResult.failure('Failed to fetch session data: $e');
    }
  }

  /// Updates the current session state and logs the change
  void _updateState(SessionState newState) {
    if (_currentState != newState) {
      final oldState = _currentState;
      _currentState = newState;

      if (kDebugMode) {
        developer.log('Session state changed: $oldState -> $newState',
            name: 'SessionService');
      }
    }
  }

  /// Emits a session event to the event stream
  void _emitEvent(
    SessionEventType type, {
    List<Session>? sessions,
    String? message,
  }) {
    final event = SessionEvent(
      type: type,
      state: _currentState,
      sessions: sessions,
      message: message,
    );

    _sessionEventController.add(event);
  }

  /// Calculates the next refresh time based on token expiry
  DateTime? _calculateNextRefreshTime() {
    try {
      final accessToken = _secureStorage.read('access_token');

      // Schedule refresh 5 minutes before token expires
      final expiryTime =
          _tokenService.getTokenExpiryTime(accessToken as String);
      return expiryTime.subtract(const Duration(minutes: 5));
    } catch (e) {
      return null;
    }
  }

  /// Clears stored session data from secure storage
  Future<void> _clearStoredSessionData() async {
    try {
      // Clear any session-related data from secure storage
      // This could include cached session information if we store it
      await _secureStorage.delete('cached_sessions');
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to clear stored session data: $e',
            name: 'SessionService');
      }
    }
  }

  @override
  Future<SessionValidationResult> validateSession(
      {bool checkServer = false}) async {
    try {
      if (kDebugMode) {
        developer.log('Validating session (checkServer: $checkServer)',
            name: 'SessionService');
      }

      // Check if we have an access token
      final accessToken = await _secureStorage.read('access_token');
      if (accessToken == null) {
        _updateState(SessionState.invalid);
        return SessionValidationResult.invalid(
          state: SessionState.invalid,
          reason: 'No access token available',
        );
      }

      // Validate the token
      final tokenValidation = _tokenService.validateToken(accessToken);
      if (!tokenValidation.isValid) {
        _updateState(SessionState.expired);
        return SessionValidationResult.invalid(
          state: SessionState.expired,
          reason: tokenValidation.reason ?? 'Token validation failed',
        );
      }

      // If server check is requested, fetch fresh session data
      if (checkServer) {
        final refreshResult = await _fetchSessionData();
        if (!refreshResult.success) {
          _updateState(SessionState.invalid);
          return SessionValidationResult.invalid(
            state: SessionState.invalid,
            reason: refreshResult.errorMessage ?? 'Server validation failed',
          );
        }
        _currentSessions = refreshResult.sessions ?? [];
      }

      _updateState(SessionState.active);
      return SessionValidationResult.valid(
        sessions: _currentSessions,
        expiryTime: tokenValidation.expiryTime,
      );
    } catch (e) {
      if (kDebugMode) {
        developer.log('Session validation failed: $e', name: 'SessionService');
      }

      _updateState(SessionState.invalid);
      return SessionValidationResult.invalid(
        state: SessionState.invalid,
        reason: 'Session validation failed: $e',
      );
    }
  }

  @override
  void configureSessionTimeout(Duration timeout) {
    _sessionTimeout = timeout;
    if (kDebugMode) {
      developer.log(
          'Session timeout configured to: ${timeout.inMinutes} minutes',
          name: 'SessionService');
    }

    // Reschedule auto refresh with new timeout
    if (_currentState == SessionState.active) {
      scheduleAutoRefresh();
    }
  }

  @override
  void scheduleAutoRefresh({VoidCallback? refreshCallback}) {
    // Clear existing timer
    clearAutoRefresh();

    _refreshCallback = refreshCallback;

    try {
      final nextRefreshTime = _calculateNextRefreshTime();
      if (nextRefreshTime == null) {
        if (kDebugMode) {
          developer.log('Cannot schedule auto refresh: no valid token',
              name: 'SessionService');
        }
        return;
      }

      final now = DateTime.now();
      final delay = nextRefreshTime.difference(now);

      if (delay.isNegative) {
        // Token is already expired, refresh immediately
        if (kDebugMode) {
          developer.log('Token already expired, refreshing immediately',
              name: 'SessionService');
        }
        refreshSession();
        return;
      }

      _autoRefreshTimer = Timer(delay, () async {
        if (kDebugMode) {
          developer.log('Auto refresh timer triggered', name: 'SessionService');
        }
        await refreshSession();
      });

      if (kDebugMode) {
        developer.log(
            'Auto refresh scheduled for: $nextRefreshTime (in ${delay.inMinutes} minutes)',
            name: 'SessionService');
      }
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to schedule auto refresh: $e',
            name: 'SessionService');
      }
    }
  }

  @override
  void clearAutoRefresh() {
    _autoRefreshTimer?.cancel();
    _autoRefreshTimer = null;
    _refreshCallback = null;

    if (kDebugMode) {
      developer.log('Auto refresh cleared', name: 'SessionService');
    }
  }

  @override
  Session? getSessionByClientId(String clientId) {
    try {
      return _currentSessions.firstWhere(
        (session) => session.clientID == clientId,
      );
    } catch (e) {
      return null;
    }
  }

  @override
  bool isSessionExpiring({Duration threshold = const Duration(minutes: 5)}) {
    try {
      final accessToken = _secureStorage.read('access_token');

      return _tokenService.isTokenExpiring(accessToken as String);
    } catch (e) {
      return true;
    }
  }

  @override
  Future<bool> performHealthCheck() async {
    try {
      if (kDebugMode) {
        developer.log('Performing session health check',
            name: 'SessionService');
      }

      // Check 1: Token availability and validity
      final accessToken = await _secureStorage.read('access_token');
      if (accessToken == null) {
        if (kDebugMode) {
          developer.log('Health check failed: No access token',
              name: 'SessionService');
        }
        return false;
      }

      final tokenValidation = _tokenService.validateToken(accessToken);
      if (!tokenValidation.isValid) {
        if (kDebugMode) {
          developer.log('Health check failed: Invalid token',
              name: 'SessionService');
        }
        return false;
      }

      // Check 2: Session data integrity
      if (_currentState == SessionState.active && _currentSessions.isEmpty) {
        if (kDebugMode) {
          developer.log('Health check warning: Active state but no sessions',
              name: 'SessionService');
        }
      }

      // Check 3: Server connectivity (optional)
      try {
        final refreshResult = await _fetchSessionData();
        if (!refreshResult.success) {
          if (kDebugMode) {
            developer.log('Health check failed: Server connectivity issue',
                name: 'SessionService');
          }
          return false;
        }
      } catch (e) {
        if (kDebugMode) {
          developer.log('Health check failed: Server error: $e',
              name: 'SessionService');
        }
        return false;
      }

      // Check 4: Refresh capability
      final refreshToken = await _secureStorage.read('refresh_token');
      if (refreshToken == null) {
        if (kDebugMode) {
          developer.log('Health check warning: No refresh token available',
              name: 'SessionService');
        }
        // This is a warning, not a failure
      }

      if (kDebugMode) {
        developer.log('Session health check passed', name: 'SessionService');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        developer.log('Health check failed with exception: $e',
            name: 'SessionService');
      }
      return false;
    }
  }

  /// Disposes of the service and cleans up resources
  void dispose() {
    clearAutoRefresh();
    _sessionEventController.close();
    _refreshCompleters.clear();

    if (kDebugMode) {
      developer.log('SessionService disposed', name: 'SessionService');
    }
  }
}
