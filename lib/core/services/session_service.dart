import 'dart:async';
import '../../model/userInfo.dart';

/// Session state enumeration for tracking session lifecycle
enum SessionState {
  /// Session is active and valid
  active,
  
  /// Session has expired and needs refresh
  expired,
  
  /// Session is invalid and cannot be refreshed
  invalid,
  
  /// Session is in the process of being refreshed
  refreshing,
  
  /// No session exists
  none,
}

/// Session validation result containing validation status and details
class SessionValidationResult {
  final bool isValid;
  final SessionState state;
  final String? reason;
  final DateTime? expiryTime;
  final List<Session>? sessions;

  const SessionValidationResult({
    required this.isValid,
    required this.state,
    this.reason,
    this.expiryTime,
    this.sessions,
  });

  /// Creates a valid session result
  factory SessionValidationResult.valid({
    required List<Session> sessions,
    DateTime? expiryTime,
  }) {
    return SessionValidationResult(
      isValid: true,
      state: SessionState.active,
      sessions: sessions,
      expiryTime: expiryTime,
    );
  }

  /// Creates an invalid session result
  factory SessionValidationResult.invalid({
    required SessionState state,
    required String reason,
  }) {
    return SessionValidationResult(
      isValid: false,
      state: state,
      reason: reason,
    );
  }
}

/// Session refresh result containing refresh status and updated data
class SessionRefreshResult {
  final bool success;
  final String? errorMessage;
  final List<Session>? sessions;
  final DateTime? nextRefreshTime;

  const SessionRefreshResult({
    required this.success,
    this.errorMessage,
    this.sessions,
    this.nextRefreshTime,
  });

  /// Creates a successful refresh result
  factory SessionRefreshResult.success({
    required List<Session> sessions,
    DateTime? nextRefreshTime,
  }) {
    return SessionRefreshResult(
      success: true,
      sessions: sessions,
      nextRefreshTime: nextRefreshTime,
    );
  }

  /// Creates a failed refresh result
  factory SessionRefreshResult.failure(String errorMessage) {
    return SessionRefreshResult(
      success: false,
      errorMessage: errorMessage,
    );
  }
}

/// Session event types for notifications
enum SessionEventType {
  /// Session was started successfully
  sessionStarted,
  
  /// Session was refreshed
  sessionRefreshed,
  
  /// Session expired
  sessionExpired,
  
  /// Session was ended/terminated
  sessionEnded,
  
  /// Session validation failed
  sessionInvalid,
  
  /// Session refresh failed
  refreshFailed,
}

/// Session event data for notifications
class SessionEvent {
  final SessionEventType type;
  final SessionState state;
  final List<Session>? sessions;
  final String? message;
  final DateTime timestamp;

  SessionEvent({
    required this.type,
    required this.state,
    this.sessions,
    this.message,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();
}

/// Abstract interface for session management operations
/// 
/// This service provides a clean abstraction for all session-related operations
/// including lifecycle management, state tracking, timeout handling, and
/// integration with token refresh mechanisms. It follows clean architecture
/// principles by separating session business logic from implementation details.
abstract class SessionService {
  /// Stream of session events for UI updates and state management
  /// 
  /// This stream emits events whenever session state changes, allowing
  /// other parts of the application to react to session lifecycle events.
  Stream<SessionEvent> get sessionEvents;

  /// Current session state
  /// 
  /// Returns the current state of the session without performing any
  /// validation or refresh operations.
  SessionState get currentState;

  /// Current sessions list
  /// 
  /// Returns the currently cached sessions. May be empty if no sessions
  /// are available or if session data hasn't been loaded yet.
  List<Session> get currentSessions;

  /// Starts a new session by fetching session data
  /// 
  /// This method initializes a new session by:
  /// 1. Validating authentication state
  /// 2. Fetching session data from the server
  /// 3. Storing session information locally
  /// 4. Setting up automatic refresh timers
  /// 
  /// Returns: [SessionValidationResult] with session data and validation status
  /// Throws: [Exception] if session cannot be started
  Future<SessionValidationResult> startSession();

  /// Refreshes the current session data
  /// 
  /// This method updates session information by:
  /// 1. Checking current authentication state
  /// 2. Fetching updated session data from server
  /// 3. Updating local session cache
  /// 4. Resetting refresh timers
  /// 
  /// Parameters:
  /// - [force]: If true, forces refresh even if session appears valid
  /// 
  /// Returns: [SessionRefreshResult] with updated session data
  Future<SessionRefreshResult> refreshSession({bool force = false});

  /// Ends the current session
  /// 
  /// This method terminates the session by:
  /// 1. Clearing local session data
  /// 2. Canceling refresh timers
  /// 3. Emitting session ended event
  /// 4. Optionally performing server-side logout
  /// 
  /// Parameters:
  /// - [clearLocal]: If true, clears local session storage
  /// - [notifyServer]: If true, notifies server of session termination
  Future<void> endSession({
    bool clearLocal = true,
    bool notifyServer = false,
  });

  /// Validates the current session
  /// 
  /// This method checks session validity by:
  /// 1. Verifying authentication tokens
  /// 2. Checking session expiry
  /// 3. Validating session data integrity
  /// 
  /// Parameters:
  /// - [checkServer]: If true, validates against server state
  /// 
  /// Returns: [SessionValidationResult] with validation details
  Future<SessionValidationResult> validateSession({bool checkServer = false});

  /// Configures session timeout duration
  /// 
  /// Sets the duration after which sessions should be considered expired
  /// and require refresh. This affects automatic refresh scheduling.
  /// 
  /// Parameters:
  /// - [timeout]: Duration after which session expires
  void configureSessionTimeout(Duration timeout);

  /// Schedules automatic session refresh
  /// 
  /// Sets up a timer to automatically refresh session data before expiry.
  /// The refresh will be triggered based on token expiry and configured
  /// session timeout.
  /// 
  /// Parameters:
  /// - [refreshCallback]: Optional callback to execute after refresh
  void scheduleAutoRefresh({VoidCallback? refreshCallback});

  /// Clears scheduled automatic refresh
  /// 
  /// Cancels any pending automatic refresh timers. This should be called
  /// when session is ended or when manual refresh is performed.
  void clearAutoRefresh();

  /// Gets session data by client ID
  /// 
  /// Retrieves specific session information for a given client ID.
  /// Useful for managing multiple active sessions.
  /// 
  /// Parameters:
  /// - [clientId]: The client ID to search for
  /// 
  /// Returns: [Session] if found, null otherwise
  Session? getSessionByClientId(String clientId);

  /// Checks if session is expiring soon
  /// 
  /// Determines if the session will expire within a specified threshold.
  /// This can be used to trigger proactive refresh operations.
  /// 
  /// Parameters:
  /// - [threshold]: Time threshold for considering session as expiring
  /// 
  /// Returns: true if session expires within threshold
  bool isSessionExpiring({Duration threshold = const Duration(minutes: 5)});

  /// Performs session health check
  /// 
  /// Comprehensive check of session health including:
  /// 1. Token validity
  /// 2. Session data integrity
  /// 3. Server connectivity
  /// 4. Refresh capability
  /// 
  /// Returns: true if session is healthy and functional
  Future<bool> performHealthCheck();
}

/// Callback type for session refresh operations
typedef VoidCallback = void Function();
