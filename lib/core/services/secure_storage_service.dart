import 'dart:convert';
import 'dart:developer' as developer;

import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// Storage health status information
class StorageHealthStatus {
  final bool isHealthy;
  final String? issue;
  final Map<String, dynamic>? details;

  const StorageHealthStatus({
    required this.isHealthy,
    this.issue,
    this.details,
  });

  /// Creates a healthy status
  factory StorageHealthStatus.healthy() {
    return const StorageHealthStatus(isHealthy: true);
  }

  /// Creates an unhealthy status with issue description
  factory StorageHealthStatus.unhealthy(String issue, {Map<String, dynamic>? details}) {
    return StorageHealthStatus(
      isHealthy: false,
      issue: issue,
      details: details,
    );
  }
}

/// Abstract interface for secure storage operations
/// 
/// This service provides a clean abstraction over secure storage functionality,
/// making it reusable across the application for any secure data operations.
/// It wraps FlutterSecureStorage with additional features like health checks,
/// batch operations, and enhanced error handling.
abstract class SecureStorageService {
  /// Stores a string value securely
  /// 
  /// Parameters:
  /// - [key]: The key to store the value under
  /// - [value]: The string value to store
  /// 
  /// Throws: [Exception] if storage operation fails
  Future<void> write(String key, String value);

  /// Retrieves a string value from secure storage
  /// 
  /// Parameters:
  /// - [key]: The key to retrieve the value for
  /// 
  /// Returns: The stored value or null if not found
  Future<String?> read(String key);

  /// Deletes a value from secure storage
  /// 
  /// Parameters:
  /// - [key]: The key to delete
  Future<void> delete(String key);

  /// Stores a JSON-serializable object securely
  /// 
  /// This method automatically converts the object to JSON before storage.
  /// 
  /// Parameters:
  /// - [key]: The key to store the object under
  /// - [value]: The object to store (must be JSON-serializable)
  /// 
  /// Throws: [Exception] if serialization or storage fails
  Future<void> writeObject(String key, Map<String, dynamic> value);

  /// Retrieves and deserializes a JSON object from secure storage
  /// 
  /// Parameters:
  /// - [key]: The key to retrieve the object for
  /// 
  /// Returns: The deserialized object or null if not found
  /// Throws: [FormatException] if stored data is not valid JSON
  Future<Map<String, dynamic>?> readObject(String key);

  /// Stores multiple key-value pairs in a single operation
  /// 
  /// This method provides better performance when storing multiple values
  /// and ensures atomicity of the operation.
  /// 
  /// Parameters:
  /// - [data]: Map of key-value pairs to store
  /// 
  /// Throws: [Exception] if any storage operation fails
  Future<void> writeBatch(Map<String, String> data);

  /// Retrieves multiple values in a single operation
  /// 
  /// Parameters:
  /// - [keys]: List of keys to retrieve values for
  /// 
  /// Returns: Map of key-value pairs (missing keys will have null values)
  Future<Map<String, String?>> readBatch(List<String> keys);

  /// Deletes multiple keys in a single operation
  /// 
  /// Parameters:
  /// - [keys]: List of keys to delete
  Future<void> deleteBatch(List<String> keys);

  /// Checks if a key exists in secure storage
  /// 
  /// Parameters:
  /// - [key]: The key to check for existence
  /// 
  /// Returns: true if the key exists, false otherwise
  Future<bool> containsKey(String key);

  /// Gets all keys currently stored in secure storage
  /// 
  /// Returns: Set of all stored keys
  Future<Set<String>> getAllKeys();

  /// Clears all data from secure storage
  /// 
  /// This operation is irreversible and should be used with caution.
  Future<void> clear();

  /// Performs a health check on the secure storage
  /// 
  /// This method tests basic read/write operations to ensure the storage
  /// is functioning correctly.
  /// 
  /// Returns: [StorageHealthStatus] indicating storage health
  Future<StorageHealthStatus> checkHealth();

  /// Checks if the secure storage service is ready for use
  /// 
  /// Returns: true if the service is ready, false otherwise
  bool isReady();

  /// Gets the total number of stored items
  /// 
  /// Returns: Number of items currently stored
  Future<int> getItemCount();

  /// Exports all stored data (for backup purposes)
  /// 
  /// This method should be used carefully as it exposes all stored data.
  /// It's intended for backup and migration scenarios.
  /// 
  /// Returns: Map of all key-value pairs
  Future<Map<String, String>> exportAll();

  /// Imports data from a backup
  /// 
  /// This method clears existing data and imports the provided data.
  /// 
  /// Parameters:
  /// - [data]: Map of key-value pairs to import
  /// - [clearExisting]: Whether to clear existing data before import (default: true)
  Future<void> importData(Map<String, String> data, {bool clearExisting = true});
}

/// Concrete implementation of SecureStorageService using FlutterSecureStorage
class SecureStorageServiceImpl implements SecureStorageService {
  final FlutterSecureStorage _storage;
  bool _isReady = true;

  SecureStorageServiceImpl({
    required FlutterSecureStorage storage,
  }) : _storage = storage;

  @override
  Future<void> write(String key, String value) async {
    try {
      await _storage.write(key: key, value: value);
      if (kDebugMode) {
        developer.log('Stored value for key: $key', name: 'SecureStorage');
      }
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to store value for key $key: $e', name: 'SecureStorage');
      }
      _isReady = false;
      throw Exception('Failed to store secure data: $e');
    }
  }

  @override
  Future<String?> read(String key) async {
    try {
      final value = await _storage.read(key: key);
      if (kDebugMode && value != null) {
        developer.log('Retrieved value for key: $key', name: 'SecureStorage');
      }
      return value;
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to read value for key $key: $e', name: 'SecureStorage');
      }
      _isReady = false;
      return null;
    }
  }

  @override
  Future<void> delete(String key) async {
    try {
      await _storage.delete(key: key);
      if (kDebugMode) {
        developer.log('Deleted key: $key', name: 'SecureStorage');
      }
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to delete key $key: $e', name: 'SecureStorage');
      }
      // Don't throw for delete operations as they might be cleanup operations
    }
  }

  @override
  Future<void> writeObject(String key, Map<String, dynamic> value) async {
    try {
      final jsonString = json.encode(value);
      await write(key, jsonString);
    } catch (e) {
      throw Exception('Failed to store object for key $key: $e');
    }
  }

  @override
  Future<Map<String, dynamic>?> readObject(String key) async {
    try {
      final jsonString = await read(key);
      if (jsonString == null) return null;
      
      return json.decode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to parse object for key $key: $e', name: 'SecureStorage');
      }
      throw FormatException('Failed to parse stored object for key $key: $e');
    }
  }

  @override
  Future<void> writeBatch(Map<String, String> data) async {
    try {
      // FlutterSecureStorage doesn't have native batch operations,
      // so we'll use Future.wait for concurrent operations
      final futures = data.entries.map((entry) => 
        _storage.write(key: entry.key, value: entry.value)
      );
      
      await Future.wait(futures);
      
      if (kDebugMode) {
        developer.log('Stored ${data.length} items in batch', name: 'SecureStorage');
      }
    } catch (e) {
      if (kDebugMode) {
        developer.log('Batch write failed: $e', name: 'SecureStorage');
      }
      _isReady = false;
      throw Exception('Failed to store batch data: $e');
    }
  }

  @override
  Future<Map<String, String?>> readBatch(List<String> keys) async {
    try {
      final futures = keys.map((key) => 
        _storage.read(key: key).then((value) => MapEntry(key, value))
      );
      
      final results = await Future.wait(futures);
      return Map.fromEntries(results);
    } catch (e) {
      if (kDebugMode) {
        developer.log('Batch read failed: $e', name: 'SecureStorage');
      }
      _isReady = false;
      throw Exception('Failed to read batch data: $e');
    }
  }

  @override
  Future<void> deleteBatch(List<String> keys) async {
    try {
      final futures = keys.map((key) => _storage.delete(key: key));
      await Future.wait(futures);
      
      if (kDebugMode) {
        developer.log('Deleted ${keys.length} items in batch', name: 'SecureStorage');
      }
    } catch (e) {
      if (kDebugMode) {
        developer.log('Batch delete failed: $e', name: 'SecureStorage');
      }
      // Don't throw for delete operations
    }
  }

  @override
  Future<bool> containsKey(String key) async {
    try {
      return await _storage.containsKey(key: key);
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to check key existence for $key: $e', name: 'SecureStorage');
      }
      return false;
    }
  }

  @override
  Future<Set<String>> getAllKeys() async {
    try {
      final allData = await _storage.readAll();
      return allData.keys.toSet();
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to get all keys: $e', name: 'SecureStorage');
      }
      _isReady = false;
      return <String>{};
    }
  }

  @override
  Future<void> clear() async {
    try {
      await _storage.deleteAll();
      if (kDebugMode) {
        developer.log('Cleared all secure storage data', name: 'SecureStorage');
      }
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to clear storage: $e', name: 'SecureStorage');
      }
      throw Exception('Failed to clear secure storage: $e');
    }
  }

  @override
  Future<StorageHealthStatus> checkHealth() async {
    try {
      const testKey = '_health_check_test_key';
      const testValue = 'health_check_test_value';
      
      // Test write operation
      await _storage.write(key: testKey, value: testValue);
      
      // Test read operation
      final readValue = await _storage.read(key: testKey);
      if (readValue != testValue) {
        return StorageHealthStatus.unhealthy(
          'Read/write mismatch',
          details: {'expected': testValue, 'actual': readValue},
        );
      }
      
      // Test delete operation
      await _storage.delete(key: testKey);
      
      // Verify deletion
      final deletedValue = await _storage.read(key: testKey);
      if (deletedValue != null) {
        return StorageHealthStatus.unhealthy(
          'Delete operation failed',
          details: {'value_after_delete': deletedValue},
        );
      }
      
      _isReady = true;
      return StorageHealthStatus.healthy();
    } catch (e) {
      _isReady = false;
      return StorageHealthStatus.unhealthy(
        'Health check failed: $e',
        details: {'error': e.toString()},
      );
    }
  }

  @override
  bool isReady() => _isReady;

  @override
  Future<int> getItemCount() async {
    try {
      final allData = await _storage.readAll();
      return allData.length;
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to get item count: $e', name: 'SecureStorage');
      }
      return 0;
    }
  }

  @override
  Future<Map<String, String>> exportAll() async {
    try {
      return await _storage.readAll();
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to export data: $e', name: 'SecureStorage');
      }
      throw Exception('Failed to export secure storage data: $e');
    }
  }

  @override
  Future<void> importData(Map<String, String> data, {bool clearExisting = true}) async {
    try {
      if (clearExisting) {
        await clear();
      }
      
      await writeBatch(data);
      
      if (kDebugMode) {
        developer.log('Imported ${data.length} items', name: 'SecureStorage');
      }
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to import data: $e', name: 'SecureStorage');
      }
      throw Exception('Failed to import secure storage data: $e');
    }
  }
}
