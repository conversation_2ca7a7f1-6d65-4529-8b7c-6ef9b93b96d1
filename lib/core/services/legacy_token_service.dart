import 'dart:async';
import 'dart:convert';
import 'dart:developer' as developer;

import 'package:flutter/foundation.dart';

import 'token_service.dart';
import 'secure_storage_service.dart';
import '../../view_model/loginPostRequests.dart';

/// Legacy token service that wraps existing LoginPostRequests functionality
/// 
/// This service provides a bridge between the new token service interface
/// and the existing LoginPostRequests implementation. It allows for gradual
/// migration while maintaining backward compatibility.
/// 
/// This is a temporary implementation that will be replaced once the full
/// refactoring is complete.
class LegacyTokenService implements TokenService {
  final SecureStorageService _secureStorage;
  
  Timer? _refreshTimer;

  LegacyTokenService({
    required SecureStorageService secureStorage,
  }) : _secureStorage = secureStorage;

  @override
  TokenValidationResult validateToken(String token) {
    try {
      // Use existing validation logic from LoginPostRequests
      if (LoginPostRequests.isTokenExpired(token)) {
        return TokenValidationResult.invalid('Token is expired');
      }

      final expiryTime = LoginPostRequests.getExpiryTime(token);
      final payload = parseTokenPayload(token);

      return TokenValidationResult.valid(
        expiryTime: expiryTime,
        payload: payload,
      );
    } catch (e) {
      return TokenValidationResult.invalid('Token validation failed: $e');
    }
  }

  @override
  bool isTokenExpired(String token) {
    try {
      return LoginPostRequests.isTokenExpired(token);
    } catch (e) {
      return true;
    }
  }

  @override
  bool isTokenExpiring(String token, {Duration threshold = const Duration(minutes: 30)}) {
    try {
      return LoginPostRequests.isTokenExpiring(token);
    } catch (e) {
      return true;
    }
  }

  @override
  DateTime getTokenExpiryTime(String token) {
    return LoginPostRequests.getExpiryTime(token);
  }

  @override
  Map<String, dynamic> parseTokenPayload(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) {
        throw const FormatException('Invalid JWT format');
      }

      // Get the payload part (middle part)
      String payloadBase64 = parts[1];
      
      // Normalize base64 padding
      payloadBase64 = base64.normalize(payloadBase64);
      
      // Decode base64
      final decodedBytes = base64.decode(payloadBase64);
      final decodedString = utf8.decode(decodedBytes);
      
      // Parse JSON
      return json.decode(decodedString) as Map<String, dynamic>;
    } catch (e) {
      throw FormatException('Failed to parse token payload: $e');
    }
  }

  @override
  Future<TokenRefreshResult> refreshTokens(String refreshToken) async {
    try {
      // Use existing refresh logic from LoginPostRequests
      final newAccessToken = await LoginPostRequests.getNewAccessToken();
      final newRefreshToken = await _secureStorage.read('refresh_token');
      
      if (newRefreshToken == null) {
        return TokenRefreshResult.failure('Failed to get new refresh token');
      }

      return TokenRefreshResult.success(
        accessToken: newAccessToken,
        refreshToken: newRefreshToken,
      );
    } catch (e) {
      return TokenRefreshResult.failure('Token refresh failed: $e');
    }
  }

  @override
  Future<String> getValidAccessToken() async {
    try {
      // Use existing logic from LoginPostRequests
      return await LoginPostRequests.getAccessToken2() ?? '';
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to get valid access token: $e', name: 'LegacyTokenService');
      }
      rethrow;
    }
  }

  @override
  void scheduleTokenRefresh(String token, Function() onRefresh) {
    try {
      clearScheduledRefresh();
      
      if (isTokenExpiring(token, threshold: const Duration(minutes: 10))) {
        // If token expires in less than 10 minutes, refresh immediately
        onRefresh();
        return;
      }

      final expiryTime = getTokenExpiryTime(token);
      final refreshTime = expiryTime.subtract(const Duration(minutes: 10));
      final delay = refreshTime.difference(DateTime.now());

      if (delay.isNegative) {
        // Should refresh immediately
        onRefresh();
        return;
      }

      _refreshTimer = Timer(delay, () {
        if (kDebugMode) {
          developer.log('Scheduled token refresh triggered', name: 'LegacyTokenService');
        }
        onRefresh();
      });

      if (kDebugMode) {
        developer.log('Token refresh scheduled for ${refreshTime.toIso8601String()}', name: 'LegacyTokenService');
      }
    } catch (e) {
      if (kDebugMode) {
        developer.log('Failed to schedule token refresh: $e', name: 'LegacyTokenService');
      }
    }
  }

  @override
  void clearScheduledRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  @override
  bool isServiceReady() {
    return _secureStorage.isReady();
  }

  /// Dispose method to clean up resources
  void dispose() {
    clearScheduledRefresh();
  }
}
