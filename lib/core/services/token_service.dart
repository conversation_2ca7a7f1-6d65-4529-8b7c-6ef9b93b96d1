import 'dart:convert';

/// Token validation result containing validation status and details
class TokenValidationResult {
  final bool isValid;
  final String? reason;
  final DateTime? expiryTime;
  final Map<String, dynamic>? payload;

  const TokenValidationResult({
    required this.isValid,
    this.reason,
    this.expiryTime,
    this.payload,
  });

  /// Creates a valid token result
  factory TokenValidationResult.valid({
    required DateTime expiryTime,
    required Map<String, dynamic> payload,
  }) {
    return TokenValidationResult(
      isValid: true,
      expiryTime: expiryTime,
      payload: payload,
    );
  }

  /// Creates an invalid token result
  factory TokenValidationResult.invalid(String reason) {
    return TokenValidationResult(
      isValid: false,
      reason: reason,
    );
  }
}

/// Token refresh result containing new tokens or error information
class TokenRefreshResult {
  final bool success;
  final String? accessToken;
  final String? refreshToken;
  final String? error;

  const TokenRefreshResult({
    required this.success,
    this.accessToken,
    this.refreshToken,
    this.error,
  });

  /// Creates a successful refresh result
  factory TokenRefreshResult.success({
    required String accessToken,
    required String refreshToken,
  }) {
    return TokenRefreshResult(
      success: true,
      accessToken: accessToken,
      refreshToken: refreshToken,
    );
  }

  /// Creates a failed refresh result
  factory TokenRefreshResult.failure(String error) {
    return TokenRefreshResult(
      success: false,
      error: error,
    );
  }
}

/// Abstract interface for token management operations
/// 
/// This service provides a clean abstraction for all token-related operations
/// including validation, parsing, refresh logic, and lifecycle management.
/// It follows clean architecture principles by separating token business logic
/// from implementation details.
abstract class TokenService {
  /// Validates a JWT token and returns detailed validation result
  /// 
  /// This method performs comprehensive token validation including:
  /// - Format validation (proper JWT structure)
  /// - Expiration checking
  /// - Payload parsing and validation
  /// 
  /// Parameters:
  /// - [token]: The JWT token to validate
  /// 
  /// Returns: [TokenValidationResult] with validation status and details
  TokenValidationResult validateToken(String token);

  /// Checks if a token is expired
  /// 
  /// Parameters:
  /// - [token]: The JWT token to check
  /// 
  /// Returns: true if the token is expired, false otherwise
  bool isTokenExpired(String token);

  /// Checks if a token is expiring soon (within a threshold)
  /// 
  /// This method is useful for proactive token refresh to avoid
  /// authentication failures due to token expiration.
  /// 
  /// Parameters:
  /// - [token]: The JWT token to check
  /// - [threshold]: Duration before expiry to consider as "expiring"
  ///   (defaults to 30 minutes)
  /// 
  /// Returns: true if the token expires within the threshold, false otherwise
  bool isTokenExpiring(String token, {Duration threshold = const Duration(minutes: 30)});

  /// Extracts the expiry time from a JWT token
  /// 
  /// Parameters:
  /// - [token]: The JWT token to parse
  /// 
  /// Returns: [DateTime] representing when the token expires
  /// Throws: [FormatException] if the token is malformed
  DateTime getTokenExpiryTime(String token);

  /// Parses a JWT token and extracts its payload
  /// 
  /// This method decodes the JWT payload and returns it as a Map.
  /// It handles base64 decoding and JSON parsing.
  /// 
  /// Parameters:
  /// - [token]: The JWT token to parse
  /// 
  /// Returns: Map containing the token payload
  /// Throws: [FormatException] if the token is malformed
  Map<String, dynamic> parseTokenPayload(String token);

  /// Attempts to refresh tokens using the provided refresh token
  /// 
  /// This method handles the token refresh flow by calling the appropriate
  /// API endpoint and returning the new tokens or error information.
  /// 
  /// Parameters:
  /// - [refreshToken]: The refresh token to use for getting new tokens
  /// 
  /// Returns: [TokenRefreshResult] containing new tokens or error details
  Future<TokenRefreshResult> refreshTokens(String refreshToken);

  /// Gets a valid access token, refreshing if necessary
  /// 
  /// This method implements automatic token refresh logic:
  /// 1. Checks if current token is valid
  /// 2. If expired or expiring, attempts refresh
  /// 3. Returns valid token or throws exception
  /// 
  /// Returns: A valid access token
  /// Throws: [Exception] if unable to get a valid token
  Future<String> getValidAccessToken();

  /// Schedules automatic token refresh for tokens that are expiring soon
  /// 
  /// This method can be used to proactively refresh tokens in the background
  /// to ensure seamless user experience.
  /// 
  /// Parameters:
  /// - [token]: The token to monitor for expiration
  /// - [onRefresh]: Callback function called when refresh is triggered
  void scheduleTokenRefresh(String token, Function() onRefresh);

  /// Clears any scheduled token refresh operations
  void clearScheduledRefresh();

  /// Checks if the token service is properly configured and ready to use
  /// 
  /// This method can be used for health checks and debugging.
  /// 
  /// Returns: true if the service is ready, false otherwise
  bool isServiceReady();
}
