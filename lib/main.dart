import 'package:device_preview/device_preview.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:water_metering/utils/excel_helpers.dart';
import '../theme/theme2.dart';
import '../views/pages/DashboardPage2.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'bloc/auth/auth_bloc.dart';
import 'bloc/auth/auth_event.dart';
import 'bloc/auth/auth_state.dart';
import 'config.dart';
import 'view_model/loginPostRequests.dart';
import 'views/pages/LoginPage2.dart';
import 'core/di/injection_container.dart' as di;

final mainNavigatorKey = GlobalKey<NavigatorState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize dependency injection container
  await di.init();

  // await ExcelHelper.deleteOldExportFiles();
  final themeProvider = ThemeNotifier();
  await themeProvider.readThemeMode();

  // Initialize AuthBloc for state restoration
  final authBloc = di.sl<AuthBloc>();

  // Trigger state restoration on app start
  authBloc.add(AuthStateRestoreRequested());

  runApp(
    ChangeNotifierProvider(
      create: (_) => themeProvider,
      child: BlocProvider<AuthBloc>.value(
        value: authBloc,
        child: const MyApp(),
      ),
      // child: DevicePreview(
      //   enabled: !kReleaseMode,
      //   builder: (context) => const MyApp(), // Wrap your app
      // ),
    ),
  );
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  initState() {
    super.initState();

    // Set preferred orientations
    SystemChrome.setPreferredOrientations(
        [DeviceOrientation.portraitDown, DeviceOrientation.portraitUp]);

    // Trigger authentication state restoration
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AuthBloc>().add(AuthStateRestoreRequested());
    });
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
        designSize: const Size(430, 881.55),
        minTextAdapt: true,
        splitScreenMode: true,
        // Use builder only if you need to use library outside ScreenUtilInit context
        builder: (_, child) {
          // print("Splash color: ");
          // print(Theme.of(context).splashColor);
          return MaterialApp(
            // showPerformanceOverlay: true,
            home: child,
          );
        },
        child: MaterialApp(
          title: 'Meter Config',
          debugShowCheckedModeBanner: false,
          // useInheritedMediaQuery: true,
          // locale: DevicePreview.locale(context), // Ensures correct locale
          // builder: DevicePreview.appBuilder, // Important to wrap the whole app
          builder: (context, child) {
            return MediaQuery(
              child: child!,
              data: MediaQuery.of(context)
                  .copyWith(textScaler: TextScaler.linear(1.0)),
            );
          },
          navigatorKey: mainNavigatorKey,
          home: const AuthStateRouter(),
          routes: {
            '/login': (context) => const LoginPage(),
            '/homePage': (context) => const DashboardPage(),
          },
          // onGenerateRoute: (settings) {
          //   if (settings.name == "/dashboard") {
          //     print("Going to dashboard page>??");
          //     return MaterialPageRoute(
          //       builder: (context) {
          //         return BlocProvider<DashboardBloc>(
          //             create: (context) => DashboardBloc(),
          //             child: const DashboardPage()
          //         );
          //       },
          //     );
          //   }
          //   return null;
          // }
        ));
  }
}

/// Widget that handles routing based on authentication state
///
/// This widget listens to AuthBloc state changes and routes the user
/// to the appropriate screen based on their authentication status.
/// It replaces the legacy LoginPostRequests.isLoggedIn routing logic.
class AuthStateRouter extends StatelessWidget {
  const AuthStateRouter({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        // Handle test configuration
        if (ConfigurationCustom.isTest) {
          return ConfigurationCustom.testScreen;
        }

        // Route based on authentication state
        switch (state.runtimeType) {
          case AuthAuthenticated:
            return const DashboardPage();
          case AuthUnauthenticated:
          case AuthError:
            return const LoginPage();
          case AuthInitial:
          case AuthLoading:
          default:
            // Show loading screen while determining auth state
            return const Scaffold(
              body: Center(
                child: CircularProgressIndicator(),
              ),
            );
        }
      },
    );
  }
}
