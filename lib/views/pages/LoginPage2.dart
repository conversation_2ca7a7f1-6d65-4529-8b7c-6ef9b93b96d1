import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:water_metering/utils/pok.dart';

import '../../bloc/auth/auth.dart';
import '../../changeNotifiers/NudronChangeNotifiers.dart';
import '../../config.dart';

import '../../presentation/widgets/auth/auth_error_display.dart';
import '../../presentation/widgets/auth/auth_button.dart';
import '../../presentation/widgets/auth/biometric_button.dart';
import '../../presentation/widgets/auth/login_form.dart';
import '../../theme/theme2.dart';
import '../../utils/alert_message.dart';
import '../../utils/biometric_helper.dart';
import '../../utils/misc_functions.dart';
import '../../utils/new_loader.dart';
import '../../utils/toggle_button.dart';
import '../../view_model/loginPostRequests.dart';
import '../table/fields/ChamferedTextWidget.dart';
import '../widgets/containers/CustomAppBar.dart';
import '../widgets/containers/PasswordController.dart';
import '../widgets/containers/customButton.dart';
import 'EnterTwoFacCode.dart';
import 'RegisterPage.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  @override
  void initState() {
    SystemChrome.setPreferredOrientations(
        [DeviceOrientation.portraitDown, DeviceOrientation.portraitUp]);
    // SystemChrome.setPreferredOrientations(
    //     [DeviceOrientation.landscapeLeft, DeviceOrientation.landscapeRight]);
    NudronRandomStuff.isSignIn.addListener(() {
      setState(() {});
    });
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   _showEmailConfirmationDialog(context);
    // });
    super.initState();
  }

  // Removed pages list - using conditional rendering instead

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthLoading) {
          // Show loading indicator if needed
        } else if (state is AuthAuthenticated) {
          CustomAlert.showCustomScaffoldMessenger(
            context,
            "Successfully logged in!",
            AlertType.success,
          );
          // Navigation is handled automatically by AuthStateRouter
        } else if (state is AuthTwoFactorRequired) {
          CustomAlert.showCustomScaffoldMessenger(
            context,
            "Please enter the code sent to your authenticator app/sms",
            AlertType.info,
          );
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => EnterTwoFacCode(
                referenceCode: state.refCode,
              ),
            ),
          );
        } else if (state is AuthError) {
          AuthErrorHandler.showError(context, state.failure);
        }
      },
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: SafeArea(
          child: Scaffold(
            appBar: CustomAppBar(
              choiceAction: null,
            ),
            backgroundColor:
                Provider.of<ThemeNotifier>(context).currentTheme.bgColor,
            resizeToAvoidBottomInset: false,
            // Prevents resize when the keyboard appears
            body: SizedBox(
              // color:Colors.green,
              height: 1.sh - 51.h,
              width: 1.sw,
              child: Stack(
                children: [
                  Align(
                    alignment: Alignment.bottomCenter,
                    // Keeps the icon at the bottom center
                    child: Transform.rotate(
                      angle: 0,
                      child: SvgPicture.asset(
                        'assets/icons/nfcicon.svg',
                        fit: BoxFit.cover,
                        clipBehavior: Clip.hardEdge,
                        color: CommonColors.blue.withOpacity(0.25),
                        width: 450.minSp,
                      ),
                    ),
                  ),
                  Scaffold(
                    backgroundColor: Colors.transparent,
                    resizeToAvoidBottomInset: true,
                    body: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            height: 3.h,
                            color: CommonColors.blue,
                          ),
                          SizedBox(height: 20.h),
                          Center(
                            child: Text(
                              'Nudron IoT Solutions',
                              style: GoogleFonts.roboto(
                                  fontSize: 37.minSp,
                                  fontWeight: FontWeight.bold,
                                  color: Provider.of<ThemeNotifier>(context)
                                      .currentTheme
                                      .loginTitleColor),
                            ),
                          ),
                          SizedBox(height: 10.h),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16.w),
                            child: Center(
                              child: Container(
                                child: Text(
                                    "Welcome to Nudron's Water Metering Dashboard",
                                    textAlign: TextAlign.center,
                                    style: GoogleFonts.roboto(
                                        fontSize: ThemeNotifier.medium.minSp,
                                        color:
                                            Provider.of<ThemeNotifier>(context)
                                                .currentTheme
                                                .basicAdvanceTextColor)),
                              ),
                            ),
                          ),
                          SizedBox(height: 20.h),
                          Center(
                            child: ToggleButtonCustom(
                              key: UniqueKey(),
                              tabs: const ["SIGN IN", "REGISTER"],
                              backgroundColor: null,
                              selectedTextColor: Colors.white,
                              unselectedTextColor:
                                  Provider.of<ThemeNotifier>(context)
                                      .currentTheme
                                      .basicAdvanceTextColor,
                              index: NudronRandomStuff.isSignIn.value ? 0 : 1,
                              onTap: (index) {
                                setState(() {
                                  NudronRandomStuff.isSignIn.value = index == 0;
                                });
                              },
                            ),
                          ),
                          SizedBox(height: 40.h),
                          // Use conditional rendering instead of IndexedStack
                          // to prevent unnecessary builds of RegisterPage
                          NudronRandomStuff.isSignIn.value
                              ? const SigninPage()
                              : const RegisterPage(),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class SigninPage extends StatefulWidget {
  const SigninPage({super.key});

  @override
  State<SigninPage> createState() => _SigninPageState();
}

class _SigninPageState extends State<SigninPage> {
  void _showEmailConfirmationDialog(BuildContext context) async {
    showDialog(
      context: context,
      barrierDismissible: false,
      // Prevents the dialog from being dismissed by tapping outside
      builder: (BuildContext dialogContext) {
        return AutoLogin(email: emailBiometricSaved!);
      },
    );
  }

  bool isLargerTextField = ConfigurationCustom.isLargerTextField;

  TextEditingController emailController = TextEditingController();

  var passwordControllerObscure = ObscuringTextEditingController();

  bool openForgotPasswordButtons = false;
  double scale = 1.2;

  // Key for accessing LoginForm methods
  final GlobalKey<LoginFormState> _loginFormKey = GlobalKey<LoginFormState>();

  String getPassword() {
    return passwordControllerObscure.getText();
  }

  @override
  void initState() {
    super.initState();
  }

  String? emailBiometricSaved;

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          // Use LoginForm component for email and password fields
          LoginForm(
            key: _loginFormKey,
            emailController: emailController,
            passwordController: passwordControllerObscure,
            onLoginRequested: (email, password) {
              // Handle login through AuthBloc
              context.read<AuthBloc>().add(
                    AuthLoginRequested(email, password),
                  );
            },
          ),
          Visibility(
            visible: openForgotPasswordButtons,
            child: Padding(
              padding: EdgeInsets.only(left: 35.w, right: 35.w),
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 5),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "Forgot Password?",
                          style: GoogleFonts.roboto(
                              fontSize: ThemeNotifier.medium.minSp,
                              fontWeight: FontWeight.w500,
                              color: CommonColors.red),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 5),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomButton(
                        width: 130.w,
                        text: "CANCEL",
                        isRed: true,
                        onPressed: () {
                          setState(() {
                            openForgotPasswordButtons = false;
                          });
                        },
                      ),
                      CustomButton(
                        width: 130.w,
                        text: "SEND EMAIL",
                        onPressed: () async {
                          LoaderUtility.showLoader(
                                  context,
                                  LoginPostRequests.forgotPassword(
                                      emailController.text))
                              .then((s) {
                            CustomAlert.showCustomScaffoldMessenger(
                                context,
                                "Temporary password sent to your email",
                                AlertType.info);

                            setState(() {
                              openForgotPasswordButtons = false;
                            });
                          }).catchError((e) {
                            CustomAlert.showCustomScaffoldMessenger(
                                context, e.toString(), AlertType.error);
                          });
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          LayoutBuilder(builder: (context, constraints) {
            return Visibility(
              visible: !openForgotPasswordButtons,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    children: [
                      // Forgot Password link
                      Align(
                        alignment: Alignment.centerRight,
                        child: Container(
                          // color: Colors.green,
                          child: Padding(
                            padding: EdgeInsets.only(right: 35.w, top: 25.h),
                            child: GestureDetector(
                              onTap: () async {
                                if (emailController.text.isNotEmpty) {
                                  final bool emailValid =
                                      MiscellaneousFunctions.isEmailValid(
                                          emailController.text);
                                  if (emailValid == false) {
                                    CustomAlert.showCustomScaffoldMessenger(
                                        context,
                                        "Please enter a valid email",
                                        AlertType.warning);
                                    return;
                                  } else {
                                    setState(() {
                                      openForgotPasswordButtons = true;
                                    });
                                  }
                                } else {
                                  CustomAlert.showCustomScaffoldMessenger(
                                      context,
                                      "Please enter an email",
                                      AlertType.warning);
                                }
                              },
                              child: Text(
                                "Forgot Password?",
                                style: GoogleFonts.roboto(
                                    fontSize: ThemeNotifier.medium.minSp,
                                    decoration: TextDecoration.underline,
                                    decorationColor: CommonColors.red,
                                    fontWeight: FontWeight.w500,
                                    color: CommonColors.red),
                              ),
                            ),
                          ),
                        ),
                      ),
                      Container(height: 40.h),
                      Center(
                        child: BlocBuilder<AuthBloc, AuthState>(
                          builder: (context, state) {
                            return AuthButton(
                              text: "SIGN IN",
                              isLoading: state is AuthLoading,
                              onPressed: () async {
                                FocusScope.of(context).unfocus();

                                // Use LoginForm's submit method which handles validation
                                _loginFormKey.currentState?.submit();
                              },
                            );
                          },
                        ),
                      ),
                      Container(height: 40.h),
                    ],
                  ),
                  // Biometric authentication section
                  Column(
                    children: [
                      BiometricButton(
                        reason: "Login with biometric authentication",
                        onSuccess: () async {
                          // Get saved biometric email and show confirmation dialog
                          emailBiometricSaved =
                              await BiometricHelper.isBiometricEnabled();
                          if (emailBiometricSaved != null &&
                              emailBiometricSaved!.isNotEmpty) {
                            _showEmailConfirmationDialog(context);
                          }
                        },
                        onError: (error) {
                          CustomAlert.showCustomScaffoldMessenger(
                            context,
                            error,
                            AlertType.error,
                          );
                        },
                      ),
                      SizedBox(height: 20.h),
                    ],
                  )
                ],
              ),
            );
          }),
          // SizedBox(height: 38.h),
        ],
      ),
    );
  }
}

class AutoLogin extends StatefulWidget {
  final String email;

  const AutoLogin({super.key, required this.email});

  @override
  State<AutoLogin> createState() => _AutoLoginState();
}

class _AutoLoginState extends State<AutoLogin> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(0.0),
      ),
      backgroundColor:
          Provider.of<ThemeNotifier>(context).currentTheme.dialogBG,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color:
                Provider.of<ThemeNotifier>(context).currentTheme.gridLineColor,
            width: 3.minSp,
          ),
        ),
        width: 350.w,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ChamferedTextWidget2(
                  text: "  AUTO LOGIN  ",
                  borderColor: Provider.of<ThemeNotifier>(context)
                      .currentTheme
                      .gridLineColor,
                ),
                IconButton(
                  icon: Icon(
                    Icons.close,
                    color: Provider.of<ThemeNotifier>(context)
                        .currentTheme
                        .gridLineColor,
                  ),
                  onPressed: () {
                    if (mounted) {
                      Navigator.of(context).pop();
                    }
                  },
                ),
              ],
            ),
            SizedBox(height: 20.h),
            Center(
              child: Text(
                "WELCOME BACK! \nLOGIN AS ${widget.email}?",
                textAlign: TextAlign.center,
                style: GoogleFonts.robotoMono(
                  color: Provider.of<ThemeNotifier>(context)
                      .currentTheme
                      .basicAdvanceTextColor,
                  fontSize: ThemeNotifier.small.minSp,
                ),
              ),
            ),
            SizedBox(height: 20.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                CustomButton(
                  text: "NO",
                  isRed: true,
                  onPressed: () {
                    if (mounted) {
                      Navigator.of(context).pop();
                    }
                  },
                ),
                CustomButton(
                  text: "YES",
                  onPressed: () async {
                    // Initiating biometric authentication
                    BiometricHelper biometricHelper = BiometricHelper();

                    if (!await biometricHelper.isBiometricSetup()) {
                      CustomAlert.showCustomScaffoldMessenger(
                        context,
                        "Biometric authentication not available. Please enable biometrics on your device first.",
                        AlertType.error,
                      );
                      Navigator.of(context).pop();
                      return;
                    }

                    bool isCorrectBiometric =
                        await biometricHelper.isCorrectBiometric();

                    if (!isCorrectBiometric) {
                      CustomAlert.showCustomScaffoldMessenger(
                        context,
                        "Biometric authentication failed",
                        AlertType.error,
                      );
                      Navigator.of(context).pop();
                      return;
                    }

                    // Use AuthBloc for biometric login
                    Navigator.of(context).pop(); // Close the dialog first
                    context.read<AuthBloc>().add(
                          AuthBiometricLoginRequested(widget.email),
                        );
                  },
                ),
              ],
            ),
            SizedBox(height: 20.h),
          ],
        ),
      ),
    );
  }
}
