import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:water_metering/utils/pok.dart';

import '../../bloc/auth/auth.dart';
import '../../presentation/widgets/auth/auth_error_display.dart';
import '../../presentation/widgets/two_factor_input.dart';
import '../../theme/theme2.dart';
import '../../utils/alert_message.dart';
import '../widgets/containers/CustomAppBar.dart';
import '../widgets/containers/customButton.dart';

class EnterTwoFacCode extends StatefulWidget {
  const EnterTwoFacCode({super.key, required this.referenceCode});

  final String referenceCode;

  @override
  State<EnterTwoFacCode> createState() => _EnterTwoFacCodeState();
}

class _EnterTwoFacCodeState extends State<EnterTwoFacCode> {
  final TextEditingController _otpFieldController = TextEditingController();
  String? _errorText;

  @override
  void initState() {
    super.initState();
    SystemChrome.setPreferredOrientations(
        [DeviceOrientation.portraitDown, DeviceOrientation.portraitUp]);
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthAuthenticated) {
          CustomAlert.showCustomScaffoldMessenger(
            context,
            "Successfully logged in! Redirecting to home page...",
            AlertType.success,
          );
          Navigator.of(context).pushNamedAndRemoveUntil("/", (route) => false);
        } else if (state is AuthError) {
          AuthErrorHandler.showError(context, state.failure);
        }
      },
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: SafeArea(
          child: Scaffold(
            resizeToAvoidBottomInset: true,
            backgroundColor:
                Provider.of<ThemeNotifier>(context).currentTheme.bgColor,
            appBar: CustomAppBar(choiceAction: null),
            body: SingleChildScrollView(
              child: Center(
                child: Column(
                  children: [
                    Container(
                      height: 3.h,
                      color: CommonColors.blue,
                    ),
                    Align(
                      alignment: Alignment.topLeft,
                      child: Container(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            IconButton(
                              icon: Icon(Icons.arrow_back,
                                  color: Provider.of<ThemeNotifier>(context)
                                      .currentTheme
                                      .basicAdvanceTextColor),
                              onPressed: () => Navigator.of(context).pop(),
                            ),
                            SizedBox(width: 10.w),
                            Text(
                              "TWO-FACTOR AUTHENTICATION",
                              style: GoogleFonts.robotoMono(
                                color: Provider.of<ThemeNotifier>(context)
                                    .currentTheme
                                    .basicAdvanceTextColor,
                                fontSize: ThemeNotifier.large.minSp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 24.w),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          SizedBox(height: 40.h),
                          SvgPicture.asset(
                            'assets/images/2falogo.svg',
                            width: min(width / 2.5, height / 2.5),
                            colorFilter: const ColorFilter.mode(
                              CommonColors.blue,
                              BlendMode.srcIn,
                            ),
                          ),
                          SizedBox(height: 40.h),
                          Text(
                            "PLEASE ENTER THE CODE SENT TO YOUR\nAUTHENTICATOR APP/SMS",
                            textAlign: TextAlign.center,
                            style: GoogleFonts.robotoMono(
                              color: Provider.of<ThemeNotifier>(context)
                                  .currentTheme
                                  .basicAdvanceTextColor,
                              fontSize: ThemeNotifier.small.minSp,
                            ),
                          ),
                          SizedBox(height: 25.h),
                          TwoFactorInput(
                            controller: _otpFieldController,
                            errorText: _errorText,
                            onCompleted: (code) {
                              if (kDebugMode) {
                                print("Two-factor code completed: $code");
                              }
                              _clearError();
                            },
                            onChanged: (value) {
                              if (_errorText != null) {
                                _clearError();
                              }
                            },
                          ),
                          SizedBox(height: 40.h),
                          SizedBox(
                            // width: double.infinity,
                            child: CustomButton(
                              text: "VERIFY",
                              onPressed: () async {
                                final code = _otpFieldController.text;
                                final validationError =
                                    TwoFactorInputValidator.validateCode(code);

                                if (validationError != null) {
                                  setState(() {
                                    _errorText = validationError;
                                  });
                                  return;
                                }

                                // Use AuthBloc for two-factor verification
                                context.read<AuthBloc>().add(
                                      AuthTwoFactorSubmitted(
                                        widget.referenceCode,
                                        code,
                                      ),
                                    );
                              },
                            ),
                          ),
                          SizedBox(height: 100.h),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Clears the error text and updates the UI
  void _clearError() {
    if (_errorText != null) {
      setState(() {
        _errorText = null;
      });
    }
  }

  @override
  void dispose() {
    _otpFieldController.dispose();
    super.dispose();
  }
}
