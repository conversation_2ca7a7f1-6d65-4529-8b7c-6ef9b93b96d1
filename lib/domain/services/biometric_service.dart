import 'package:local_auth/local_auth.dart';
import '../../core/error/auth_failures.dart';

/// Domain service interface for biometric authentication operations
///
/// This service defines the contract for biometric authentication functionality
/// following clean architecture principles. It serves as the boundary between
/// the domain layer and the data layer for biometric operations.
///
/// All implementations of this service should handle:
/// - Platform-specific biometric authentication (iOS Face ID/Touch ID, Android fingerprint/face unlock)
/// - Biometric availability checking
/// - Biometric enrollment status
/// - Secure biometric authentication flows
/// - Error handling and mapping to domain failures
abstract class BiometricService {
  /// Checks if biometric authentication is available on the device
  ///
  /// This method verifies:
  /// - Device hardware support for biometric authentication
  /// - Operating system support for biometric APIs
  /// - Biometric authentication framework availability
  ///
  /// Returns true if biometric authentication is supported by the device,
  /// false otherwise.
  ///
  /// This method should not throw exceptions and should return false
  /// for any error conditions.
  Future<bool> isAvailable();

  /// Checks if biometric authentication is set up and enrolled on the device
  ///
  /// This method verifies:
  /// - Biometric authentication is available (calls isAvailable internally)
  /// - At least one biometric (fingerprint, face, etc.) is enrolled
  /// - Biometric authentication is ready to use
  ///
  /// Returns true if biometric authentication is set up and ready to use,
  /// false otherwise.
  ///
  /// This method should not throw exceptions and should return false
  /// for any error conditions.
  Future<bool> isEnrolled();

  /// Authenticates the user using biometric authentication
  ///
  /// This method performs biometric authentication with the following behavior:
  /// - Presents the platform-specific biometric authentication UI
  /// - Uses the provided reason text in the authentication prompt
  /// - Handles platform-specific authentication options
  /// - Returns authentication result
  ///
  /// Parameters:
  /// - [reason]: Localized reason text displayed to the user during authentication
  ///
  /// Returns [BiometricAuthResult] containing:
  /// - Success if biometric authentication succeeds
  /// - Failure with specific error type if authentication fails
  ///
  /// Possible failure types:
  /// - BiometricNotAvailableFailure: Device doesn't support biometrics
  /// - BiometricNotEnrolledFailure: No biometrics are enrolled
  /// - BiometricAuthenticationFailure: Authentication failed (wrong biometric)
  /// - BiometricLockedFailure: Biometric authentication is locked
  /// - BiometricCancelledFailure: User cancelled authentication
  /// - BiometricSystemFailure: System error during authentication
  Future<BiometricAuthResult> authenticate({
    required String reason,
  });

  /// Gets the list of available biometric types on the device
  ///
  /// This method returns the specific biometric authentication methods
  /// available on the current device (e.g., fingerprint, face, iris).
  ///
  /// Returns a list of [BiometricType] values representing the available
  /// biometric authentication methods. Returns an empty list if no
  /// biometric methods are available.
  ///
  /// This method should not throw exceptions and should return an empty
  /// list for any error conditions.
  Future<List<BiometricType>> getAvailableBiometrics();

  /// Checks if biometric authentication is enabled for the current user
  ///
  /// This method checks the user's preference for biometric authentication
  /// stored in secure storage. This is separate from device enrollment
  /// and represents the user's choice to use biometric authentication
  /// for this application.
  ///
  /// Returns true if the user has enabled biometric authentication
  /// for this application, false otherwise.
  ///
  /// This method should not throw exceptions and should return false
  /// for any error conditions.
  Future<bool> isBiometricEnabled();

  /// Sets the user's preference for biometric authentication
  ///
  /// This method stores the user's choice to enable or disable biometric
  /// authentication for this application in secure storage.
  ///
  /// Parameters:
  /// - [enabled]: Whether to enable biometric authentication for the user
  ///
  /// Returns [BiometricSettingsResult] containing:
  /// - Success if the setting was saved successfully
  /// - Failure if there was an error saving the setting
  ///
  /// This operation should be atomic and either succeed completely or fail
  /// without partial state changes.
  Future<BiometricSettingsResult> setBiometricEnabled(bool enabled);

  /// Checks if the device supports biometric authentication and has enrolled biometrics
  ///
  /// This is a convenience method that combines [isAvailable] and [isEnrolled]
  /// checks to determine if biometric authentication can be used.
  ///
  /// Returns true if biometric authentication is both available and enrolled,
  /// false otherwise.
  ///
  /// This method should not throw exceptions and should return false
  /// for any error conditions.
  Future<bool> canUseBiometric();
}

/// Result of a biometric authentication operation
class BiometricAuthResult {
  /// Whether the authentication was successful
  final bool success;

  /// Authentication failure (if authentication failed)
  final AuthFailure? failure;

  const BiometricAuthResult._({
    required this.success,
    this.failure,
  });

  /// Creates a successful biometric authentication result
  factory BiometricAuthResult.success() {
    return const BiometricAuthResult._(success: true);
  }

  /// Creates a failed biometric authentication result
  factory BiometricAuthResult.failure(AuthFailure failure) {
    return BiometricAuthResult._(
      success: false,
      failure: failure,
    );
  }

  @override
  String toString() {
    return 'BiometricAuthResult(success: $success, failure: $failure)';
  }
}

/// Result of a biometric settings operation
class BiometricSettingsResult {
  /// Whether the operation was successful
  final bool success;

  /// Settings failure (if operation failed)
  final AuthFailure? failure;

  const BiometricSettingsResult._({
    required this.success,
    this.failure,
  });

  /// Creates a successful biometric settings result
  factory BiometricSettingsResult.success() {
    return const BiometricSettingsResult._(success: true);
  }

  /// Creates a failed biometric settings result
  factory BiometricSettingsResult.failure(AuthFailure failure) {
    return BiometricSettingsResult._(
      success: false,
      failure: failure,
    );
  }

  @override
  String toString() {
    return 'BiometricSettingsResult(success: $success, failure: $failure)';
  }
}
