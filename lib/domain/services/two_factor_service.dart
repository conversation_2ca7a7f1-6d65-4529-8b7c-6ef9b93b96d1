import '../../core/error/auth_failures.dart';

/// Domain service interface for two-factor authentication operations
///
/// This service defines the contract for two-factor authentication functionality
/// following clean architecture principles. It serves as the boundary between
/// the domain layer and the data layer for 2FA operations.
///
/// All implementations of this service should handle:
/// - Sending 2FA codes via SMS or app-based authentication
/// - Verifying 2FA codes during authentication
/// - Managing 2FA settings and preferences
/// - Resending codes when needed
/// - Error handling and mapping to domain failures
abstract class TwoFactorService {
  /// Sends a two-factor authentication code to the user
  ///
  /// This method initiates the 2FA process by sending a verification code
  /// to the user through their configured 2FA method (SMS or app).
  ///
  /// Parameters:
  /// - [refCode]: Reference code from the initial login attempt
  /// - [method]: The 2FA method to use (SMS or app-based)
  ///
  /// Returns [TwoFactorSendResult] containing:
  /// - Success if the code was sent successfully
  /// - Failure with specific error type if sending fails
  ///
  /// Possible failure types:
  /// - TwoFactorNotEnabledFailure: 2FA is not enabled for the user
  /// - TwoFactorMethodNotSupportedFailure: Requested method is not supported
  /// - NetworkFailure: Network connectivity issues
  /// - ServerFailure: Server-side errors
  /// - TwoFactorSystemFailure: System error during code sending
  Future<TwoFactorSendResult> sendCode({
    required String refCode,
    TwoFactorMethod method = TwoFactorMethod.sms,
  });

  /// Verifies a two-factor authentication code
  ///
  /// This method verifies the 2FA code provided by the user and completes
  /// the authentication process if the code is valid.
  ///
  /// Parameters:
  /// - [refCode]: Reference code from the initial login attempt
  /// - [code]: The 2FA code provided by the user
  ///
  /// Returns [TwoFactorVerifyResult] containing:
  /// - Success with authentication tokens if verification succeeds
  /// - Failure with specific error type if verification fails
  ///
  /// Possible failure types:
  /// - InvalidTwoFactorCodeFailure: Code is incorrect or invalid format
  /// - TwoFactorCodeExpiredFailure: Code has expired
  /// - TwoFactorAttemptsExceededFailure: Too many failed attempts
  /// - NetworkFailure: Network connectivity issues
  /// - ServerFailure: Server-side errors
  Future<TwoFactorVerifyResult> verifyCode({
    required String refCode,
    required String code,
  });

  /// Resends a two-factor authentication code
  ///
  /// This method resends a 2FA code to the user, typically when the original
  /// code was not received or has expired.
  ///
  /// Parameters:
  /// - [refCode]: Reference code from the initial login attempt
  /// - [method]: The 2FA method to use for resending
  ///
  /// Returns [TwoFactorSendResult] containing:
  /// - Success if the code was resent successfully
  /// - Failure with specific error type if resending fails
  ///
  /// Business Rules:
  /// - Rate limiting may apply to prevent abuse
  /// - Previous codes may be invalidated when a new code is sent
  Future<TwoFactorSendResult> resendCode({
    required String refCode,
    TwoFactorMethod method = TwoFactorMethod.sms,
  });

  /// Checks if two-factor authentication is enabled for the current user
  ///
  /// This method checks the user's 2FA settings stored in secure storage.
  /// This represents the user's choice to use 2FA for this application.
  ///
  /// Returns true if the user has enabled 2FA for this application,
  /// false otherwise.
  ///
  /// This method should not throw exceptions and should return false
  /// for any error conditions.
  Future<bool> isTwoFactorEnabled();

  /// Sets the user's preference for two-factor authentication
  ///
  /// This method stores the user's choice to enable or disable 2FA
  /// for this application in secure storage.
  ///
  /// Parameters:
  /// - [enabled]: Whether to enable 2FA for the user
  ///
  /// Returns [TwoFactorSettingsResult] containing:
  /// - Success if the setting was saved successfully
  /// - Failure if there was an error saving the setting
  ///
  /// This operation should be atomic and either succeed completely or fail
  /// without partial state changes.
  Future<TwoFactorSettingsResult> setTwoFactorEnabled(bool enabled);

  /// Gets the list of available two-factor authentication methods
  ///
  /// This method returns the 2FA methods available for the current user
  /// based on their account configuration and device capabilities.
  ///
  /// Returns a list of [TwoFactorMethod] values representing the available
  /// 2FA methods. Returns an empty list if no methods are available.
  ///
  /// This method should not throw exceptions and should return an empty
  /// list for any error conditions.
  Future<List<TwoFactorMethod>> getTwoFactorMethods();

  /// Enables two-factor authentication for the user account
  ///
  /// This method enables 2FA on the server side and returns the necessary
  /// setup information (QR code for app-based auth, confirmation for SMS).
  ///
  /// Parameters:
  /// - [method]: The 2FA method to enable
  ///
  /// Returns [TwoFactorSetupResult] containing:
  /// - Success with setup data if 2FA was enabled successfully
  /// - Failure with specific error type if enabling fails
  ///
  /// For app-based authentication, the setup data includes:
  /// - QR code image data for scanning
  /// - Manual entry key as fallback
  ///
  /// For SMS authentication, the setup data includes:
  /// - Confirmation that SMS 2FA is enabled
  Future<TwoFactorSetupResult> enableTwoFactor({
    required TwoFactorMethod method,
  });

  /// Disables two-factor authentication for the user account
  ///
  /// This method disables 2FA on the server side and removes any stored
  /// 2FA preferences from local storage.
  ///
  /// Returns [TwoFactorSettingsResult] containing:
  /// - Success if 2FA was disabled successfully
  /// - Failure if there was an error disabling 2FA
  ///
  /// This operation should be atomic and either succeed completely or fail
  /// without partial state changes.
  Future<TwoFactorSettingsResult> disableTwoFactor();
}

/// Available two-factor authentication methods
enum TwoFactorMethod {
  /// SMS-based two-factor authentication
  sms,

  /// App-based two-factor authentication (TOTP)
  app,
}

/// Extension to provide user-friendly names for 2FA methods
extension TwoFactorMethodExtension on TwoFactorMethod {
  /// Returns a user-friendly display name for the 2FA method
  String get displayName {
    switch (this) {
      case TwoFactorMethod.sms:
        return 'SMS';
      case TwoFactorMethod.app:
        return 'Authenticator App';
    }
  }

  /// Returns a description of the 2FA method
  String get description {
    switch (this) {
      case TwoFactorMethod.sms:
        return 'Receive verification codes via SMS';
      case TwoFactorMethod.app:
        return 'Use an authenticator app to generate codes';
    }
  }
}

/// Result of a two-factor code sending operation
class TwoFactorSendResult {
  /// Whether the operation was successful
  final bool success;

  /// Send failure (if operation failed)
  final AuthFailure? failure;

  const TwoFactorSendResult._({
    required this.success,
    this.failure,
  });

  /// Creates a successful two-factor send result
  factory TwoFactorSendResult.success() {
    return const TwoFactorSendResult._(success: true);
  }

  /// Creates a failed two-factor send result
  factory TwoFactorSendResult.failure(AuthFailure failure) {
    return TwoFactorSendResult._(
      success: false,
      failure: failure,
    );
  }

  @override
  String toString() {
    return 'TwoFactorSendResult(success: $success, failure: $failure)';
  }
}

/// Result of a two-factor code verification operation
class TwoFactorVerifyResult {
  /// Whether the verification was successful
  final bool success;

  /// Access token (if verification was successful)
  final String? accessToken;

  /// Refresh token (if verification was successful)
  final String? refreshToken;

  /// Verification failure (if verification failed)
  final AuthFailure? failure;

  const TwoFactorVerifyResult._({
    required this.success,
    this.accessToken,
    this.refreshToken,
    this.failure,
  });

  /// Creates a successful two-factor verification result
  factory TwoFactorVerifyResult.success({
    required String accessToken,
    required String refreshToken,
  }) {
    return TwoFactorVerifyResult._(
      success: true,
      accessToken: accessToken,
      refreshToken: refreshToken,
    );
  }

  /// Creates a failed two-factor verification result
  factory TwoFactorVerifyResult.failure(AuthFailure failure) {
    return TwoFactorVerifyResult._(
      success: false,
      failure: failure,
    );
  }

  @override
  String toString() {
    return 'TwoFactorVerifyResult(success: $success, failure: $failure)';
  }
}

/// Result of a two-factor settings operation
class TwoFactorSettingsResult {
  /// Whether the operation was successful
  final bool success;

  /// Settings failure (if operation failed)
  final AuthFailure? failure;

  const TwoFactorSettingsResult._({
    required this.success,
    this.failure,
  });

  /// Creates a successful two-factor settings result
  factory TwoFactorSettingsResult.success() {
    return const TwoFactorSettingsResult._(success: true);
  }

  /// Creates a failed two-factor settings result
  factory TwoFactorSettingsResult.failure(AuthFailure failure) {
    return TwoFactorSettingsResult._(
      success: false,
      failure: failure,
    );
  }

  @override
  String toString() {
    return 'TwoFactorSettingsResult(success: $success, failure: $failure)';
  }
}

/// Result of a two-factor setup operation
class TwoFactorSetupResult {
  /// Whether the setup was successful
  final bool success;

  /// Setup data (if setup was successful)
  final TwoFactorSetupData? setupData;

  /// Setup failure (if setup failed)
  final AuthFailure? failure;

  const TwoFactorSetupResult._({
    required this.success,
    this.setupData,
    this.failure,
  });

  /// Creates a successful two-factor setup result
  factory TwoFactorSetupResult.success({
    required TwoFactorSetupData setupData,
  }) {
    return TwoFactorSetupResult._(
      success: true,
      setupData: setupData,
    );
  }

  /// Creates a failed two-factor setup result
  factory TwoFactorSetupResult.failure(AuthFailure failure) {
    return TwoFactorSetupResult._(
      success: false,
      failure: failure,
    );
  }

  @override
  String toString() {
    return 'TwoFactorSetupResult(success: $success, failure: $failure)';
  }
}

/// Data returned when setting up two-factor authentication
class TwoFactorSetupData {
  /// The 2FA method that was set up
  final TwoFactorMethod method;

  /// QR code image data (for app-based 2FA)
  final String? qrCodeImage;

  /// Manual entry key (for app-based 2FA)
  final String? manualEntryKey;

  /// Setup confirmation message
  final String? message;

  const TwoFactorSetupData({
    required this.method,
    this.qrCodeImage,
    this.manualEntryKey,
    this.message,
  });

  @override
  String toString() {
    return 'TwoFactorSetupData(method: $method, hasQrCode: ${qrCodeImage != null})';
  }
}
