import 'package:equatable/equatable.dart';

/// Domain entity representing an authentication session
///
/// This entity encapsulates session-related information in the authentication domain.
/// It is immutable and follows clean architecture principles by having no external dependencies.
class AuthSession extends Equatable {
  /// Unique identifier for the session
  final String id;

  /// User identifier associated with this session
  final String userId;

  /// Client identifier for the session
  final String clientId;

  /// Device information for the session
  final String deviceInfo;

  /// Location/IP address of the session
  final String location;

  /// Timestamp of the last session refresh
  final DateTime lastRefresh;

  /// Timestamp when the session was created
  final DateTime createdAt;

  /// Access token for the session
  final String? accessToken;

  /// Refresh token for the session
  final String? refreshToken;

  /// Whether the session is currently active
  final bool isActive;

  /// Session expiration timestamp
  final DateTime? expiresAt;

  const AuthSession({
    required this.id,
    required this.userId,
    required this.clientId,
    required this.deviceInfo,
    required this.location,
    required this.lastRefresh,
    required this.createdAt,
    this.accessToken,
    this.refreshToken,
    this.isActive = true,
    this.expiresAt,
  });

  /// Creates an AuthSession instance from a JSON map
  factory AuthSession.fromJson(Map<String, dynamic> json) {
    return AuthSession(
      id: json['id'] ?? json['sessionId'] ?? '',
      userId: json['userId'] ?? json['userID'] ?? '',
      clientId: json['clientID'] ?? json['clientId'] ?? '',
      deviceInfo: json['deviceInfo'] ?? '',
      location: json['location'] ?? json['ip'] ?? 'Unknown',
      lastRefresh: json['lastRefresh'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['lastRefresh'])
          : DateTime.now(),
      createdAt: json['createdAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['createdAt'])
          : DateTime.now(),
      accessToken: json['accessToken'],
      refreshToken: json['refreshToken'],
      isActive: json['isActive'] ?? true,
      expiresAt: json['expiresAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['expiresAt'])
          : null,
    );
  }

  /// Converts the AuthSession instance to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'sessionId': id, // For backward compatibility
      'userId': userId,
      'userID': userId, // For backward compatibility
      'clientID': clientId,
      'clientId': clientId, // For backward compatibility
      'deviceInfo': deviceInfo,
      'location': location,
      'lastRefresh': lastRefresh.millisecondsSinceEpoch,
      'createdAt': createdAt.millisecondsSinceEpoch,
      if (accessToken != null) 'accessToken': accessToken,
      if (refreshToken != null) 'refreshToken': refreshToken,
      'isActive': isActive,
      if (expiresAt != null) 'expiresAt': expiresAt!.millisecondsSinceEpoch,
    };
  }

  /// Creates a copy of this AuthSession with the given fields replaced with new values
  AuthSession copyWith({
    String? id,
    String? userId,
    String? clientId,
    String? deviceInfo,
    String? location,
    DateTime? lastRefresh,
    DateTime? createdAt,
    String? accessToken,
    String? refreshToken,
    bool? isActive,
    DateTime? expiresAt,
  }) {
    return AuthSession(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      clientId: clientId ?? this.clientId,
      deviceInfo: deviceInfo ?? this.deviceInfo,
      location: location ?? this.location,
      lastRefresh: lastRefresh ?? this.lastRefresh,
      createdAt: createdAt ?? this.createdAt,
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      isActive: isActive ?? this.isActive,
      expiresAt: expiresAt ?? this.expiresAt,
    );
  }

  /// Returns whether the session has expired
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// Returns whether the session is valid (active and not expired)
  bool get isValid {
    return isActive && !isExpired;
  }

  /// Returns whether the session has tokens
  bool get hasTokens {
    return accessToken != null && refreshToken != null;
  }

  /// Returns the time until session expiration
  Duration? get timeUntilExpiration {
    if (expiresAt == null) return null;
    final now = DateTime.now();
    if (now.isAfter(expiresAt!)) return Duration.zero;
    return expiresAt!.difference(now);
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        clientId,
        deviceInfo,
        location,
        lastRefresh,
        createdAt,
        accessToken,
        refreshToken,
        isActive,
        expiresAt,
      ];

  @override
  String toString() {
    return 'AuthSession(clientId: $clientId, deviceInfo: $deviceInfo, '
        'location: $location, isActive: $isActive, isValid: $isValid)';
  }
}
