import '../repositories/auth_repository.dart';

/// Use case for global logout operations
///
/// This use case handles logging out the user from all active sessions globally.
/// It invalidates all user sessions on the server and clears local authentication data.
///
/// Business Rules:
/// - Requires active network connection for server-side logout
/// - Invalidates sessions on all devices
/// - Local logout always succeeds regardless of server response
/// - If global logout fails, local logout is still performed
class GlobalLogoutUseCase {
  final AuthRepository _authRepository;

  const GlobalLogoutUseCase(this._authRepository);

  /// Executes the global logout use case for all sessions
  ///
  /// This method performs the following operations:
  /// 1. Logs out the user from all active sessions globally
  /// 2. Invalidates all user sessions on the server
  /// 3. Clears local authentication tokens and cached data
  ///
  /// This operation requires network connectivity to invalidate server sessions.
  /// If the operation fails, the user remains logged in on other devices.
  ///
  /// Business Rules:
  /// - Requires active network connection
  /// - Invalidates sessions on all devices
  /// - Local logout always succeeds regardless of server response
  ///
  /// Throws:
  /// - NetworkException for connectivity issues
  /// - ServerException for server-side errors
  ///
  /// Note: Even if server logout fails, local logout will still be performed
  /// to ensure the user is logged out from the current device.
  Future<void> call() async {
    await _authRepository.globalLogout();
  }
}
