import '../entities/auth_result.dart';
import '../repositories/auth_repository.dart';
import '../../core/error/auth_failures.dart';

/// Use case for two-factor authentication verification
///
/// This use case encapsulates the business logic for verifying two-factor
/// authentication codes. It follows the single responsibility principle by
/// handling only 2FA verification-related business rules and delegating
/// data operations to the repository.
///
/// The use case validates input parameters and coordinates with the repository
/// to complete the two-factor authentication process.
class VerifyTwoFactorUseCase {
  final AuthRepository _authRepository;

  const VerifyTwoFactorUseCase(this._authRepository);

  /// Executes the two-factor verification use case
  ///
  /// This method performs the following operations:
  /// 1. Validates the input parameters
  /// 2. Delegates to the repository for 2FA verification
  /// 3. Returns the authentication result
  ///
  /// Parameters:
  /// - [refCode]: Reference code received from the initial login attempt
  /// - [code]: Two-factor authentication code provided by the user
  ///
  /// Returns [AuthResult] containing:
  /// - Success with user data and tokens if 2FA verification succeeds
  /// - Failure result with error message if 2FA verification fails or validation fails
  ///
  /// Business Rules:
  /// - Reference code must not be empty
  /// - 2FA code must be exactly 6 digits (numeric)
  /// - 2FA code must not contain spaces or special characters
  /// - Both parameters are required and cannot be null
  ///
  /// Throws:
  /// - NetworkException for connectivity issues
  /// - ServerException for server-side errors
  /// - AuthException for authentication-specific errors
  Future<AuthResult> call(String refCode, String code) async {
    // Validate input parameters
    final validationResult = _validateTwoFactorParameters(refCode, code);
    if (validationResult != null) {
      return validationResult;
    }

    // Trim whitespace from parameters
    final trimmedRefCode = refCode.trim();
    final trimmedCode = code.trim();

    // Delegate to repository for 2FA verification
    return await _authRepository.verifyTwoFactor(trimmedRefCode, trimmedCode);
  }

  /// Validates two-factor authentication parameters according to business rules
  ///
  /// Returns null if validation passes, or an AuthResult.failure if validation fails
  AuthResult? _validateTwoFactorParameters(String refCode, String code) {
    // Check if reference code is provided
    if (refCode.trim().isEmpty) {
      return AuthResult.failure(
        failure: const ValidationFailure(
          field: 'refCode',
          message: 'reference code is required for two-factor verification',
        ),
      );
    }

    // Check if 2FA code is provided
    if (code.trim().isEmpty) {
      return AuthResult.failure(
        failure: const ValidationFailure(
          field: 'code',
          message: 'verification code is required',
        ),
      );
    }

    // Validate 2FA code format (should be 6 digits)
    final codeRegex = RegExp(r'^\d{6}$');
    if (!codeRegex.hasMatch(code.trim())) {
      return AuthResult.failure(
        failure: const ValidationFailure(
          field: 'code',
          message: 'verification code must be exactly 6 digits',
        ),
      );
    }

    // Validate reference code format (should not be empty and have reasonable length)
    final trimmedRefCode = refCode.trim();
    if (trimmedRefCode.length < 10 || trimmedRefCode.length > 100) {
      return AuthResult.failure(
        failure: const ValidationFailure(
          field: 'refCode',
          message: 'Invalid reference code format',
        ),
      );
    }

    return null; // Validation passed
  }
}

/// Use case for refreshing authentication tokens
///
/// This use case encapsulates the business logic for refreshing expired
/// or expiring authentication tokens. It provides a clean interface for
/// token management throughout the application.
class RefreshTokenUseCase {
  final AuthRepository _authRepository;

  const RefreshTokenUseCase(this._authRepository);

  /// Executes the token refresh use case
  ///
  /// This method performs the following operations:
  /// 1. Attempts to refresh the current authentication token
  /// 2. Returns the new access token if successful
  /// 3. Handles token refresh failures appropriately
  ///
  /// Returns the new access token as a String if refresh succeeds.
  ///
  /// Business Rules:
  /// - Refresh token must be valid and not expired
  /// - New access token is automatically stored securely
  /// - Refresh operation should be atomic (all or nothing)
  ///
  /// Throws:
  /// - TokenExpiredException if the refresh token has expired
  /// - NetworkException for connectivity issues
  /// - ServerException for server-side errors
  /// - AuthException if token refresh fails
  ///
  /// Note: If token refresh fails due to expired refresh token,
  /// the user should be redirected to login again.
  Future<String> call() async {
    return await _authRepository.refreshToken();
  }
}

/// Use case for validating two-factor authentication setup
///
/// This use case provides utility methods for validating 2FA-related
/// operations and checking 2FA requirements.
class TwoFactorValidationUseCase {
  /// Validates if a two-factor authentication code has the correct format
  ///
  /// This method checks if the provided code meets the format requirements
  /// for two-factor authentication codes without making any network calls.
  ///
  /// Parameters:
  /// - [code]: The 2FA code to validate
  ///
  /// Returns true if the code format is valid, false otherwise.
  ///
  /// Business Rules:
  /// - Code must be exactly 6 digits
  /// - Code must contain only numeric characters
  /// - Code cannot be empty or contain whitespace
  static bool isValidTwoFactorCode(String code) {
    if (code.trim().isEmpty) {
      return false;
    }

    final codeRegex = RegExp(r'^\d{6}$');
    return codeRegex.hasMatch(code.trim());
  }

  /// Validates if a reference code has the correct format
  ///
  /// This method checks if the provided reference code meets the format
  /// requirements without making any network calls.
  ///
  /// Parameters:
  /// - [refCode]: The reference code to validate
  ///
  /// Returns true if the reference code format is valid, false otherwise.
  ///
  /// Business Rules:
  /// - Reference code must not be empty
  /// - Reference code must have reasonable length (10-100 characters)
  /// - Reference code cannot contain only whitespace
  static bool isValidReferenceCode(String refCode) {
    final trimmedRefCode = refCode.trim();

    if (trimmedRefCode.isEmpty) {
      return false;
    }

    return trimmedRefCode.length >= 10 && trimmedRefCode.length <= 100;
  }

  /// Generates a user-friendly error message for invalid 2FA codes
  ///
  /// This method provides consistent error messaging for 2FA validation failures.
  ///
  /// Parameters:
  /// - [code]: The invalid 2FA code
  ///
  /// Returns a user-friendly error message explaining what's wrong with the code.
  static String getTwoFactorCodeErrorMessage(String code) {
    if (code.trim().isEmpty) {
      return 'Two-factor authentication code is required';
    }

    if (code.trim().length != 6) {
      return 'Two-factor authentication code must be exactly 6 digits';
    }

    final codeRegex = RegExp(r'^\d+$');
    if (!codeRegex.hasMatch(code.trim())) {
      return 'Two-factor authentication code must contain only numbers';
    }

    return 'Invalid two-factor authentication code format';
  }
}
