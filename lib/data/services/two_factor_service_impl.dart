import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../../domain/services/two_factor_service.dart';
import '../../core/error/auth_failures.dart';
import '../../view_model/custom_exception.dart';
import '../../view_model/loginPostRequests.dart';
import '../datasources/auth_remote_datasource.dart';

/// Concrete implementation of TwoFactorService using existing infrastructure
///
/// This implementation provides two-factor authentication functionality
/// by integrating with the existing remote data sources and LoginPostRequests.
/// It handles:
/// - SMS and app-based 2FA code sending and verification
/// - 2FA settings management using secure storage
/// - Error handling and mapping to domain failures
/// - Integration with existing API endpoints
class TwoFactorServiceImpl implements TwoFactorService {
  static const String _twoFactorEnabledKey = 'two_factor';

  final AuthRemoteDataSource _remoteDataSource;
  final FlutterSecureStorage _secureStorage;

  const TwoFactorServiceImpl({
    required AuthRemoteDataSource remoteDataSource,
    required FlutterSecureStorage secureStorage,
  })  : _remoteDataSource = remoteDataSource,
        _secureStorage = secureStorage;

  @override
  Future<TwoFactorSendResult> sendCode({
    required String refCode,
    TwoFactorMethod method = TwoFactorMethod.sms,
  }) async {
    try {
      // For now, we don't have a separate send code endpoint
      // The code is sent during the initial login process
      // This method could be extended in the future if needed
      return TwoFactorSendResult.success();
    } catch (e) {
      return TwoFactorSendResult.failure(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<TwoFactorVerifyResult> verifyCode({
    required String refCode,
    required String code,
  }) async {
    try {
      // Use the existing remote data source for verification
      final tokens = await _remoteDataSource.verifyTwoFactor(refCode, code);

      return TwoFactorVerifyResult.success(
        accessToken: tokens['access_token']!,
        refreshToken: tokens['refresh_token']!,
      );
    } catch (e) {
      return TwoFactorVerifyResult.failure(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<TwoFactorSendResult> resendCode({
    required String refCode,
    TwoFactorMethod method = TwoFactorMethod.sms,
  }) async {
    try {
      // For now, resending is the same as sending
      // This could be extended with specific resend logic if needed
      return await sendCode(refCode: refCode, method: method);
    } catch (e) {
      return TwoFactorSendResult.failure(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<bool> isTwoFactorEnabled() async {
    try {
      final value = await _secureStorage.read(key: _twoFactorEnabledKey);
      return value == 'true';
    } catch (e) {
      // Return false for any error conditions
      return false;
    }
  }

  @override
  Future<TwoFactorSettingsResult> setTwoFactorEnabled(bool enabled) async {
    try {
      await _secureStorage.write(
        key: _twoFactorEnabledKey,
        value: enabled.toString(),
      );
      return TwoFactorSettingsResult.success();
    } catch (e) {
      return TwoFactorSettingsResult.failure(
        TwoFactorSystemFailure(
          message: 'Failed to save two-factor authentication settings',
          context: {'error': e.toString()},
        ),
      );
    }
  }

  @override
  Future<List<TwoFactorMethod>> getTwoFactorMethods() async {
    try {
      // For now, return both SMS and app methods
      // This could be extended to check user's account capabilities
      return [TwoFactorMethod.sms, TwoFactorMethod.app];
    } catch (e) {
      // Return empty list for any error conditions
      return [];
    }
  }

  @override
  Future<TwoFactorSetupResult> enableTwoFactor({
    required TwoFactorMethod method,
  }) async {
    try {
      // Convert method to the API mode parameter
      final int mode = _methodToApiMode(method);

      // Use existing LoginPostRequests method
      final response = await LoginPostRequests.enableTwoFactorAuth(mode);

      // Create setup data based on the method and response
      final setupData = _createSetupData(method, response);

      return TwoFactorSetupResult.success(setupData: setupData);
    } catch (e) {
      return TwoFactorSetupResult.failure(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<TwoFactorSettingsResult> disableTwoFactor() async {
    try {
      // Use existing LoginPostRequests method
      await LoginPostRequests.disableTwoFactorAuth();

      return TwoFactorSettingsResult.success();
    } catch (e) {
      return TwoFactorSettingsResult.failure(_mapExceptionToFailure(e));
    }
  }

  /// Converts TwoFactorMethod to API mode parameter
  int _methodToApiMode(TwoFactorMethod method) {
    switch (method) {
      case TwoFactorMethod.sms:
        return 2; // SMS mode
      case TwoFactorMethod.app:
        return 10; // App mode (light theme)
    }
  }

  /// Creates setup data from API response
  TwoFactorSetupData _createSetupData(
    TwoFactorMethod method,
    List<String?> response,
  ) {
    switch (method) {
      case TwoFactorMethod.sms:
        return TwoFactorSetupData(
          method: method,
          message: 'SMS two-factor authentication has been enabled',
        );
      case TwoFactorMethod.app:
        return TwoFactorSetupData(
          method: method,
          qrCodeImage: response.isNotEmpty ? response[0] : null,
          manualEntryKey: response.length > 1 ? response[1] : null,
          message: 'Scan the QR code with your authenticator app',
        );
    }
  }

  /// Maps exceptions to domain failures
  AuthFailure _mapExceptionToFailure(dynamic exception) {
    if (exception is CustomException) {
      final message = exception.toString().toLowerCase();

      if (message.contains('incorrect code')) {
        return const InvalidTwoFactorCodeFailure();
      } else if (message.contains('code expired')) {
        return const TwoFactorCodeExpiredFailure();
      } else if (message.contains('error processing request')) {
        return const TwoFactorSetupFailure();
      } else if (message.contains('network') ||
          message.contains('connection')) {
        return const NetworkFailure();
      } else if (message.contains('server')) {
        return const ServerFailure();
      }

      return TwoFactorSystemFailure(
        message: exception.toString().replaceAll('CustomException: ', ''),
      );
    }

    // Handle other exception types
    if (exception.toString().toLowerCase().contains('timeout')) {
      return const TimeoutFailure();
    }

    return TwoFactorSystemFailure(
      message: 'Two-factor authentication system error',
      context: {'error': exception.toString()},
    );
  }
}
