import 'package:flutter/services.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:local_auth/local_auth.dart';
import 'package:local_auth/error_codes.dart' as auth_error;

import '../../domain/services/biometric_service.dart';
import '../../core/error/auth_failures.dart';

/// Concrete implementation of BiometricService using local_auth package
///
/// This implementation provides platform-specific biometric authentication
/// functionality for both iOS and Android platforms. It handles:
/// - Face ID and Touch ID on iOS
/// - Fingerprint and face unlock on Android
/// - Platform-specific error handling and mapping to domain failures
/// - Secure storage of biometric preferences
class BiometricServiceImpl implements BiometricService {
  static const String _biometricEnabledKey = 'biometric_enabled';

  final LocalAuthentication _localAuth;
  final FlutterSecureStorage _secureStorage;

  const BiometricServiceImpl({
    required LocalAuthentication localAuth,
    required FlutterSecureStorage secureStorage,
  })  : _localAuth = localAuth,
        _secureStorage = secureStorage;

  @override
  Future<bool> isAvailable() async {
    try {
      return await _localAuth.canCheckBiometrics;
    } catch (e) {
      // Return false for any error conditions
      return false;
    }
  }

  @override
  Future<bool> isEnrolled() async {
    try {
      // First check if biometric authentication is available
      if (!await isAvailable()) {
        return false;
      }

      // Check if any biometric methods are enrolled
      final availableBiometrics = await _localAuth.getAvailableBiometrics();
      return availableBiometrics.isNotEmpty;
    } catch (e) {
      // Return false for any error conditions
      return false;
    }
  }

  @override
  Future<BiometricAuthResult> authenticate({
    required String reason,
  }) async {
    try {
      // Check if biometric authentication is available
      if (!await isAvailable()) {
        return BiometricAuthResult.failure(
          const BiometricNotAvailableFailure(),
        );
      }

      // Check if biometric authentication is enrolled
      if (!await isEnrolled()) {
        return BiometricAuthResult.failure(
          const BiometricNotEnrolledFailure(),
        );
      }

      // Perform biometric authentication
      final authenticated = await _localAuth.authenticate(
        localizedReason: reason,
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
          useErrorDialogs: true,
        ),
      );

      if (authenticated) {
        return BiometricAuthResult.success();
      } else {
        return BiometricAuthResult.failure(
          const BiometricAuthenticationFailure(),
        );
      }
    } on PlatformException catch (e) {
      // Handle platform-specific errors and map to domain failures
      return BiometricAuthResult.failure(_mapPlatformException(e));
    } catch (e) {
      // Handle any other unexpected errors
      return BiometricAuthResult.failure(
        BiometricSystemFailure(
          context: {'error': e.toString()},
        ),
      );
    }
  }

  @override
  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      // Return empty list for any error conditions
      return [];
    }
  }

  @override
  Future<bool> isBiometricEnabled() async {
    try {
      final value = await _secureStorage.read(key: _biometricEnabledKey);
      return value == 'true';
    } catch (e) {
      // Return false for any error conditions
      return false;
    }
  }

  @override
  Future<BiometricSettingsResult> setBiometricEnabled(bool enabled) async {
    try {
      await _secureStorage.write(
        key: _biometricEnabledKey,
        value: enabled.toString(),
      );
      return BiometricSettingsResult.success();
    } catch (e) {
      return BiometricSettingsResult.failure(
        BiometricSystemFailure(
          message: 'Failed to save biometric settings',
          context: {'error': e.toString()},
        ),
      );
    }
  }

  @override
  Future<bool> canUseBiometric() async {
    try {
      return await isAvailable() && await isEnrolled();
    } catch (e) {
      // Return false for any error conditions
      return false;
    }
  }

  /// Maps platform-specific exceptions to domain failures
  ///
  /// This method converts LocalAuthentication platform exceptions
  /// to appropriate domain failure types for consistent error handling.
  AuthFailure _mapPlatformException(PlatformException exception) {
    switch (exception.code) {
      case auth_error.notAvailable:
        return const BiometricNotAvailableFailure();

      case auth_error.notEnrolled:
        return const BiometricNotEnrolledFailure();

      case auth_error.lockedOut:
        return const BiometricLockedFailure(isPermanent: false);

      case auth_error.permanentlyLockedOut:
        return const BiometricLockedFailure(isPermanent: true);

      case auth_error.biometricOnlyNotSupported:
        return const BiometricNotAvailableFailure(
          message:
              'Biometric-only authentication is not supported on this device',
        );

      // Note: deviceNotSupported may not be available in all versions
      // case auth_error.deviceNotSupported:
      //   return const BiometricNotAvailableFailure(
      //     message: 'This device does not support biometric authentication',
      //   );

      case auth_error.passcodeNotSet:
        return const BiometricNotEnrolledFailure(
          message:
              'Please set up a device passcode to use biometric authentication',
        );

      case auth_error.otherOperatingSystem:
        return const BiometricNotAvailableFailure(
          message:
              'Biometric authentication is not supported on this operating system',
        );

      default:
        // Handle user cancellation and other generic errors
        if (exception.message?.toLowerCase().contains('cancel') == true ||
            exception.code == 'UserCancel') {
          return const BiometricCancelledFailure();
        }

        return BiometricSystemFailure(
          message: exception.message ?? 'Biometric authentication system error',
          context: {
            'code': exception.code,
            'details': exception.details,
          },
        );
    }
  }
}
