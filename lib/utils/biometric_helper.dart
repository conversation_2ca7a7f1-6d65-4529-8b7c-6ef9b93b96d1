import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../changeNotifiers/NudronChangeNotifiers.dart';
import '../view_model/loginPostRequests.dart';
import '../domain/services/biometric_service.dart';
import '../core/di/injection_container.dart';

/// Legacy BiometricHelper class for backward compatibility
///
/// This class has been refactored to use the new BiometricService abstraction
/// while maintaining the same public API for existing code. New code should
/// use BiometricService directly through dependency injection.
///
/// @deprecated Use BiometricService directly through dependency injection
class BiometricHelper {
  late final BiometricService _biometricService;

  /// Constructor that initializes the BiometricService from dependency injection
  BiometricHelper() {
    _biometricService = sl<BiometricService>();
  }

  /// Constructor for testing with custom BiometricService
  BiometricHelper.withService(this._biometricService);

  // Function to check if the device supports biometric authentication
  Future<bool> isBiometricSupported() async {
    return await _biometricService.isAvailable();
  }

  // Function to check if biometric authentication is set up on the device
  Future<bool> isBiometricSetup() async {
    return await _biometricService.isEnrolled();
  }

  /// Checks if biometric authentication can be used (available and enrolled)
  Future<bool> canUseBiometric() async {
    return await _biometricService.canUseBiometric();
  }

  /// Gets the user's biometric preference setting
  Future<bool> getBiometricEnabled() async {
    return await _biometricService.isBiometricEnabled();
  }

  Future<String> getPassword() async {
    return await const FlutterSecureStorage().read(key: 'password') ?? '';
  }

  // Function to authenticate the user using biometrics
  Future<bool> authenticateWithBiometrics(
      {String reason = 'Authenticate to proceed'}) async {
    final result = await _biometricService.authenticate(reason: reason);
    return result.success;
  }

  // Function to check if biometric authentication was successful
  Future<bool> isCorrectBiometric() async {
    return await authenticateWithBiometrics(
        reason: 'Authenticate to confirm your identity');
  }

  Future<void> toggleBiometric(bool value) async {
    // Update the legacy change notifier for backward compatibility
    NudronRandomStuff.isBiometricEnabled.value = value;

    // Use the new biometric service for settings
    await _biometricService.setBiometricEnabled(value);

    // Also write to the legacy storage key for backward compatibility
    await const FlutterSecureStorage()
        .write(key: 'biometric', value: value.toString());
  }

  static Future<String?> isBiometricEnabled() async {
    String? biometric =
        await const FlutterSecureStorage().read(key: 'biometric');
    String? email = await const FlutterSecureStorage().read(key: 'email');
    if (kDebugMode) print('biometric: $biometric, email: $email');
    if (biometric != null &&
        email != null &&
        biometric == 'true' &&
        LoginPostRequests.isLoggedIn == false) {
      return email;
    }
    return null;
  }

  static checkAndStoreBiometric(String email, String password) async {
    String? emailst = await const FlutterSecureStorage().read(key: 'email');
    if ((emailst != null && emailst != email) || emailst == null) {
      await const FlutterSecureStorage().delete(key: 'biometric');
    }
    await const FlutterSecureStorage().write(key: 'email', value: email);
    await const FlutterSecureStorage().write(key: 'password', value: password);
  }
}
