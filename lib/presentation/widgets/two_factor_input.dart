import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:provider/provider.dart';
import 'package:sms_autofill/sms_autofill.dart';

import '../../theme/theme2.dart';

/// Reusable widget for two-factor authentication code input
///
/// This widget provides a consistent interface for entering 2FA codes
/// across the application. It includes:
/// - 6-digit PIN code input with proper validation
/// - Auto-focus and SMS auto-fill support
/// - Paste support for convenience
/// - Consistent theming and styling
/// - Proper error handling and validation feedback
class TwoFactorInput extends StatefulWidget {
  /// Controller for the PIN code input
  final TextEditingController controller;

  /// Callback when the code is completed (all 6 digits entered)
  final ValueChanged<String>? onCompleted;

  /// Callback when the code changes
  final ValueChanged<String>? onChanged;

  /// Whether to enable SMS auto-fill
  final bool enableAutoFill;

  /// Whether to auto-focus the input when the widget is built
  final bool autoFocus;

  /// Error text to display below the input
  final String? errorText;

  /// Whether the input is enabled
  final bool enabled;

  /// Length of the PIN code (default: 6)
  final int length;

  /// Whether to obscure the text (for security)
  final bool obscureText;

  const TwoFactorInput({
    super.key,
    required this.controller,
    this.onCompleted,
    this.onChanged,
    this.enableAutoFill = true,
    this.autoFocus = true,
    this.errorText,
    this.enabled = true,
    this.length = 6,
    this.obscureText = false,
  });

  @override
  State<TwoFactorInput> createState() => _TwoFactorInputState();
}

class _TwoFactorInputState extends State<TwoFactorInput> with CodeAutoFill {
  @override
  void initState() {
    super.initState();

    // Initialize SMS auto-fill if enabled
    if (widget.enableAutoFill) {
      _initializeAutoFill();
    }
  }

  @override
  void dispose() {
    // Clean up SMS auto-fill listener
    if (widget.enableAutoFill) {
      cancel();
    }
    super.dispose();
  }

  /// Initialize SMS auto-fill functionality
  void _initializeAutoFill() async {
    try {
      // Start listening for SMS codes
      listenForCode();

      // Print device hash for debugging (only in debug mode)
      if (mounted) {
        final deviceHash = await SmsAutoFill().getAppSignature;
        debugPrint("Two-factor input device hash: $deviceHash");
      }
    } catch (e) {
      debugPrint("Error initializing SMS auto-fill: $e");
    }
  }

  @override
  void codeUpdated() {
    if (code != null && mounted) {
      setState(() {
        widget.controller.text = code!;
      });

      // Trigger onCompleted if the code is complete
      if (code!.length == widget.length && widget.onCompleted != null) {
        widget.onCompleted!(code!);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Provider.of<ThemeNotifier>(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        PinCodeTextField(
          length: widget.length,
          controller: widget.controller,
          enabled: widget.enabled,
          autoFocus: widget.autoFocus,
          obscureText: widget.obscureText,

          // Input formatting and validation
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
          ],
          keyboardType: const TextInputType.numberWithOptions(decimal: true),

          // Styling
          textStyle: GoogleFonts.roboto(
            color: theme.currentTheme.basicAdvanceTextColor,
            fontSize: ThemeNotifier.medium.sp,
            fontWeight: FontWeight.w400,
          ),
          cursorColor: theme.currentTheme.basicAdvanceTextColor,
          backgroundColor: theme.currentTheme.bgColor,

          // Pin field styling
          pinTheme: PinTheme(
            shape: PinCodeFieldShape.box,
            borderRadius: BorderRadius.circular(8.r),
            fieldHeight: 50.h,
            fieldWidth: 45.w,
            borderWidth: 1.5,

            // Active field styling
            activeFillColor: theme.currentTheme.bgColor,
            activeColor: theme.currentTheme.basicAdvanceTextColor,
            activeBorderWidth: 2.0,

            // Selected field styling
            selectedFillColor: theme.currentTheme.bgColor,
            selectedColor: CommonColors.blue,
            selectedBorderWidth: 2.0,

            // Inactive field styling
            inactiveFillColor: theme.currentTheme.bgColor,
            inactiveColor:
                theme.currentTheme.basicAdvanceTextColor.withValues(alpha: 0.3),
            inactiveBorderWidth: 1.5,

            // Error field styling
            errorBorderColor: Colors.red,
            errorBorderWidth: 2.0,
          ),

          // Animation and behavior
          animationType: AnimationType.fade,
          animationDuration: const Duration(milliseconds: 300),
          enableActiveFill: true,
          enablePinAutofill: widget.enableAutoFill,

          // Callbacks
          onCompleted: widget.onCompleted,
          onChanged: (value) {
            if (widget.onChanged != null) {
              widget.onChanged!(value);
            }
          },

          // Paste support
          beforeTextPaste: (text) {
            // Allow pasting if the text contains only digits and is the right length
            if (text != null) {
              final cleanText = text.replaceAll(RegExp(r'[^0-9]'), '');
              return cleanText.length <= widget.length;
            }
            return false;
          },

          appContext: context,
        ),

        // Error text display
        if (widget.errorText != null) ...[
          SizedBox(height: 8.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 4.w),
            child: Text(
              widget.errorText!,
              style: GoogleFonts.roboto(
                color: Colors.red,
                fontSize: ThemeNotifier.small.sp,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ],
    );
  }
}

/// Extension to provide validation utilities for 2FA codes
extension TwoFactorInputValidation on String {
  /// Validates if the string is a valid 2FA code
  bool get isValidTwoFactorCode {
    // Check if it's exactly 6 digits
    final regex = RegExp(r'^\d{6}$');
    return regex.hasMatch(this);
  }

  /// Returns a cleaned version of the string (only digits)
  String get cleanedTwoFactorCode {
    return replaceAll(RegExp(r'[^0-9]'), '');
  }
}

/// Helper class for 2FA input validation
class TwoFactorInputValidator {
  /// Validates a 2FA code and returns an error message if invalid
  static String? validateCode(String? code) {
    if (code == null || code.isEmpty) {
      return 'Please enter the verification code';
    }

    if (code.length < 6) {
      return 'Code must be 6 digits';
    }

    if (!code.isValidTwoFactorCode) {
      return 'Please enter a valid 6-digit code';
    }

    return null; // Valid
  }

  /// Validates a 2FA code for completion (all digits entered)
  static bool isComplete(String? code) {
    return code != null && code.length == 6 && code.isValidTwoFactorCode;
  }
}
