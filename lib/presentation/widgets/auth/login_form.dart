import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../bloc/auth/auth_bloc.dart';
import '../../../bloc/auth/auth_event.dart';
import '../../../utils/misc_functions.dart';
import '../../../utils/alert_message.dart';
import '../../../views/widgets/containers/customTextField.dart';
import '../../../views/widgets/containers/PasswordController.dart';

/// Reusable login form component
///
/// This widget provides a standardized login form interface that extracts
/// common authentication form patterns. It includes:
/// - Email input field with validation
/// - Password input field with show/hide toggle
/// - Form validation and error display
/// - Integration with AuthBloc for state management
/// - Consistent theming using existing patterns
///
/// The form follows the same visual design and validation logic as the
/// original LoginPage2.dart implementation but as a reusable component.
class LoginForm extends StatefulWidget {
  /// Email text controller
  final TextEditingController? emailController;

  /// Password text controller (ObscuringTextEditingController)
  final ObscuringTextEditingController? passwordController;

  /// Callback when login is requested with valid credentials
  final Function(String email, String password)? onLoginRequested;

  /// Whether to show validation errors immediately
  final bool showValidationErrors;

  /// Whether the form is enabled for input
  final bool enabled;

  /// Custom email hint text
  final String emailHint;

  /// Custom password hint text
  final String passwordHint;

  /// Whether to auto-focus the email field
  final bool autoFocus;

  const LoginForm({
    super.key,
    this.emailController,
    this.passwordController,
    this.onLoginRequested,
    this.showValidationErrors = false,
    this.enabled = true,
    this.emailHint = 'Enter Email',
    this.passwordHint = 'Enter Password',
    this.autoFocus = false,
  });

  @override
  State<LoginForm> createState() => LoginFormState();
}

class LoginFormState extends State<LoginForm> {
  late TextEditingController _emailController;
  late ObscuringTextEditingController _passwordController;
  bool _ownControllers = false;

  String? _emailError;
  String? _passwordError;

  @override
  void initState() {
    super.initState();

    // Use provided controllers or create new ones
    if (widget.emailController != null) {
      _emailController = widget.emailController!;
    } else {
      _emailController = TextEditingController();
      _ownControllers = true;
    }

    if (widget.passwordController != null) {
      _passwordController = widget.passwordController!;
    } else {
      _passwordController = ObscuringTextEditingController();
      _ownControllers = true;
    }
  }

  @override
  void dispose() {
    // Only dispose controllers we created
    if (_ownControllers) {
      _emailController.dispose();
      _passwordController.dispose();
    }
    super.dispose();
  }

  /// Validates the email field
  String? _validateEmail(String email) {
    if (email.trim().isEmpty) {
      return 'Email address is required';
    }

    if (!MiscellaneousFunctions.isEmailValid(email.trim())) {
      return 'Please enter a valid email address';
    }

    return null;
  }

  /// Validates the password field
  String? _validatePassword(String password) {
    if (password.trim().isEmpty) {
      return 'Password is required';
    }

    return null;
  }

  /// Validates the entire form
  bool _validateForm() {
    final email = _emailController.text;
    final password = _passwordController.getText();

    final emailError = _validateEmail(email);
    final passwordError = _validatePassword(password);

    setState(() {
      _emailError = emailError;
      _passwordError = passwordError;
    });

    return emailError == null && passwordError == null;
  }

  /// Handles form submission
  void _handleSubmit() {
    if (!widget.enabled) return;

    if (_validateForm()) {
      final email = _emailController.text.trim();
      final password = _passwordController.getText();

      if (widget.onLoginRequested != null) {
        widget.onLoginRequested!(email, password);
      } else {
        // Default behavior: trigger AuthBloc login event
        context.read<AuthBloc>().add(
              AuthLoginRequested(email, password),
            );
      }
    } else {
      // Show validation error
      if (_emailError != null || _passwordError != null) {
        final errorMessage =
            _emailError ?? _passwordError ?? 'Please check your input';
        CustomAlert.showCustomScaffoldMessenger(
          context,
          errorMessage,
          AlertType.warning,
        );
      }
    }
  }

  /// Clears validation errors
  void _clearErrors() {
    if (_emailError != null || _passwordError != null) {
      setState(() {
        _emailError = null;
        _passwordError = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Email field
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 35.w),
          child: CustomTextField(
            controller: _emailController,
            iconPath: 'assets/icons/mail.svg',
            hintText: widget.emailHint,
            keyboardType: TextInputType.emailAddress,
            enableSuggestions: true,
            autocorrect: false,
            onChanged: (_) => _clearErrors(),
          ),
        ),

        // Error text for email (if validation errors should be shown)
        if (widget.showValidationErrors && _emailError != null) ...[
          SizedBox(height: 8.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 35.w),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                _emailError!,
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 12.sp,
                ),
              ),
            ),
          ),
        ],

        SizedBox(height: 22.h),

        // Password field
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 35.w),
          child: PasswordTextField(
            controller: _passwordController,
            hint: widget.passwordHint,
          ),
        ),

        // Error text for password (if validation errors should be shown)
        if (widget.showValidationErrors && _passwordError != null) ...[
          SizedBox(height: 8.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 35.w),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                _passwordError!,
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 12.sp,
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// Public method to trigger form validation and submission
  void submit() {
    _handleSubmit();
  }

  /// Public method to validate the form without submitting
  bool validate() {
    return _validateForm();
  }

  /// Public method to clear all validation errors
  void clearErrors() {
    _clearErrors();
  }

  /// Public method to get current email value
  String get email => _emailController.text.trim();

  /// Public method to get current password value
  String get password => _passwordController.getText();
}
