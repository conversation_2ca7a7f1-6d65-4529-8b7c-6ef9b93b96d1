import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../../core/di/injection_container.dart';
import '../../../domain/services/biometric_service.dart';
import '../../../theme/theme2.dart';
import '../../../utils/alert_message.dart';
import '../../../utils/pok.dart';

/// Reusable biometric authentication button component
///
/// This widget provides a standardized interface for biometric authentication
/// across the application. It includes:
/// - Biometric availability detection
/// - Platform-specific icons (fingerprint/face)
/// - Integration with BiometricService
/// - Error state handling
/// - Consistent styling with other auth components
/// - Loading states during authentication
///
/// The button automatically detects biometric availability and shows
/// appropriate UI states based on device capabilities.
class BiometricButton extends StatefulWidget {
  /// Callback when biometric authentication succeeds
  final VoidCallback? onSuccess;

  /// Callback when biometric authentication fails
  final ValueChanged<String>? onError;

  /// Custom text to display (defaults to "LOGIN WITH BIOMETRICS")
  final String? text;

  /// Custom reason text for biometric prompt
  final String reason;

  /// Whether the button is enabled
  final bool enabled;

  /// Whether to show the button even if biometrics are not available
  final bool showWhenUnavailable;

  const BiometricButton({
    super.key,
    this.onSuccess,
    this.onError,
    this.text,
    this.reason = 'Please authenticate to continue',
    this.enabled = true,
    this.showWhenUnavailable = false,
  });

  @override
  State<BiometricButton> createState() => _BiometricButtonState();
}

class _BiometricButtonState extends State<BiometricButton> {
  late final BiometricService _biometricService;
  bool _isLoading = false;
  bool _isAvailable = false;
  bool _isEnrolled = false;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _biometricService = sl<BiometricService>();
    _checkBiometricAvailability();
  }

  /// Checks if biometric authentication is available and enrolled
  Future<void> _checkBiometricAvailability() async {
    try {
      final available = await _biometricService.isAvailable();
      final enrolled = await _biometricService.isEnrolled();

      if (mounted) {
        setState(() {
          _isAvailable = available;
          _isEnrolled = enrolled;
          _isInitialized = true;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isAvailable = false;
          _isEnrolled = false;
          _isInitialized = true;
        });
      }
    }
  }

  /// Handles biometric authentication
  Future<void> _handleBiometricAuth() async {
    if (!widget.enabled || _isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _biometricService.authenticate(
        reason: widget.reason,
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (result.success) {
          widget.onSuccess?.call();
        } else {
          final errorMessage = _getErrorMessage(result.failure!);
          widget.onError?.call(errorMessage);

          // Show error using the app's standard alert system
          CustomAlert.showCustomScaffoldMessenger(
            context,
            errorMessage,
            AlertType.error,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        final errorMessage = 'Biometric authentication failed: ${e.toString()}';
        widget.onError?.call(errorMessage);

        CustomAlert.showCustomScaffoldMessenger(
          context,
          errorMessage,
          AlertType.error,
        );
      }
    }
  }

  /// Maps biometric failures to user-friendly error messages
  String _getErrorMessage(dynamic failure) {
    // This would be expanded based on the actual failure types
    // from the BiometricService implementation
    return failure.toString().contains('not available')
        ? 'Biometric authentication not available. Please enable biometrics on your device first.'
        : failure.toString().contains('not enrolled')
            ? 'No biometrics enrolled. Please set up biometric authentication in your device settings.'
            : failure.toString().contains('cancelled')
                ? 'Biometric authentication was cancelled.'
                : 'Biometric authentication failed. Please try again.';
  }

  @override
  Widget build(BuildContext context) {
    // Don't show the button if biometrics are not available and showWhenUnavailable is false
    if (_isInitialized && !_isAvailable && !widget.showWhenUnavailable) {
      return const SizedBox.shrink();
    }

    // Don't show the button if biometrics are available but not enrolled
    if (_isInitialized &&
        _isAvailable &&
        !_isEnrolled &&
        !widget.showWhenUnavailable) {
      return const SizedBox.shrink();
    }

    final theme = Provider.of<ThemeNotifier>(context);
    final isButtonEnabled =
        widget.enabled && _isAvailable && _isEnrolled && !_isLoading;

    return GestureDetector(
      onTap: isButtonEnabled ? _handleBiometricAuth : null,
      child: Container(
        child: Column(
          children: [
            _buildIcon(theme, isButtonEnabled),
            SizedBox(height: 8.h),
            _buildText(theme, isButtonEnabled),
          ],
        ),
      ),
    );
  }

  /// Builds the biometric icon with loading state
  Widget _buildIcon(ThemeNotifier theme, bool isEnabled) {
    if (_isLoading) {
      return SizedBox(
        width: 70.minSp,
        height: 70.minSp,
        child: CircularProgressIndicator(
          strokeWidth: 3.0,
          valueColor: AlwaysStoppedAnimation<Color>(
            theme.currentTheme.basicAdvanceTextColor,
          ),
        ),
      );
    }

    return Icon(
      Icons.fingerprint,
      size: 70.minSp,
      color: isEnabled
          ? theme.currentTheme.basicAdvanceTextColor
          : theme.currentTheme.basicAdvanceTextColor.withOpacity(0.5),
    );
  }

  /// Builds the biometric button text
  Widget _buildText(ThemeNotifier theme, bool isEnabled) {
    String displayText = widget.text ?? "LOGIN WITH BIOMETRICS";

    // Show different text based on availability state
    if (_isInitialized && !_isAvailable) {
      displayText = "BIOMETRICS NOT AVAILABLE";
    } else if (_isInitialized && _isAvailable && !_isEnrolled) {
      displayText = "BIOMETRICS NOT SET UP";
    } else if (_isLoading) {
      displayText = "AUTHENTICATING...";
    }

    return Text(
      displayText,
      style: GoogleFonts.robotoMono(
        decoration: isEnabled ? TextDecoration.underline : TextDecoration.none,
        decorationColor: isEnabled
            ? theme.currentTheme.basicAdvanceTextColor
            : theme.currentTheme.basicAdvanceTextColor.withOpacity(0.5),
        fontSize: ThemeNotifier.small.minSp,
        color: isEnabled
            ? theme.currentTheme.basicAdvanceTextColor
            : theme.currentTheme.basicAdvanceTextColor.withOpacity(0.5),
      ),
    );
  }
}
