import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../theme/theme2.dart';
import '../../../utils/pok.dart';

/// Reusable authentication button component
///
/// This widget provides a standardized button for authentication actions
/// following the app's design patterns. It includes:
/// - Loading state with spinner
/// - Disabled state handling
/// - Consistent styling and theming
/// - Configurable text and callbacks
/// - Support for primary and secondary button styles
///
/// The button follows the same visual design as CustomButton but adds
/// authentication-specific functionality like loading states.
class AuthButton extends StatelessWidget {
  /// Text to display on the button
  final String text;

  /// Callback when the button is pressed
  final VoidCallback? onPressed;

  /// Whether the button is in loading state
  final bool isLoading;

  /// Whether the button is disabled
  final bool isDisabled;

  /// Button style variant
  final AuthButtonStyle style;

  /// Custom width for the button
  final double? width;

  /// Whether to use dynamic width based on content
  final bool dynamicWidth;

  /// Custom font size for the button text
  final double? fontSize;

  const AuthButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.isDisabled = false,
    this.style = AuthButtonStyle.primary,
    this.width,
    this.dynamicWidth = false,
    this.fontSize,
  });

  /// Creates a primary authentication button (blue background)
  const AuthButton.primary({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.isDisabled = false,
    this.width,
    this.dynamicWidth = false,
    this.fontSize,
  }) : style = AuthButtonStyle.primary;

  /// Creates a secondary authentication button (red background)
  const AuthButton.secondary({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.isDisabled = false,
    this.width,
    this.dynamicWidth = false,
    this.fontSize,
  }) : style = AuthButtonStyle.secondary;

  @override
  Widget build(BuildContext context) {
    final bool isButtonDisabled = isDisabled || isLoading || onPressed == null;
    final Color buttonColor = _getButtonColor(isButtonDisabled);
    final chamferHeight = (44.h) / 3;

    return ClipPath(
      clipper: ChamferClipper(chamferHeight: chamferHeight),
      child: Material(
        color: buttonColor,
        child: InkWell(
          onTap: isButtonDisabled ? null : onPressed,
          splashColor: isButtonDisabled ? null : Colors.white.withOpacity(0.2),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                height: 44.h,
                width: dynamicWidth ? null : (width ?? 112.w),
                child: Center(
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: dynamicWidth ? 20.w : 0,
                    ),
                    child: _buildButtonContent(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the button content (text or loading indicator)
  Widget _buildButtonContent() {
    if (isLoading) {
      return SizedBox(
        width: 20.w,
        height: 20.h,
        child: CircularProgressIndicator(
          strokeWidth: 2.0,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      );
    }

    return Text(
      text,
      maxLines: 1,
      textAlign: TextAlign.center,
      style: GoogleFonts.robotoMono(
        fontSize: (fontSize ?? ThemeNotifier.small).minSp,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
    );
  }

  /// Gets the button color based on style and state
  Color _getButtonColor(bool isDisabled) {
    if (isDisabled) {
      return Colors.grey.shade400;
    }

    switch (style) {
      case AuthButtonStyle.primary:
        return CommonColors.blue;
      case AuthButtonStyle.secondary:
        return CommonColors.red;
    }
  }
}

/// Button style variants for authentication buttons
enum AuthButtonStyle {
  /// Primary button style (blue background)
  primary,

  /// Secondary button style (red background)
  secondary,
}

/// Custom clipper for creating chamfered corners on buttons
///
/// This clipper creates the distinctive chamfered corner design
/// used throughout the app's button components.
class ChamferClipper extends CustomClipper<Path> {
  final double chamferHeight;

  const ChamferClipper({required this.chamferHeight});

  @override
  Path getClip(Size size) {
    final path = Path()
      ..moveTo(0, 0)
      ..lineTo(size.width - chamferHeight, 0)
      ..lineTo(size.width, chamferHeight)
      ..lineTo(size.width, size.height)
      ..lineTo(chamferHeight, size.height)
      ..lineTo(0, size.height - chamferHeight)
      ..close();

    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
