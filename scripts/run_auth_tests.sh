#!/bin/bash

# Authentication Test Execution Script
# This script runs all authentication-related tests and generates coverage reports

set -e  # Exit on any error

echo "🧪 Starting Authentication Test Suite"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    print_error "Flutter is not installed or not in PATH"
    exit 1
fi

# Clean previous test artifacts
print_status "Cleaning previous test artifacts..."
rm -rf coverage/
flutter clean
flutter pub get

# Create coverage directory
mkdir -p coverage

print_status "Running authentication tests..."

# Test categories
declare -a test_files=(
    "test/data/repositories/auth_repository_impl_test.dart"
    "test/presentation/widgets/auth/login_form_test.dart"
)

# Track test results
total_tests=0
passed_tests=0
failed_tests=0

# Run each test file individually
for test_file in "${test_files[@]}"; do
    if [ -f "$test_file" ]; then
        print_status "Running: $test_file"
        
        if flutter test "$test_file" --coverage; then
            print_success "✅ $test_file - PASSED"
            ((passed_tests++))
        else
            print_error "❌ $test_file - FAILED"
            ((failed_tests++))
        fi
        ((total_tests++))
        echo ""
    else
        print_warning "Test file not found: $test_file"
    fi
done

# Generate coverage report if genhtml is available
if command -v genhtml &> /dev/null; then
    print_status "Generating HTML coverage report..."
    genhtml coverage/lcov.info -o coverage/html --ignore-errors source
    print_success "Coverage report generated at: coverage/html/index.html"
else
    print_warning "genhtml not found. Install lcov to generate HTML coverage reports."
    print_status "Coverage data available at: coverage/lcov.info"
fi

# Display test summary
echo ""
echo "📊 Test Summary"
echo "==============="
echo "Total test files: $total_tests"
echo "Passed: $passed_tests"
echo "Failed: $failed_tests"

if [ $failed_tests -eq 0 ]; then
    print_success "🎉 All authentication tests passed!"
    
    # Extract coverage percentage from lcov.info if available
    if [ -f "coverage/lcov.info" ]; then
        print_status "Extracting coverage information..."
        
        # Calculate coverage percentage
        if grep -q "^LF:" coverage/lcov.info && grep -q "^LH:" coverage/lcov.info; then
            # Sum up all LF and LH values
            total_lines=$(grep "^LF:" coverage/lcov.info | cut -d: -f2 | awk '{sum += $1} END {print sum}')
            hit_lines=$(grep "^LH:" coverage/lcov.info | cut -d: -f2 | awk '{sum += $1} END {print sum}')

            if [ "$total_lines" -gt 0 ] 2>/dev/null; then
                coverage_percent=$(echo "scale=1; $hit_lines * 100 / $total_lines" | bc 2>/dev/null || echo "0")
                echo "📈 Overall Coverage: ${coverage_percent}% ($hit_lines/$total_lines lines)"

                # Check if coverage meets minimum requirement
                if (( $(echo "$coverage_percent >= 80.0" | bc -l 2>/dev/null || echo "0") )); then
                    print_success "✅ Coverage meets minimum requirement (80%)"
                else
                    print_warning "⚠️  Coverage below minimum requirement (80%)"
                fi
            else
                print_warning "Unable to calculate coverage percentage"
            fi
        else
            print_warning "Coverage data format not recognized"
        fi
    fi
    
    exit 0
else
    print_error "❌ $failed_tests test file(s) failed"
    exit 1
fi
